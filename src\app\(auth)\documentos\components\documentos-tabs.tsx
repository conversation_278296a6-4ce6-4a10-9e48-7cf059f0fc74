'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';

import { DocumentosTable } from './documentos-table';
import { DocumentosAgrupadosTable } from './documentos-agrupados-table';
import { Documento, StatusDocumento, DocumentosAgrupados } from '@/types/documentos';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, RefreshCw, List, FileStack } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { FixAuthButton } from '@/components/FixAuthButton';
import { getDocumentos, getDocumentosAgrupados } from '@/services/documentos';

interface DocumentosTabsProps {
  onSelectDocumento: (documento: Documento) => void;
  refreshTrigger: number;
  onDocumentosLoaded?: (documentos: Documento[]) => void;
}

export function DocumentosTabs({ onSelectDocumento, refreshTrigger, onDocumentosLoaded }: DocumentosTabsProps) {
  const [activeTab, setActiveTab] = useState<string>('pendentes');
  const [viewMode, setViewMode] = useState<'individual' | 'agrupado'>('agrupado');
  const [todosDocumentos, setTodosDocumentos] = useState<Documento[]>([]);
  const [documentosAgrupados, setDocumentosAgrupados] = useState<DocumentosAgrupados[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Filtrar documentos por status localmente para visualização individual
  const documentosPendentes = todosDocumentos.filter(doc => doc.status === 'pendente');
  const documentosAprovados = todosDocumentos.filter(doc => doc.status === 'aprovado');
  const documentosRejeitados = todosDocumentos.filter(doc => doc.status === 'rejeitado');

  // Filtrar documentos agrupados por status para visualização agrupada
  const agrupadosPendentes = documentosAgrupados.map(grupo => ({
    ...grupo,
    documentos: grupo.documentos.filter(doc => doc.status === 'pendente')
  })).filter(grupo => grupo.documentos.length > 0);

  const agrupadosAprovados = documentosAgrupados.map(grupo => ({
    ...grupo,
    documentos: grupo.documentos.filter(doc => doc.status === 'aprovado')
  })).filter(grupo => grupo.documentos.length > 0);

  const agrupadosRejeitados = documentosAgrupados.map(grupo => ({
    ...grupo,
    documentos: grupo.documentos.filter(doc => doc.status === 'rejeitado')
  })).filter(grupo => grupo.documentos.length > 0);

  const loadAllDocumentos = async () => {
    setLoading(true);
    setError(null);

    try {
      // Forçar uma pequena pausa para garantir que os dados foram atualizados no banco
      await new Promise(resolve => setTimeout(resolve, 200));

      // Carregar dados individuais e agrupados simultaneamente
      const [documentos, agrupados] = await Promise.all([
        getDocumentos(undefined, true),
        getDocumentosAgrupados(undefined, true)
      ]);
      
      setTodosDocumentos(documentos);
      setDocumentosAgrupados(agrupados);
      
      // Notificar o componente pai sobre os documentos carregados
      if (onDocumentosLoaded) {
        onDocumentosLoaded(documentos);
      }
    } catch (error: any) {
      // Verificar se é erro de autenticação
      if (error.message && (
        error.message.includes('auth') ||
        error.message.includes('JWT') ||
        error.message.includes('token') ||
        error.message.includes('401')
      )) {
        setError('Erro de autenticação. Clique no botão para corrigir.');
      } else {
        setError(error.message || 'Erro ao carregar documentos');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAllDocumentos();
  }, [refreshTrigger]);

  if (error) {
    return (
      <div className="border rounded-md p-4 bg-destructive/10">
        <div className="flex flex-col items-center justify-center space-y-4 py-4">
          <AlertCircle className="h-10 w-10 text-destructive" />
          <div className="text-center">
            <h3 className="font-medium">Erro ao carregar documentos</h3>
            <p className="text-sm text-muted-foreground mb-4">{error}</p>
            <div className="flex space-x-2 justify-center">
              <Button
                variant="outline"
                size="sm"
                onClick={loadAllDocumentos}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Tentar novamente
              </Button>
              <FixAuthButton />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Toggle para alternar entre modos de visualização */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Visualização:</span>
          <div className="flex items-center border rounded-md">
            <Button
              variant={viewMode === 'individual' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('individual')}
              className="rounded-r-none"
            >
              <List className="h-4 w-4 mr-2" />
              Individual
            </Button>
            <Button
              variant={viewMode === 'agrupado' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('agrupado')}
              className="rounded-l-none border-l"
            >
              <FileStack className="h-4 w-4 mr-2" />
              Agrupado por lançamento
            </Button>
          </div>
        </div>
      </div>

      <Tabs defaultValue="pendentes" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="pendentes" className="relative">
            Inbox
            {(viewMode === 'individual' ? documentosPendentes.length : agrupadosPendentes.length) > 0 && (
              <Badge variant="destructive" className="ml-2">
                {viewMode === 'individual' 
                  ? documentosPendentes.length 
                  : agrupadosPendentes.reduce((acc, grupo) => acc + grupo.documentos.length, 0)
                }
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="aprovados">
            Aprovados
            {(viewMode === 'individual' ? documentosAprovados.length : agrupadosAprovados.length) > 0 && (
              <Badge variant="outline" className="ml-2">
                {viewMode === 'individual' 
                  ? documentosAprovados.length 
                  : agrupadosAprovados.reduce((acc, grupo) => acc + grupo.documentos.length, 0)
                }
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="rejeitados">
            Rejeitados
            {(viewMode === 'individual' ? documentosRejeitados.length : agrupadosRejeitados.length) > 0 && (
              <Badge variant="outline" className="ml-2">
                {viewMode === 'individual' 
                  ? documentosRejeitados.length 
                  : agrupadosRejeitados.reduce((acc, grupo) => acc + grupo.documentos.length, 0)
                }
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="pendentes">
          {viewMode === 'individual' ? (
            <DocumentosTable
              status="pendente"
              documentos={documentosPendentes}
              loading={loading}
              onSelectDocumento={onSelectDocumento}
              onDocumentosChange={loadAllDocumentos}
            />
          ) : (
            <DocumentosAgrupadosTable
              status="pendente"
              documentosAgrupados={agrupadosPendentes}
              loading={loading}
              onSelectDocumento={onSelectDocumento}
              onDocumentosChange={loadAllDocumentos}
              onDocumentClick={onSelectDocumento}
            />
          )}
        </TabsContent>
        
        <TabsContent value="aprovados">
          {viewMode === 'individual' ? (
            <DocumentosTable
              status="aprovado"
              documentos={documentosAprovados}
              loading={loading}
              onSelectDocumento={onSelectDocumento}
              onDocumentosChange={loadAllDocumentos}
            />
          ) : (
            <DocumentosAgrupadosTable
              status="aprovado"
              documentosAgrupados={agrupadosAprovados}
              loading={loading}
              onSelectDocumento={onSelectDocumento}
              onDocumentosChange={loadAllDocumentos}
              onDocumentClick={onSelectDocumento}
            />
          )}
        </TabsContent>
        
        <TabsContent value="rejeitados">
          {viewMode === 'individual' ? (
            <DocumentosTable
              status="rejeitado"
              documentos={documentosRejeitados}
              loading={loading}
              onSelectDocumento={onSelectDocumento}
              onDocumentosChange={loadAllDocumentos}
            />
          ) : (
            <DocumentosAgrupadosTable
              status="rejeitado"
              documentosAgrupados={agrupadosRejeitados}
              loading={loading}
              onSelectDocumento={onSelectDocumento}
              onDocumentosChange={loadAllDocumentos}
              onDocumentClick={onSelectDocumento}
            />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}