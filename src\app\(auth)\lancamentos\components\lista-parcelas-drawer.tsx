import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  ChevronLeft,
  ChevronRight,
  Edit,
  Check,
  X,
  Trash2,
  Plus,
  Calendar,
  Save,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { format, parseISO, addDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from 'sonner';
import { updateMultiplasParcelas } from '@/services/parcelas';

// Interface para parcela
interface Parcela {
  id: string;
  numero: number;
  valor: number;
  vencimento: string | null;
  status: 'pendente' | 'pago' | 'cancelado';
  data_pagamento: string | null;
  lancamento_id: string;
  created_at?: string | null;
  updated_at?: string | null;
}

interface ListaParcelasDrawerProps {
  parcelas: Parcela[];
  valorTotalLancamento: number;
  onEditParcela: (parcela: Parcela, indice: number, total: number) => void;
  onRemoveParcela: (parcelaId: string) => Promise<void>;
  onAdicionarParcela: () => Promise<void>;
}

// Função para formatar data
function formatDate(dateString?: string | null): string {
  if (!dateString) return '-';
  try {
    return format(parseISO(dateString), 'dd/MM/yy', { locale: ptBR });
  } catch (error) {

    return dateString;
  }
}

// Função para formatar valores em reais
function formatCurrency(value?: number | null): string {
  if (value === undefined || value === null) return 'R$ 0,00';
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
}

// Função para converter qualquer valor para número com precisão de 2 casas decimais
const converterParaNumero = (valor: any): number => {
  if (typeof valor === 'number') {
    // Garantir precisão de 2 casas decimais para números
    return parseFloat(valor.toFixed(2));
  }

  if (typeof valor === 'string') {
    // Remover caracteres não numéricos exceto ponto e vírgula
    // Substituir vírgula por ponto para garantir a conversão correta
    const valorLimpo = valor.replace(/[^\d,.]/g, '').replace(',', '.');

    // Converter para número e garantir 2 casas decimais
    const numero = parseFloat(valorLimpo) || 0;
    return parseFloat(numero.toFixed(2));
  }

  return 0;
};

// Função para formatar valor para exibição no input
const formatarValorParaInput = (valor: number | string | null | undefined): string => {
  if (valor === null || valor === undefined) return '';

  // Se for número, converter para string e aplicar formatação
  if (typeof valor === 'number') {
    return valor.toFixed(2).replace('.', ',');
  }

  // Se for string, verificar se já está formatada com vírgula
  if (typeof valor === 'string') {
    // Se já tiver vírgula, provavelmente está formatado
    if (valor.includes(',')) return valor;

    // Tenta converter para número e formatar
    const numero = parseFloat(valor.replace(',', '.'));
    if (!isNaN(numero)) {
      return numero.toFixed(2).replace('.', ',');
    }

    // Se não conseguir converter, tenta aplicar a máscara
    return mascaraMonetaria(valor);
  }

  return '';
};

// Função para aplicar máscara de valor monetário
const mascaraMonetaria = (valor: string): string => {
  // Se o valor estiver vazio, retornar vazio
  if (!valor.trim()) return '';

  // Remover todos os caracteres não numéricos
  let apenasNumeros = valor.replace(/\D/g, '');

  // Se não tiver números, retorna vazio
  if (!apenasNumeros) {
    return '';
  }

  // Prevenir valores muito grandes que podem causar problemas de processamento
  if (apenasNumeros.length > 12) {
    apenasNumeros = apenasNumeros.substring(0, 12);
  }

  // Converte para número dividido por 100 (para tratar os centavos)
  const numero = parseInt(apenasNumeros, 10) / 100;

  // Formata com duas casas decimais e substitui ponto por vírgula
  return numero.toFixed(2).replace('.', ',');
};

export function ListaParcelasDrawer({
  parcelas,
  valorTotalLancamento,
  onEditParcela,
  onRemoveParcela,
  onAdicionarParcela
}: ListaParcelasDrawerProps) {
  // Component is now always open
  const [aberto, setAberto] = useState(true);
  const [parcelaAtual, setParcelaAtual] = useState(0);
  const [parcalaParaExcluir, setParcelaParaExcluir] = useState<string | null>(null);
  const [removendo, setRemovendo] = useState(false);
  // Estado para as parcelas editáveis
  const [parcelasEditadas, setParcelasEditadas] = useState<Parcela[]>([]);
  // Estado para controlar quais parcelas estão sendo editadas em lote
  const [parcelasEmEdicao, setParcelasEmEdicao] = useState<Record<string, Parcela>>({});
  // Estado para controlar modo de edição em lote
  const [modoEdicaoEmLote, setModoEdicaoEmLote] = useState(false);
  // Estado para controlar salvamento em lote
  const [salvandoEmLote, setSalvandoEmLote] = useState(false);

  // Atualizar o estado interno quando as parcelas mudarem
  useEffect(() => {
    setParcelasEditadas([...parcelas]);
    // Limpar o estado de edição em lote quando as parcelas mudam
    setParcelasEmEdicao({});
    setModoEdicaoEmLote(false);
  }, [parcelas]);

  // Verificar se há parcelas para exibir
  if (!parcelas || parcelas.length === 0) {
    return null;
  }

  const parcelasPagas = parcelasEditadas.filter(p => p.status === 'pago').length;
  const totalParcelas = parcelasEditadas.length;

  const handleProximaParcela = () => {
    if (parcelaAtual < parcelasEditadas.length - 1) {
      setParcelaAtual(parcelaAtual + 1);
    }
  };

  const handleParcelaAnterior = () => {
    if (parcelaAtual > 0) {
      setParcelaAtual(parcelaAtual - 1);
    }
  };

  const confirmarExclusao = async () => {
    if (!parcalaParaExcluir) return;

    try {
      setRemovendo(true);
      await onRemoveParcela(parcalaParaExcluir);
      toast.success('Parcela removida com sucesso');
    } catch (error) {
      toast.error('Erro ao remover parcela');
    } finally {
      setRemovendo(false);
      setParcelaParaExcluir(null);
    }
  };

  const handleAdicionarParcela = async () => {
    try {
      await onAdicionarParcela();
      toast.success('Nova parcela adicionada');
    } catch (error) {
      toast.error('Erro ao adicionar parcela');
    }
  };

  // Iniciar edição de parcela individual (modo antigo)
  const iniciarEdicaoIndividual = (parcela: Parcela) => {
    // Se estiver no modo de edição em lote, adicionar/atualizar no estado de lote
    if (modoEdicaoEmLote) {
      setParcelasEmEdicao(prev => ({
        ...prev,
        [parcela.id]: { ...parcela }
      }));
    }
  };

  // Iniciar edição em lote
  const iniciarEdicaoEmLote = () => {
    setModoEdicaoEmLote(true);
    // Inicializar o estado de edição em lote com todas as parcelas
    const parcelasIniciaisEmEdicao = parcelasEditadas.reduce((acc, parcela) => {
      acc[parcela.id] = { ...parcela };
      return acc;
    }, {} as Record<string, Parcela>);
    setParcelasEmEdicao(parcelasIniciaisEmEdicao);
  };

  // Cancelar edição em lote
  const cancelarEdicaoEmLote = () => {
    setModoEdicaoEmLote(false);
    setParcelasEmEdicao({});
    // Restaurar as parcelas originais
    setParcelasEditadas([...parcelas]);
  };

  // Atualizar uma parcela no modo de edição em lote
  const atualizarParcelaEmLote = (id: string, campo: keyof Parcela, valor: any) => {
    const parcelaAtual = parcelasEmEdicao[id] || parcelasEditadas.find(p => p.id === id);

    if (!parcelaAtual) return;

    let valorProcessado = valor;

    // Processamento especial para o campo valor
    if (campo === 'valor') {
      try {
        // Aplicar máscara de moeda para o campo valor
        valorProcessado = mascaraMonetaria(valor);

        // Se o valor processado estiver vazio, usar 0,00
        if (!valorProcessado) {
          valorProcessado = '0,00';
        }
      } catch (error) {
        valorProcessado = '0,00'; // Valor padrão seguro em caso de erro
      }
    }

    // Atualizar a parcela no estado de edição em lote
    setParcelasEmEdicao(prev => ({
      ...prev,
      [id]: {
        ...prev[id] || parcelaAtual,
        [campo]: valorProcessado
      }
    }));

    // Atualizar também no estado visual
    setParcelasEditadas(prev =>
      prev.map(p =>
        p.id === id
          ? { ...p, [campo]: valorProcessado }
          : p
      )
    );
  };

  // Salvar todas as parcelas em lote
  const salvarParcelasEmLote = async () => {
    // Verificar se há parcelas para salvar
    if (Object.keys(parcelasEmEdicao).length === 0) {
      toast.info('Nenhuma alteração para salvar');
      return;
    }

    try {
      setSalvandoEmLote(true);

      // Encontrar o ID do lançamento (todas as parcelas pertencem ao mesmo lançamento)
      const lancamentoId = parcelasEditadas[0]?.lancamento_id;

      if (!lancamentoId) {
        throw new Error('ID do lançamento não encontrado');
      }

      // Função segura para converter valores
      const converterValorSeguro = (valor: any): number => {
        if (valor === null || valor === undefined) return 0;

        if (typeof valor === 'number') {
          return isNaN(valor) ? 0 : parseFloat(valor.toFixed(2));
        }

        if (typeof valor === 'string') {
          // Se for uma string vazia, retornar 0
          if (!valor.trim()) return 0;

          // Remover formatação e converter para número
          try {
            const valorLimpo = valor.replace(/[^\d,.]/g, '').replace(',', '.');
            const numero = parseFloat(valorLimpo);
            return isNaN(numero) ? 0 : parseFloat(numero.toFixed(2));
          } catch (e) {
            return 0;
          }
        }

        return 0;
      };

      // Usar o valor total do lançamento passado via prop
      const valorTotalFormatado = parseFloat(valorTotalLancamento.toFixed(2));

      // Processar as parcelas em lote com verificação adicional de valor
      const parcelasParaAtualizar = Object.values(parcelasEmEdicao).map(parcela => {
        // Converter o valor de forma segura para evitar NaN
        const valor = converterValorSeguro(parcela.valor);

        // Usar sempre um valor mínimo válido (1 centavo) se o valor for 0 ou inválido
        const valorFinal = valor <= 0 ? 0.01 : valor;

        return {
          id: parcela.id,
          valor: valorFinal,
          vencimento: parcela.vencimento || undefined,
          status: parcela.status,
          data_pagamento: parcela.data_pagamento || undefined,
        };
      });

      // Verificar a soma dos valores das parcelas atualizadas
      const somaValoresAtualizados = parcelasParaAtualizar.reduce((total, p) => total + (p.valor || 0), 0);
      const somaFormatada = parseFloat(somaValoresAtualizados.toFixed(2));

      // Tolerância para lidar com problemas de arredondamento
      const TOLERANCIA = 2.0; // Tolerância de 2 reais para considerar variações de arredondamento
      const diferencaAbsoluta = Math.abs(somaFormatada - valorTotalFormatado);

      // Verificar se está dentro da tolerância
      if (diferencaAbsoluta > TOLERANCIA && somaFormatada > valorTotalFormatado) {
        toast.error(`O valor total das parcelas (${somaFormatada.toFixed(2)}) excede o valor do lançamento (${valorTotalFormatado.toFixed(2)})`);
        setSalvandoEmLote(false);
        return;
      }

      // Verificar se há alguma parcela com valor zero
      const parcelasZeradas = parcelasParaAtualizar.filter(p => p.valor <= 0);
      if (parcelasZeradas.length > 0) {
        toast.warning('Algumas parcelas tinham valores inválidos e foram ajustadas para o valor mínimo de R$ 0,01');
      }

      // Chamar o serviço de atualização em lote
      const resultado = await updateMultiplasParcelas(lancamentoId, parcelasParaAtualizar);

      // Se recebemos informações sobre erros parciais
      if (resultado.erros && resultado.erros.length > 0) {
        toast.warning(`${resultado.erros.length} parcelas não puderam ser atualizadas`);
      } else {
        toast.success('Todas as parcelas foram atualizadas com sucesso');
      }

      // Limpar estado de edição
      setModoEdicaoEmLote(false);
      setParcelasEmEdicao({});

      // Atualizar a exibição (geralmente este onEditParcela recarrega os dados do servidor)
      // Usar a primeira parcela atualizada para recarregar todas as parcelas
      if (parcelasEditadas.length > 0) {
        // Garantir que estamos enviando a parcela com valores numéricos corretos
        const primeiraParcela = {
          ...parcelasEditadas[0],
          valor: converterParaNumero(parcelasEditadas[0].valor)
        };
        onEditParcela(primeiraParcela, 0, parcelasEditadas.length);
      }
    } catch (error: any) {
      toast.error(`Erro ao salvar parcelas: ${error.message}`);
    } finally {
      setSalvandoEmLote(false);
    }
  };

  return (
    <>
      <div className="w-full space-y-2 mt-4 border rounded-md p-4 bg-muted/30">
        <div className="flex items-center justify-between mb-2">
          <div className="text-left font-medium">
            Parcelas deste pagamento - {parcelasPagas} de {totalParcelas} pagas
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm">{totalParcelas} parcelas</span>
          </div>
        </div>
        <div className="space-y-2">
          {/* Barra de ações */}
          <div className="flex justify-between items-center pb-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleAdicionarParcela}
              className="text-xs h-8"
            >
              <Plus className="h-3 w-3 mr-1" /> Adicionar Parcela
            </Button>

            {!modoEdicaoEmLote ? (
              <Button
                variant="outline"
                size="sm"
                onClick={iniciarEdicaoEmLote}
                className="text-xs h-8"
              >
                <Edit className="h-3 w-3 mr-1" /> Editar Várias
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={cancelarEdicaoEmLote}
                  className="text-xs h-8"
                  disabled={salvandoEmLote}
                >
                  <X className="h-3 w-3 mr-1" /> Cancelar
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={salvarParcelasEmLote}
                  className="text-xs h-8"
                  disabled={salvandoEmLote}
                >
                  {salvandoEmLote ? (
                    <>Salvando...</>
                  ) : (
                    <>
                      <Save className="h-3 w-3 mr-1" /> Salvar Todas
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader className="bg-muted">
                <TableRow>
                  <TableHead className="w-[40px]">#</TableHead>
                  <TableHead>Valor</TableHead>
                  <TableHead>Vencimento</TableHead>
                  <TableHead className="w-[100px] text-center">Status</TableHead>
                  <TableHead className="w-[100px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {parcelasEditadas.map((parcela, index) => (
                  <TableRow key={parcela.id}>
                    <TableCell>{parcela.numero}</TableCell>

                    {/* Valor */}
                    <TableCell>
                      {modoEdicaoEmLote ? (
                        <Input
                          value={formatarValorParaInput(parcela.valor)}
                          onChange={(e) => atualizarParcelaEmLote(
                            parcela.id,
                            'valor',
                            e.target.value
                          )}
                          className="w-24"
                        />
                      ) : (
                        formatCurrency(parcela.valor)
                      )}
                    </TableCell>

                    {/* Vencimento */}
                    <TableCell>
                      {modoEdicaoEmLote ? (
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="w-full justify-start text-left font-normal"
                            >
                              <Calendar className="mr-2 h-4 w-4" />
                              {parcela.vencimento ? formatDate(parcela.vencimento) : "Selecionar data"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="bg-background text-foreground z-50 w-auto p-0" align="start">
                            <CalendarComponent
                              mode="single"
                              selected={parcela.vencimento ? parseISO(parcela.vencimento) : undefined}
                              onSelect={(date) => date && atualizarParcelaEmLote(
                                parcela.id,
                                'vencimento',
                                format(date, 'yyyy-MM-dd')
                              )}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      ) : (
                        formatDate(parcela.vencimento)
                      )}
                    </TableCell>

                    {/* Status */}
                    <TableCell className="text-center">
                      {modoEdicaoEmLote ? (
                        <Select
                          value={parcela.status}
                          onValueChange={(value) => atualizarParcelaEmLote(
                            parcela.id,
                            'status',
                            value as 'pendente' | 'pago' | 'cancelado'
                          )}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Status" />
                          </SelectTrigger>
                          <SelectContent className="bg-background text-foreground z-50">
                            <SelectItem value="pendente">Pendente</SelectItem>
                            <SelectItem value="pago">Pago</SelectItem>
                            <SelectItem value="cancelado">Cancelado</SelectItem>
                          </SelectContent>
                        </Select>
                      ) : (
                        <Badge
                          variant={parcela.status === 'pago' ? 'default' : 'outline'}
                          className={`min-w-[90px] justify-center ${
                            parcela.status === 'pago'
                              ? 'bg-green-600 hover:bg-green-600 text-white'
                              : parcela.status === 'cancelado'
                                ? 'bg-slate-500 hover:bg-slate-500 text-white'
                                : 'bg-yellow-500 hover:bg-yellow-500 text-yellow-950'
                          }`}
                        >
                          {parcela.status === 'pago'
                            ? 'Pago'
                            : parcela.status === 'cancelado'
                              ? 'Cancelado'
                              : 'Pendente'}
                        </Badge>
                      )}
                    </TableCell>

                    {/* Ações */}
                    <TableCell>
                      {!modoEdicaoEmLote && (
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7"
                            onClick={() => iniciarEdicaoIndividual(parcela)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7 text-destructive"
                            onClick={() => setParcelaParaExcluir(parcela.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

      {/* Diálogo de confirmação de remoção */}
      <AlertDialog open={!!parcalaParaExcluir} onOpenChange={(open) => !open && setParcelaParaExcluir(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja remover esta parcela? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={removendo}>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                confirmarExclusao();
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={removendo}
            >
              {removendo ? "Removendo..." : "Remover"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}