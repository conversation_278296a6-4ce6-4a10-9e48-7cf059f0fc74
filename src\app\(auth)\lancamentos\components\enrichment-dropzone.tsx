'use client';

import { useState, useRef, useEffect } from 'react';
import { Upload, FileText, CheckCircle2, ArrowDownToLine, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
// import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { LancamentoComParcelas } from '@/types/lancamentos';
import { Documento, DadosExtraidos } from '@/types/documentos';
import { uploadDocumento } from '@/services/documentos';
import { analisarDocumento } from '@/services/ai-analysis';
import { supabase } from '@/lib/supabase/client';
import { useContatosService } from '@/services/contatos';

interface EnrichmentDropzoneProps {
  lancamento: LancamentoComParcelas;
  onEnrichmentComplete: (changes: EnrichmentResult) => void;
  onClose: () => void;
  variant?: 'overlay' | 'inline' | 'drawer-overlay';
}

export interface EnrichmentResult {
  status?: 'pago' | 'pendente' | 'vencido';
  novosDocumentos: Documento[];
  novoContato?: any;
  dadosEnriquecidos?: {
    valor_total?: number;
    data_vencimento?: string;
    forma_pagamento?: string;
    data_pagamento?: string;
  };
}

interface ProcessingFile {
  file: File;
  progress: number;
  status: 'uploading' | 'analyzing' | 'processing' | 'completed' | 'error';
  error?: string;
  documento?: Documento;
  dadosExtraidos?: DadosExtraidos;
}

export function EnrichmentDropzone({ 
  lancamento, 
  onEnrichmentComplete, 
  onClose,
  variant = 'overlay'
}: EnrichmentDropzoneProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [files, setFiles] = useState<ProcessingFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStep, setCurrentStep] = useState<string>('');
  const dropzoneRef = useRef<HTMLDivElement>(null);
  const contatosService = useContatosService();

  // Prevenir comportamento padrão do navegador
  useEffect(() => {
    const preventDefault = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
    };

    document.addEventListener('dragover', preventDefault);
    document.addEventListener('drop', preventDefault);

    return () => {
      document.removeEventListener('dragover', preventDefault);
      document.removeEventListener('drop', preventDefault);
    };
  }, []);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Verificar se o mouse saiu realmente da área
    if (dropzoneRef.current && !dropzoneRef.current.contains(e.relatedTarget as Node)) {
      setIsDragging(false);
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const droppedFiles = Array.from(e.dataTransfer.files);
    
    // Filtrar apenas arquivos válidos
    const validFiles = droppedFiles.filter(file => {
      const isValid = 
        file.type === 'application/pdf' ||
        file.type === 'image/png' ||
        file.type === 'image/jpeg';

      if (!isValid) {
        toast.error(`Arquivo não suportado: ${file.name}. Apenas PDF, PNG e JPEG são aceitos.`);
      }

      return isValid;
    });

    if (validFiles.length === 0) return;

    const processingFiles: ProcessingFile[] = validFiles.map(file => ({
      file,
      progress: 0,
      status: 'uploading'
    }));

    setFiles(processingFiles);
    setIsProcessing(true);

    await processFiles(processingFiles);
  };

  const processFiles = async (processingFiles: ProcessingFile[]) => {
    try {
      // Obter usuário atual
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Usuário não autenticado');
      }

      const userId = session.user.id;
      const uploadedDocuments: Documento[] = [];

      // Processar cada arquivo
      for (let i = 0; i < processingFiles.length; i++) {
        const fileItem = processingFiles[i];
        
        try {
          // Fase 1: Upload
          setCurrentStep(`Enviando ${fileItem.file.name}...`);
          setFiles(prev => prev.map((f, idx) => 
            idx === i ? { ...f, progress: 25, status: 'uploading' } : f
          ));

          const documento = await uploadDocumento(fileItem.file, userId);
          uploadedDocuments.push(documento);

          // Fase 2: Análise
          setCurrentStep(`Analisando ${fileItem.file.name}...`);
          setFiles(prev => prev.map((f, idx) => 
            idx === i ? { ...f, progress: 50, status: 'analyzing', documento } : f
          ));

          const analise = await analisarDocumento(documento.id);

          // Fase 3: Processamento
          setCurrentStep(`Processando enriquecimento...`);
          setFiles(prev => prev.map((f, idx) => 
            idx === i ? { ...f, progress: 75, status: 'processing', dadosExtraidos: analise } : f
          ));

          // Simular processamento
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Concluído
          setFiles(prev => prev.map((f, idx) => 
            idx === i ? { ...f, progress: 100, status: 'completed' } : f
          ));

        } catch (error) {
          console.error(`Erro ao processar ${fileItem.file.name}:`, error);
          setFiles(prev => prev.map((f, idx) => 
            idx === i ? { 
              ...f, 
              status: 'error', 
              error: error instanceof Error ? error.message : 'Erro desconhecido' 
            } : f
          ));
        }
      }

      // Aplicar enriquecimento
      if (uploadedDocuments.length > 0) {
        await applyEnrichment(uploadedDocuments);
      }

    } catch (error) {
      console.error('Erro no processamento:', error);
      toast.error('Erro ao processar arquivos');
    } finally {
      setIsProcessing(false);
      setCurrentStep('');
    }
  };

  const applyEnrichment = async (documentos: Documento[]) => {
    try {
      setCurrentStep('Aplicando enriquecimento...');

      const enrichmentResult: EnrichmentResult = {
        novosDocumentos: documentos
      };

      // Analisar tipos de documentos para decidir o enriquecimento
      for (const doc of documentos) {
        const dados = doc.dados_extraidos;
        if (!dados) continue;

        // Caso 1: Comprovante de pagamento
        if (dados.tipo_documento === 'comprovante' && dados.status_pagamento === 'pago') {
          enrichmentResult.status = 'pago';
          enrichmentResult.dadosEnriquecidos = {
            data_pagamento: dados.data_pagamento,
            forma_pagamento: dados.forma_pagamento_realizada
          };

          toast.success('Lançamento marcado como pago com base no comprovante!');
        }

        // Caso 2: Documento com dados de contato
        if (dados.contato_detectado && contatosService) {
          try {
            // Verificar se o contato já existe pelo nome
            const contatoExistente = await contatosService.buscarContatoPorNome(dados.contato_detectado.nome_empresa || '');
            
            if (!contatoExistente && dados.contato_detectado.nome_empresa) {
              // Criar novo contato
              const novoContato = await contatosService.criarContato({
                nome_empresa: dados.contato_detectado.nome_empresa,
                nome_razao: dados.contato_detectado.nome_razao,
                nome_contato: dados.contato_detectado.nome_contato,
                email: dados.contato_detectado.email,
                telefone: dados.contato_detectado.telefone,
                cpf_cnpj: dados.contato_detectado.cpf_cnpj,
                tipo_pessoa: dados.contato_detectado.tipo_pessoa || 'juridica',
                chave_pix: dados.contato_detectado.chave_pix,
                tipo: 'fornecedor'
              });

              enrichmentResult.novoContato = novoContato;
              toast.success('Novo fornecedor criado a partir dos dados do documento!');
            }
          } catch (error) {
            console.error('Erro ao criar contato:', error);
          }
        }

        // Caso 3: Enriquecer dados financeiros
        if (dados.valor_total && dados.valor_total !== lancamento.valor_total) {
          enrichmentResult.dadosEnriquecidos = {
            ...enrichmentResult.dadosEnriquecidos,
            valor_total: dados.valor_total
          };
        }

        if (dados.data_vencimento && dados.data_vencimento !== lancamento.data_competencia) {
          enrichmentResult.dadosEnriquecidos = {
            ...enrichmentResult.dadosEnriquecidos,
            data_vencimento: dados.data_vencimento
          };
        }
      }

      // Chamar callback com os resultados
      onEnrichmentComplete(enrichmentResult);

    } catch (error) {
      console.error('Erro ao aplicar enriquecimento:', error);
      toast.error('Erro ao aplicar enriquecimento');
    }
  };

  const getFileIcon = (file: File) => {
    if (file.type === 'application/pdf') {
      return <FileText className="h-5 w-5 text-red-500" />;
    }
    return <FileText className="h-5 w-5 text-blue-500" />;
  };

  const getStatusIcon = (status: ProcessingFile['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'error':
        return <X className="h-4 w-4 text-red-500" />;
      default:
        return (
          <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        );
    }
  };

  // Renderização inline para uso dentro de drawers
  if (variant === 'inline') {
    return (
      <div className="w-full animate-in slide-in-from-top-4 duration-300">
        {/* Drop Zone Inline */}
        <div
          ref={dropzoneRef}
          className={cn(
            "border-2 border-dashed rounded-lg transition-all duration-300 overflow-hidden shadow-sm",
            isDragging 
              ? "border-blue-400 bg-blue-50/80 ring-2 ring-blue-200 shadow-lg transform scale-[1.02]" 
              : "border-gray-300 hover:border-gray-400 bg-white hover:bg-gray-50/50",
            isProcessing && "pointer-events-none opacity-75"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {files.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 px-6 text-center">
              <div className={cn(
                "mb-4 p-3 rounded-full transition-all duration-300",
                isDragging 
                  ? "bg-blue-100 text-blue-600 scale-110" 
                  : "bg-gray-100 text-gray-500"
              )}>
                <ArrowDownToLine className={cn(
                  "transition-transform duration-300",
                  isDragging ? "h-8 w-8 animate-bounce" : "h-6 w-6"
                )} />
              </div>
              
              <h3 className={cn(
                "text-base font-medium mb-2 transition-colors",
                isDragging ? "text-blue-700" : "text-gray-700"
              )}>
                {isDragging ? 'Solte os arquivos aqui' : 'Arraste documentos aqui para enriquecer'}
              </h3>
              
              <p className="text-sm text-gray-600 mb-4">
                Comprovantes de pagamento, documentos com dados de contato, etc.
              </p>
              
              <div className="text-xs text-gray-500 space-y-1">
                <p>• <strong>Comprovante:</strong> Marcará como pago automaticamente</p>
                <p>• <strong>Dados de contato:</strong> Criará novo fornecedor</p>
                <p>• <strong>Valores/datas:</strong> Atualizará informações financeiras</p>
              </div>

              {/* Botão para fechar */}
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="mt-4 text-gray-500 hover:text-gray-700"
              >
                <X className="h-4 w-4 mr-2" />
                Fechar
              </Button>
            </div>
          ) : (
            <div className="p-6">
              {/* Status geral */}
              {isProcessing && (
                <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center gap-3 text-sm text-blue-700">
                    <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin flex-shrink-0" />
                    <span className="font-medium">{currentStep || 'Processando...'}</span>
                  </div>
                </div>
              )}

              {/* Lista de arquivos */}
              <div className="space-y-3">
                {files.map((file, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 border rounded-lg bg-white">
                    {getFileIcon(file.file)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate text-gray-700">
                        {file.file.name}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <div className="flex-1 h-2 bg-gray-100 rounded-full overflow-hidden">
                          <div 
                            className={cn(
                              "h-full rounded-full transition-all duration-300",
                              file.status === 'error' ? 'bg-red-500' :
                              file.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                            )}
                            style={{ width: `${file.progress}%` }}
                          />
                        </div>
                        <span className="text-xs text-gray-500 min-w-[3rem]">
                          {file.progress}%
                        </span>
                      </div>
                      {file.error && (
                        <p className="text-xs text-red-600 mt-1">{file.error}</p>
                      )}
                    </div>
                    <div className="flex-shrink-0">
                      {getStatusIcon(file.status)}
                    </div>
                  </div>
                ))}
              </div>

              {/* Footer com botão fechar quando não está processando */}
              {!isProcessing && (
                <div className="flex justify-end mt-4 pt-4 border-t">
                  <Button variant="outline" size="sm" onClick={onClose}>
                    Fechar
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Renderização drawer-overlay - cobre todo o drawer
  if (variant === 'drawer-overlay') {
    return (
      <>
        {/* Overlay que cobre todo o drawer */}
        <div 
          className={cn(
            "absolute inset-0 z-50 flex items-center justify-center p-8",
            isDragging 
              ? "bg-blue-50/90 backdrop-blur-sm border-4 border-dashed border-blue-400 drawer-overlay-dragging" 
              : "bg-gray-50/80 backdrop-blur-sm border-4 border-dashed border-gray-300"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div 
            ref={dropzoneRef}
            className="w-full max-w-md text-center"
          >
            {files.length === 0 ? (
              <div className="flex flex-col items-center justify-center">
                <div className={cn(
                  "mb-6 p-4 rounded-full transition-all duration-300",
                  isDragging 
                    ? "bg-blue-100 text-blue-600 scale-125 upload-icon-dragging" 
                    : "bg-white text-gray-500 shadow-lg"
                )}>
                  <ArrowDownToLine className={cn(
                    "transition-transform duration-300",
                    isDragging ? "h-12 w-12 animate-bounce" : "h-10 w-10"
                  )} />
                </div>
                
                <h3 className={cn(
                  "text-xl font-medium mb-3 transition-colors",
                  isDragging ? "text-blue-700" : "text-gray-700"
                )}>
                  {isDragging ? 'Solte os arquivos aqui' : 'Arraste documentos aqui para enriquecer'}
                </h3>
                
                <p className="text-base text-gray-600 mb-6">
                  Comprovantes de pagamento, documentos com dados de contato, etc.
                </p>
                
                <div className="text-sm text-gray-500 space-y-2 mb-6">
                  <p>• <strong>Comprovante:</strong> Marcará como pago automaticamente</p>
                  <p>• <strong>Dados de contato:</strong> Criará novo fornecedor</p>
                  <p>• <strong>Valores/datas:</strong> Atualizará informações financeiras</p>
                </div>

                {/* Botão para fechar */}
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="bg-white hover:bg-gray-50"
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancelar
                </Button>
              </div>
            ) : (
              <div className="bg-white rounded-lg p-6 shadow-lg max-w-lg mx-auto">
                {/* Status geral */}
                {isProcessing && (
                  <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center gap-3 text-blue-700">
                      <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin flex-shrink-0" />
                      <span className="font-medium text-base">{currentStep || 'Processando...'}</span>
                    </div>
                  </div>
                )}

                {/* Lista de arquivos */}
                <div className="space-y-3">
                  {files.map((file, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50">
                      {getFileIcon(file.file)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate text-gray-700">
                          {file.file.name}
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
                            <div 
                              className={cn(
                                "h-full rounded-full transition-all duration-300",
                                file.status === 'error' ? 'bg-red-500' :
                                file.status === 'completed' ? 'bg-green-500' : 'bg-blue-500 upload-progress-active'
                              )}
                              style={{ width: `${file.progress}%` }}
                            />
                          </div>
                          <span className="text-xs text-gray-500 min-w-[3rem]">
                            {file.progress}%
                          </span>
                        </div>
                        {file.error && (
                          <p className="text-xs text-red-600 mt-1">{file.error}</p>
                        )}
                      </div>
                      <div className="flex-shrink-0">
                        {getStatusIcon(file.status)}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Footer com botão fechar quando não está processando */}
                {!isProcessing && (
                  <div className="flex justify-center mt-6">
                    <Button variant="outline" onClick={onClose}>
                      Fechar
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Estilos CSS específicos */}
        <style jsx>{`
          .drawer-overlay-dragging {
            animation: pulse-border-drawer 1.5s infinite;
          }

          .upload-icon-dragging {
            animation: pulse-icon-drawer 1.5s infinite ease-in-out;
          }

          .upload-progress-active {
            animation: progress-pulse-drawer 2s infinite;
          }

          @keyframes pulse-icon-drawer {
            0%, 100% {
              transform: scale(1.25);
            }
            50% {
              transform: scale(1.3);
            }
          }

          @keyframes pulse-border-drawer {
            0%, 100% {
              box-shadow: inset 0 0 0 0 rgba(59, 130, 246, 0.4);
            }
            50% {
              box-shadow: inset 0 0 0 8px rgba(59, 130, 246, 0.4);
            }
          }

          @keyframes progress-pulse-drawer {
            0%, 100% {
              opacity: 0.9;
            }
            50% {
              opacity: 0.7;
            }
          }
        `}</style>
      </>
    );
  }

  // Renderização como overlay (comportamento original)
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 pointer-events-none">
      {/* Backdrop separado que não interfere com drag and drop */}
      <div className="fixed inset-0 bg-background/80 backdrop-blur-sm -z-10"></div>
      
      <div className="bg-background border rounded-lg shadow-lg w-full max-w-2xl max-h-[80vh] overflow-hidden pointer-events-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-lg font-semibold">Enriquecer Lançamento</h2>
            <p className="text-sm text-muted-foreground">
              Arraste documentos para enriquecer o lançamento "{lancamento.descricao}"
            </p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            disabled={isProcessing}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Drop Zone */}
        <div
          ref={dropzoneRef}
          className={cn(
            "m-6 border-2 border-dashed rounded-lg transition-colors",
            isDragging ? "border-blue-500 bg-blue-50/50" : "border-muted-foreground/25",
            isProcessing && "pointer-events-none opacity-75"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {files.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
              <div className={cn(
                "mb-4 p-3 rounded-full transition-colors",
                isDragging ? "bg-blue-100 text-blue-600" : "bg-muted text-muted-foreground"
              )}>
                <ArrowDownToLine className="h-8 w-8" />
              </div>
              <h3 className="text-lg font-medium mb-2">
                {isDragging ? 'Solte os arquivos aqui' : 'Arraste documentos aqui'}
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                Comprovantes de pagamento, documentos com dados de contato, etc.
              </p>
              <div className="text-xs text-muted-foreground space-y-1">
                <p>• <strong>Comprovante:</strong> Marcará como pago automaticamente</p>
                <p>• <strong>Dados de contato:</strong> Criará novo fornecedor</p>
                <p>• <strong>Valores/datas:</strong> Atualizará informações financeiras</p>
              </div>
            </div>
          ) : (
            <div className="p-6 space-y-4">
              {/* Status geral */}
              {isProcessing && (
                <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2 text-sm text-blue-700">
                    <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                    {currentStep || 'Processando...'}
                  </div>
                </div>
              )}

              {/* Lista de arquivos */}
              <div className="space-y-3">
                {files.map((file, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                    {getFileIcon(file.file)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {file.file.name}
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="flex-1 h-2 bg-muted rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-primary transition-all duration-300"
                            style={{ width: `${file.progress}%` }}
                          />
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {file.progress}%
                        </span>
                      </div>
                      {file.error && (
                        <p className="text-xs text-red-600 mt-1">{file.error}</p>
                      )}
                    </div>
                    <div className="flex-shrink-0">
                      {getStatusIcon(file.status)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {files.length > 0 && !isProcessing && (
          <div className="flex justify-end gap-2 p-6 border-t">
            <Button variant="outline" onClick={onClose}>
              Fechar
            </Button>
          </div>
        )}
      </div>
    </div>
  );
} 