import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('As variáveis de ambiente NEXT_PUBLIC_SUPABASE_URL e NEXT_PUBLIC_SUPABASE_ANON_KEY são obrigatórias')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] &
      DefaultSchema['Views'])
  ? (DefaultSchema['Tables'] &
      DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R
    }
    ? R
    : never
  : never

type DefaultSchema = Database[Extract<keyof Database, 'public'>]

export const Constants = {
  public: {
    Enums: {},
  },
} as const

/**
 * Faz upload de um arquivo para o bucket 'anexos' do Supabase Storage.
 * Retorna a URL pública do arquivo salvo.
 */
export async function uploadAnexo(file: File, pathPrefix: string = ''): Promise<string> {
  try {

    // Importar o cliente de serviço dinamicamente para evitar problemas de importação circular
    const { createServiceClient } = await import('@/lib/supabase/service-client');
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      
      throw new Error('Erro ao criar cliente de serviço para upload');
    }

    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 8)}.${fileExt}`;
    const filePath = pathPrefix ? `${pathPrefix}/${fileName}` : fileName;

    // Usar o cliente de serviço para o upload
    const { data, error } = await serviceClient.storage.from('anexos').upload(filePath, file, {
      cacheControl: '3600',
      upsert: false,
    });

    if (error) {
      
      throw error;
    }

    // Gerar URL pública
    const { data: publicUrlData } = serviceClient.storage.from('anexos').getPublicUrl(filePath);

    return publicUrlData.publicUrl;
  } catch (error) {
    
    throw error;
  }
}

export async function getSignedUrlAnexo(path: string, expiresInSeconds = 3600): Promise<string> {
  const { data, error } = await supabase.storage
    .from('anexos')
    .createSignedUrl(path, expiresInSeconds);
  if (error) throw error;
  return data.signedUrl;
}

export function extrairPathDeUrl(url: string) {
  try {

    // Decodificar a URL para lidar com caracteres especiais
    const decodedUrl = decodeURIComponent(url);

    // Método 1: Usando regex para extrair após /anexos/
    const regexMatch = decodedUrl.match(/\/anexos\/(.+?)(\?|$)/);
    if (regexMatch) {
      return regexMatch[1];
    }

    // Método 2: Se a URL contém o caminho completo
    if (decodedUrl.includes('anexos/')) {
      const path = decodedUrl.split('anexos/')[1].split('?')[0];
              return path;
    }

    // Método 3: Se a URL é apenas o nome do arquivo
    if (decodedUrl.includes('/')) {
      const path = decodedUrl.split('/').pop() || '';
              return path;
    }

    // Fallback: retornar a URL original se nenhum método funcionar
    return decodedUrl;
  } catch (error) {
    
    return '';
  }
}