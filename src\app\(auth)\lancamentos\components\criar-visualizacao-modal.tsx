import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { criarVisualizacao } from './filtros/visualizacoes-service';
import { toast } from 'sonner';

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  filtrosAtuais: any;
  onVisualizacaoCriada: (nova: any) => void;
}

export default function CriarVisualizacaoModal({ open, onOpenChange, filtrosAtuais, onVisualizacaoCriada }: Props) {
  const [nome, setNome] = useState('');
  const [descricao, setDes<PERSON><PERSON><PERSON>] = useState('');
  const [loading, setLoading] = useState(false);
  const [erro, setErro] = useState<string | null>(null);

  async function handleCriar() {

    setErro(null);
    if (!nome.trim()) {
      setErro('O nome é obrigatório');
      return;
    }
    setLoading(true);

    try {
      // Variável para armazenar o ID do usuário
      let userId;

      try {
        // Tentar obter o usuário do cliente Supabase
        const supabase = createClientComponentClient();
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        // Se encontrou o usuário, usar seu ID
        if (user && user.id) {
          userId = user.id;

        } else {
          // Se não encontrou, tentar obter da API de sessão
          
          try {
            const sessionRes = await fetch('/api/auth/session');
            const sessionData = await sessionRes.json();

            if (sessionData?.user?.id) {
              userId = sessionData.user.id;

            } else {
              
              setErro('Você precisa estar autenticado para criar uma visualização. Por favor, faça login novamente.');
              setLoading(false);
              return;
            }
          } catch (sessionError) {
            
            setErro('Erro ao verificar sua sessão. Por favor, faça login novamente.');
            setLoading(false);
            return;
          }
        }
      } catch (authError) {
        
        setErro('Erro ao verificar sua autenticação. Por favor, faça login novamente.');
        setLoading(false);
        return;
      }

      // Garantir que temos um ID de usuário válido
      if (!userId) {
        
        setErro('Não foi possível identificar o usuário. Por favor, faça login novamente.');
        setLoading(false);
        return;
      }

      // Usar o serviço para criar a visualização
      const novaVisualizacao = await criarVisualizacao(
        nome,
        filtrosAtuais,
        userId,
        descricao,
        'lancamentos'
      );

      if (!novaVisualizacao) {

        setErro('Erro ao criar visualização. Tente novamente.');
        setLoading(false);
        return;
      }

      onVisualizacaoCriada(novaVisualizacao);
      setNome('');
      setDescricao('');
      onOpenChange(false);
      toast.success('Visualização salva com sucesso');

    } catch (e: any) {
      setErro(e.message || 'Erro inesperado');
    } finally {
      setLoading(false);
    }
  }

  function handleClose() {
    setNome('');
    setDescricao('');
    setErro(null);
    onOpenChange(false);
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[480px]">
        <DialogHeader>
          <DialogTitle>Criar nova visualização</DialogTitle>
          <DialogDescription>Salve um conjunto de filtros para reutilizar depois.</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Nome <span className="text-destructive">*</span></label>
            <Input value={nome} onChange={e => setNome(e.target.value)} placeholder="Ex: Pendentes do Sete Lagos" maxLength={60} required />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Descrição <span className="text-muted-foreground">(opcional)</span></label>
            <Textarea
              value={descricao}
              onChange={e => setDescricao(e.target.value)}
              placeholder="Descrição opcional para esta visualização"
              className="resize-none h-[80px]"
              maxLength={200}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Filtros aplicados</label>
            <div className="bg-muted p-3 rounded-md text-sm">
              <pre className="whitespace-pre-wrap break-all">
                {JSON.stringify(filtrosAtuais, null, 2)}
              </pre>
            </div>
          </div>
          {erro && <div className="text-destructive text-sm">{erro}</div>}
          <div className="flex justify-end gap-2 pt-2">
            <Button variant="ghost" onClick={handleClose} disabled={loading}>Cancelar</Button>
            <Button onClick={handleCriar} disabled={loading || !nome.trim()}>{loading ? 'Criando...' : 'Criar visualização'}</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}