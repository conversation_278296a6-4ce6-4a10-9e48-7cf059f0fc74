export interface Contato {
  id: string;
  nome_empresa: string;
  nome_razao?: string | null;
  nome_contato?: string | null;
  email?: string;
  telefone?: string;
  tipo: 'fornecedor' | 'prestador' | 'cliente';
  tipo_pessoa?: 'fisica' | 'juridica' | null;
  cpf_cnpj?: string | null;
  created_at?: string;
  updated_at?: string;
  chave_pix?: string | null;
}

export type CreateContatoDTO = Omit<Contato, 'id' | 'created_at' | 'updated_at'>;
export type UpdateContatoDTO = Partial<CreateContatoDTO>;