'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Documento } from '@/types/documentos';
import { formatCurrency } from '@/lib/utils';
import { Alert<PERSON>ir<PERSON>, Clock, FileCheck, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface DocumentosSummaryCardsProps {
  documentos?: Documento[];
  loading?: boolean;
  onRefresh?: () => void;
}

export function DocumentosSummaryCards({ documentos = [], loading = false, onRefresh }: DocumentosSummaryCardsProps) {
  // Calcular estatísticas localmente
  const calcularEstatisticas = () => {
    const hoje = new Date();
    hoje.setHours(0, 0, 0, 0);

    const dataFutura = new Date();
    dataFutura.setDate(dataFutura.getDate() + 7);
    dataFutura.setHours(23, 59, 59, 999);

    const documentosPendentes = documentos.filter(doc => doc.status === 'pendente');

    // Calcular valor total dos pendentes
    const valorTotalPendentes = documentosPendentes.reduce((total, doc) => {
      return total + (doc.valor_total || 0);
    }, 0);

    // Documentos vencidos (pendentes com data_vencimento no passado)
    const vencidos = documentosPendentes.filter(doc => {
      if (!doc.data_vencimento) return false;
      const dataVencimento = new Date(doc.data_vencimento);
      dataVencimento.setHours(23, 59, 59, 999);
      return dataVencimento < hoje;
    });

    // Documentos com vencimento nos próximos 7 dias
    const proximosVencimentos = documentosPendentes.filter(doc => {
      if (!doc.data_vencimento) return false;
      const dataVencimento = new Date(doc.data_vencimento);
      dataVencimento.setHours(0, 0, 0, 0);
      return dataVencimento >= hoje && dataVencimento <= dataFutura;
    });

    return {
      pendentes: {
        quantidade: documentosPendentes.length,
        valor_total: valorTotalPendentes
      },
      vencidos: {
        quantidade: vencidos.length
      },
      proximos_vencimentos: {
        quantidade: proximosVencimentos.length
      }
    };
  };

  const estatisticas = calcularEstatisticas();

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium h-4 bg-gray-200 rounded"></CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-3">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Documentos Pendentes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold">
                {estatisticas.pendentes.quantidade}
              </div>
              <p className="text-xs text-muted-foreground">
                Total: {formatCurrency(estatisticas.pendentes.valor_total)}
              </p>
            </div>
            <div className="p-2 bg-primary/10 rounded-full">
              <FileCheck className="h-5 w-5 text-primary" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Documentos Vencidos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold">
                {estatisticas.vencidos.quantidade}
              </div>
              <p className="text-xs text-muted-foreground">
                Necessitam atenção imediata
              </p>
            </div>
            <div className="p-2 bg-destructive/10 rounded-full">
              <AlertCircle className="h-5 w-5 text-destructive" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Próximos 7 dias</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold">
                {estatisticas.proximos_vencimentos.quantidade}
              </div>
              <p className="text-xs text-muted-foreground">
                Vencendo em breve
              </p>
            </div>
            <div className="p-2 bg-yellow-500/10 rounded-full">
              <Clock className="h-5 w-5 text-yellow-500" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}