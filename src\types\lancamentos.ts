import { Database } from "@/types/supabase"

// Tipos Enum
export type FormaPagamento = Database['public']['Enums']['forma_pagamento'];
export type TipoLancamento = Database['public']['Enums']['tipo_lancamento'];
export type StatusLancamento = Database['public']['Enums']['status_lancamento'];

// Tipos de Tabela
export type Lancamento = Database["public"]["Tables"]["lancamentos"]["Row"];
export type Parcela = Database["public"]["Tables"]["parcelas"]["Row"];
// type InsertLancamento = Database["public"]["Tables"]["lancamentos"]["Insert"]; // Manter comentado ou remover se não usado
export type UpdateLancamento = Database["public"]["Tables"]["lancamentos"]["Update"];

// Tipos Combinados/Compostos
export type LancamentoComParcelas = Lancamento & {
  parcelas: Parcela[];
  contatos: {
    nome_empresa: string | null;
    nome_razao: string | null;
    nome_contato: string | null;
    chave_pix: string | null;
  } | null;
  obras: {
    nome: string | null;
  } | null;
  categorias: {
    id: string;
    nome: string | null;
  } | null;
  documentos?: {
    id: string;
    nome: string;
    arquivo_url: string;
    arquivo_path: string;
    tipo_arquivo: string;
  }[];
};