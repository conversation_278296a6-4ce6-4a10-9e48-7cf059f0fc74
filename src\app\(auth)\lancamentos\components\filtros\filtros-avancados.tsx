'use client'

import { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { ChevronRight, Filter, X, Folder, Users, Calendar, ListChecks, Check } from 'lucide-react'
import { cn, overlayStyles } from '@/lib/utils'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { DatePicker } from '@/components/ui/date-picker'
import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { SearchableSelect } from '@/components/ui/searchable-select'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

// Tipos de filtros disponíveis
type FilterType = 'projeto' | 'fornecedor' | 'data' | 'status'

// Interface para os valores dos filtros
interface FilterValues {
  projeto?: string
  fornecedor?: string | string[]
  dataInicio?: Date
  dataFim?: Date
  status?: string[]
}

// Propriedades do componente
interface FiltrosAvancadosProps {
  projetos: string[]
  fornecedores: string[]
  statusOptions: { value: string; label: string }[]
  initialValues?: FilterValues
  onChange: (values: FilterValues) => void
  onSaveView?: () => void
  open?: boolean
  onOpenChange?: (open: boolean) => void
  isPopoverContent?: boolean
}

type FiltroAvancado = Record<string, any>;

interface Contato {
  id: string;
  nome_empresa: string | null;
  nome_razao: string | null;
  nome_contato: string | null;
}

interface Obra {
  id: string;
  nome: string | null;
}

interface Categoria {
  id: string;
  nome: string | null;
}

export default function FiltrosAvancados({
  projetos,
  fornecedores,
  statusOptions,
  initialValues = {},
  onChange,
  onSaveView,
  open: controlledOpen,
  onOpenChange,
  isPopoverContent = false
}: FiltrosAvancadosProps) {
  // Estado para controlar o popover (uncontrolled mode)
  const [uncontrolledOpen, setUncontrolledOpen] = useState(false)

  // Determinar se estamos em modo controlado ou não
  const isControlled = controlledOpen !== undefined && onOpenChange !== undefined

  // Usar o estado apropriado
  const open = isControlled ? controlledOpen : uncontrolledOpen
  const setOpen = isControlled
    ? onOpenChange
    : setUncontrolledOpen

  // Estado para os valores dos filtros
  const [filterValues, setFilterValues] = useState<FilterValues>(initialValues)

  // Estados temporários para os filtros (não aplicados imediatamente)
  const [tempFornecedorFilter, setTempFornecedorFilter] = useState<string | string[]>(
    initialValues.fornecedor || []
  )

  // Estado temporário para o filtro de data
  const [tempDataInicioFilter, setTempDataInicioFilter] = useState<Date | undefined>(
    initialValues.dataInicio
  )
  const [tempDataFimFilter, setTempDataFimFilter] = useState<Date | undefined>(
    initialValues.dataFim
  )

  // Estado temporário para o filtro de status
  const [tempStatusFilter, setTempStatusFilter] = useState<string[]>(
    Array.isArray(initialValues.status) ? initialValues.status : []
  )

  // Estado para o filtro selecionado atualmente no painel
  const [selectedFilter, setSelectedFilter] = useState<FilterType>('projeto')

  // Contador de filtros ativos
  const activeFiltersCount = Object.entries(filterValues).filter(
    ([key, value]) => {
      // Verificar se o valor não é undefined
      if (value === undefined) return false;

      // Para arrays, verificar se tem elementos
      if (Array.isArray(value)) return value.length > 0;

      // Para outros tipos, considerar como filtro ativo
      return true;
    }
  ).length

  // Atualizar os valores dos filtros quando as props mudarem
  useEffect(() => {
    setFilterValues(initialValues)
    setTempFornecedorFilter(initialValues.fornecedor || [])
    setTempDataInicioFilter(initialValues.dataInicio)
    setTempDataFimFilter(initialValues.dataFim)
    setTempStatusFilter(Array.isArray(initialValues.status) ? initialValues.status : [])
  }, [initialValues])

  // Função para atualizar um filtro específico
  const updateFilter = (type: keyof FilterValues, value: any) => {
    // Garantir que status seja sempre um array
    let processedValue = value;
    if (type === 'status') {
      if (value === undefined || value === null) {
        processedValue = [];
      } else if (!Array.isArray(value)) {
        processedValue = [value];
      }
    }

    const newValues = { ...filterValues, [type]: processedValue }
    setFilterValues(newValues)
    onChange(newValues)
  }

  // Função para limpar todos os filtros
  const resetFilters = () => {
    const newValues: FilterValues = {}
    setFilterValues(newValues)
    onChange(newValues)

    // Limpar também os estados temporários
    setTempFornecedorFilter([])
    setTempDataInicioFilter(undefined)
    setTempDataFimFilter(undefined)
    setTempStatusFilter([])
  }

  // Função para remover um filtro específico
  const removeFilter = (type: keyof FilterValues) => {
    const newValues = { ...filterValues }
    delete newValues[type]
    setFilterValues(newValues)
    onChange(newValues)

    // Resetar os filtros temporários correspondentes
    if (type === 'fornecedor') {
      setTempFornecedorFilter([])
    } else if (type === 'dataInicio') {
      setTempDataInicioFilter(undefined)
    } else if (type === 'dataFim') {
      setTempDataFimFilter(undefined)
    } else if (type === 'status') {
      setTempStatusFilter([])
    }
  }

  // Função para aplicar o filtro temporário de fornecedor
  const applyFornecedorFilter = () => {
    updateFilter('fornecedor', tempFornecedorFilter)
  }

  // Função para aplicar o filtro temporário de data
  const applyDataFilter = () => {
    updateFilter('dataInicio', tempDataInicioFilter)
    updateFilter('dataFim', tempDataFimFilter)
  }

  // Função para aplicar o filtro temporário de status
  const applyStatusFilter = () => {
    updateFilter('status', tempStatusFilter)
  }

  // Renderizar o conteúdo do filtro selecionado
  const renderFilterContent = () => {
    switch (selectedFilter) {
      case 'projeto':
        return (
          <div className="p-4">
            <h3 className="font-semibold text-base mb-2">Selecione o projeto</h3>
            <SearchableSelect
              options={[
                { value: 'todos', label: 'Todos os projetos' },
                ...projetos.map(projeto => ({ value: projeto, label: projeto }))
              ]}
              value={filterValues.projeto || 'todos'}
              onValueChange={(value) => updateFilter('projeto', value === 'todos' ? undefined : value)}
              placeholder="Todos os projetos"
              searchPlaceholder="Buscar projeto..."
              emptyMessage="Nenhum projeto encontrado"
            />
          </div>
        )

      case 'fornecedor':
        // Permitir múltipla seleção de fornecedores
        const fornecedoresSelecionados = Array.isArray(tempFornecedorFilter)
          ? tempFornecedorFilter
          : tempFornecedorFilter
          ? [tempFornecedorFilter]
          : [];
        const todosFornecedoresSelecionados = fornecedoresSelecionados.length === fornecedores.length;

        // Verificar se há alterações pendentes
        const hasPendingChanges = JSON.stringify(tempFornecedorFilter) !== JSON.stringify(filterValues.fornecedor);

        return (
          <div className="flex flex-col">
            <h3 className="text-base font-medium mb-4">Selecione o fornecedor</h3>
            <div className="relative mb-3">
              <Input
                placeholder="Buscar fornecedor"
                className="h-9 rounded-md border border-gray-300"
                value={typeof tempFornecedorFilter === 'string' ? tempFornecedorFilter : ''}
                onChange={(e) => setTempFornecedorFilter(e.target.value || '')}
              />
            </div>
            <div className="flex justify-between mb-2">
              <button
                type="button"
                className="text-blue-600 text-sm underline cursor-pointer font-normal"
                onClick={() => {
                  if (todosFornecedoresSelecionados) {
                    setTempFornecedorFilter([])
                  } else {
                    setTempFornecedorFilter(fornecedores)
                  }
                }}
              >
                {todosFornecedoresSelecionados ? 'Limpar seleção' : 'Selecionar todos'}
              </button>
              <span className="text-sm text-gray-500">
                {fornecedoresSelecionados.length} selecionado(s)
              </span>
            </div>
            <div className="mb-3">
              <ScrollArea className="h-[200px] pr-2 border border-gray-100 rounded-md p-2">
                <div className="flex flex-col gap-2">
                  {fornecedores
                    .filter(f =>
                      !tempFornecedorFilter ||
                      (typeof tempFornecedorFilter === 'string'
                        ? f.toLowerCase().includes(tempFornecedorFilter.toLowerCase())
                        : true)
                    )
                    .map(fornecedor => (
                      <div key={fornecedor} className="flex items-center gap-2 py-0.5">
                        <Checkbox
                          id={`fornecedor-${fornecedor}`}
                          checked={fornecedoresSelecionados.includes(fornecedor)}
                          onCheckedChange={(checked) => {
                            let novosSelecionados = [...fornecedoresSelecionados]
                            if (checked) {
                              novosSelecionados.push(fornecedor)
                            } else {
                              novosSelecionados = novosSelecionados.filter(f => f !== fornecedor)
                            }
                            setTempFornecedorFilter(novosSelecionados)
                          }}
                          className="scale-90"
                        />
                        <label
                          htmlFor={`fornecedor-${fornecedor}`}
                          className="text-sm font-normal cursor-pointer flex-1 leading-tight"
                        >
                          {fornecedor}
                        </label>
                      </div>
                    ))}
                </div>
              </ScrollArea>
            </div>

            {/* Botão de confirmação */}
            <div className="mt-4 flex justify-end">
              <Button
                size="sm"
                className="gap-1 bg-blue-600 hover:bg-blue-700 text-white"
                disabled={!hasPendingChanges}
                onClick={applyFornecedorFilter}
              >
                <Check className="h-4 w-4" />
                <span>Aplicar filtro</span>
              </Button>
            </div>
          </div>
        )

      case 'data':
        // Verificar se há alterações pendentes
        const hasDataPendingChanges =
          (tempDataInicioFilter !== filterValues.dataInicio) ||
          (tempDataFimFilter !== filterValues.dataFim);

        return (
          <div className="flex flex-col">
            <h3 className="text-base font-medium mb-4">Período</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium mb-1">De</p>
                <DatePicker
                  date={tempDataInicioFilter}
                  setDate={(date) => setTempDataInicioFilter(date)}
                  placeholder="DD/MM/AAAA"
                  className="w-full border border-gray-300"
                />
              </div>
              <div>
                <p className="text-sm font-medium mb-1">Até</p>
                <DatePicker
                  date={tempDataFimFilter}
                  setDate={(date) => setTempDataFimFilter(date)}
                  placeholder="DD/MM/AAAA"
                  className="w-full border border-gray-300"
                />
              </div>
            </div>

            {/* Botão de confirmação */}
            <div className="mt-4 flex justify-end">
              <Button
                size="sm"
                className="gap-1 bg-blue-600 hover:bg-blue-700 text-white"
                disabled={!hasDataPendingChanges}
                onClick={applyDataFilter}
              >
                <Check className="h-4 w-4" />
                <span>Aplicar filtro</span>
              </Button>
            </div>
          </div>
        )

      case 'status':
        // Garantir que tempStatusFilter seja sempre um array
        const statusSelecionados = Array.isArray(tempStatusFilter) ? tempStatusFilter : [];
        const todosStatusSelecionados = statusSelecionados.length === statusOptions.length;

        // Verificar se há alterações pendentes
        const hasStatusPendingChanges = JSON.stringify(tempStatusFilter) !== JSON.stringify(filterValues.status);

        return (
          <div className="flex flex-col">
            <h3 className="text-base font-medium mb-4">Status do lançamento</h3>
            <div className="flex justify-between mb-2">
              <button
                type="button"
                className="text-blue-600 text-sm underline cursor-pointer font-normal"
                onClick={() => {
                  if (todosStatusSelecionados) {
                    setTempStatusFilter([])
                  } else {
                    setTempStatusFilter(statusOptions.map(o => o.value))
                  }
                }}
              >
                {todosStatusSelecionados ? 'Limpar seleção' : 'Selecionar todos'}
              </button>
              <span className="text-sm text-gray-500">
                {statusSelecionados.length} selecionado(s)
              </span>
            </div>
            <div className="border border-gray-100 rounded-md p-3 mb-3">
              <div className="space-y-2">
                {statusOptions.map(option => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${option.value}`}
                      checked={statusSelecionados.includes(option.value)}
                      onCheckedChange={(checked) => {
                        // Garantir que estamos trabalhando com um array
                        const currentStatus = [...statusSelecionados];
                        if (checked) {
                          setTempStatusFilter([...currentStatus, option.value])
                        } else {
                          setTempStatusFilter(
                            currentStatus.filter(s => s !== option.value)
                          )
                        }
                      }}
                    />
                    <label
                      htmlFor={`status-${option.value}`}
                      className="text-sm font-normal cursor-pointer flex-1"
                    >
                      {option.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Botão de confirmação */}
            <div className="mt-4 flex justify-end">
              <Button
                size="sm"
                className="gap-1 bg-blue-600 hover:bg-blue-700 text-white"
                disabled={!hasStatusPendingChanges}
                onClick={applyStatusFilter}
              >
                <Check className="h-4 w-4" />
                <span>Aplicar filtro</span>
              </Button>
            </div>
          </div>
        )

      default:
        return <div className="p-4">Selecione um filtro</div>
    }
  }

  // Renderizar os chips de filtros ativos
  const renderActiveFilters = () => {
    const chips = []

    // Não incluir projeto aqui, pois já está sendo renderizado separadamente

    if (filterValues.fornecedor) {
      chips.push(
        <div
          key="fornecedor"
          className="flex items-center gap-1 px-3 py-1 rounded-md bg-white border border-gray-200"
        >
          <span className="text-sm">Fornecedor: {typeof filterValues.fornecedor === 'string' ? filterValues.fornecedor : filterValues.fornecedor?.join(', ')}</span>
          <X
            className="h-3.5 w-3.5 cursor-pointer text-gray-500 hover:text-gray-700"
            onClick={() => removeFilter('fornecedor')}
          />
        </div>
      )
    }

    if (filterValues.dataInicio || filterValues.dataFim) {
      const dateText = []
      if (filterValues.dataInicio) {
        dateText.push(`De: ${format(filterValues.dataInicio, 'dd/MM/yy')}`)
      }
      if (filterValues.dataFim) {
        dateText.push(`Até: ${format(filterValues.dataFim, 'dd/MM/yy')}`)
      }

      chips.push(
        <div
          key="data"
          className="flex items-center gap-1 px-3 py-1 rounded-md bg-white border border-gray-200"
        >
          <span className="text-sm">{dateText.join(' ')}</span>
          <X
            className="h-3.5 w-3.5 cursor-pointer text-gray-500 hover:text-gray-700"
            onClick={() => {
              removeFilter('dataInicio')
              removeFilter('dataFim')
            }}
          />
        </div>
      )
    }

    if (filterValues.status && Array.isArray(filterValues.status) && filterValues.status.length > 0) {
      // Mapear os valores de status para seus rótulos
      const statusLabels = statusOptions
        .filter(option => filterValues.status!.includes(option.value))
        .map(option => option.label);

      chips.push(
        <div
          key="status"
          className="flex items-center gap-1 px-3 py-1 rounded-md bg-white border border-gray-200"
        >
          <span className="text-sm">Status: {statusLabels.length} selecionado(s)</span>
          <X
            className="h-3.5 w-3.5 cursor-pointer text-gray-500 hover:text-gray-700"
            onClick={() => removeFilter('status')}
          />
        </div>
      )
    }

    return chips
  }

  // Se for conteúdo de popover, renderizar apenas o conteúdo
  if (isPopoverContent) {
    return (
          <div className="flex flex-col">
            <div className="p-4 border-b">
              <h2 className="text-lg font-medium">Filtros avançados</h2>
              <p className="text-sm text-muted-foreground">Personalize e salve visualizações para facilitar seu dia a dia</p>
            </div>
            <div className="flex w-full">
              {/* Painel lateral de categorias */}
              <div className="w-[150px] bg-[#F9FAFB] min-h-[340px] py-4 flex flex-col">
                <div
                  className={cn(
                    "flex items-center px-4 py-2 cursor-pointer group text-[14px] font-normal text-[#6B7280] transition-all",
                    selectedFilter === 'projeto'
                      ? "bg-white text-[#2563EB] font-medium"
                      : "hover:bg-white hover:text-[#2563EB]"
                  )}
                  onClick={() => setSelectedFilter('projeto')}
                >
                  <Folder className="h-5 w-5 mr-3 text-[#9CA3AF] group-hover:text-[#2563EB]" />
                  <span>Projeto</span>
                </div>
                <div
                  className={cn(
                    "flex items-center px-4 py-2 cursor-pointer group text-[14px] font-normal text-[#6B7280] transition-all",
                    selectedFilter === 'fornecedor'
                      ? "bg-white text-[#2563EB] font-medium"
                      : "hover:bg-white hover:text-[#2563EB]"
                  )}
                  onClick={() => setSelectedFilter('fornecedor')}
                >
                  <Users className="h-5 w-5 mr-3 text-[#9CA3AF] group-hover:text-[#2563EB]" />
                  <span>Fornecedor</span>
                </div>
                <div
                  className={cn(
                    "flex items-center px-4 py-2 cursor-pointer group text-[14px] font-normal text-[#6B7280] transition-all",
                    selectedFilter === 'data'
                      ? "bg-white text-[#2563EB] font-medium"
                      : "hover:bg-white hover:text-[#2563EB]"
                  )}
                  onClick={() => setSelectedFilter('data')}
                >
                  <Calendar className="h-5 w-5 mr-3 text-[#9CA3AF] group-hover:text-[#2563EB]" />
                  <span>Data</span>
                </div>
                <div
                  className={cn(
                    "flex items-center px-4 py-2 cursor-pointer group text-[14px] font-normal text-[#6B7280] transition-all",
                    selectedFilter === 'status'
                      ? "bg-white text-[#2563EB] font-medium"
                      : "hover:bg-white hover:text-[#2563EB]"
                  )}
                  onClick={() => setSelectedFilter('status')}
                >
                  <ListChecks className="h-5 w-5 mr-3 text-[#9CA3AF] group-hover:text-[#2563EB]" />
                  <span>Status</span>
                </div>
              </div>
              {/* Divider vertical */}
              <div className="w-px bg-[#E5E7EB] self-stretch" />
              {/* Painel de conteúdo do filtro */}
              <div className="flex-1 min-w-[300px] max-w-[calc(100vw-220px)] bg-white p-6 flex flex-col gap-4 justify-start min-h-[340px]">
                {selectedFilter === 'projeto' && (
                  <div className="flex flex-col">
                    <h3 className="text-base font-medium mb-4">Selecione o projeto</h3>
                    <div className="w-full">
                      <SearchableSelect
                        options={[
                          { value: 'todos', label: 'Todos os projetos' },
                          ...projetos.map(projeto => ({ value: projeto, label: projeto }))
                        ]}
                        value={filterValues.projeto || 'todos'}
                        onValueChange={(value) => updateFilter('projeto', value === 'todos' ? undefined : value)}
                        placeholder="Todos os projetos"
                        searchPlaceholder="Buscar projeto..."
                        emptyMessage="Nenhum projeto encontrado"
                      />
                    </div>
                  </div>
                )}
                {selectedFilter !== 'projeto' && renderFilterContent()}
              </div>
            </div>
            <div className="flex items-center justify-end gap-2 p-4 border-t">
              <Button
                variant="outline"
                size="sm"
                onClick={resetFilters}
                className="border border-gray-300"
              >
                Limpar
              </Button>
              {onSaveView && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setOpen(false)
                    onSaveView()
                  }}
                  className="border border-gray-300"
                >
                  Salvar visualização
                </Button>
              )}
            </div>
          </div>
    );
  }

  // Modo não controlado (com botão próprio)
  return (
    <div className="flex flex-wrap items-center gap-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={cn(
              "h-9 px-3 gap-1 border border-gray-300 rounded-md bg-white",
              activeFiltersCount > 0 && "border-gray-300"
            )}
          >
            <Filter className="h-4 w-4" />
            <span>Filtros</span>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-1 h-5 px-1.5 bg-gray-100 text-gray-800">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="rounded-md border shadow-md p-0 min-w-[500px] max-w-[600px] w-full overflow-visible bg-white z-50"
          side="bottom"
          align="start"
          sideOffset={5}
        >
          <div className="flex flex-col">
            <div className="p-4 border-b">
              <h2 className="text-lg font-medium">Filtros avançados</h2>
              <p className="text-sm text-muted-foreground">Personalize e salve visualizações para facilitar seu dia a dia</p>
            </div>
            <div className="flex w-full">
              {/* Painel lateral de categorias */}
              <div className="w-[150px] bg-[#F9FAFB] min-h-[340px] py-4 flex flex-col">
                <div
                  className={cn(
                    "flex items-center px-4 py-2 cursor-pointer group text-[14px] font-normal text-[#6B7280] transition-all",
                    selectedFilter === 'projeto'
                      ? "bg-white text-[#2563EB] font-medium"
                      : "hover:bg-white hover:text-[#2563EB]"
                  )}
                  onClick={() => setSelectedFilter('projeto')}
                >
                  <Folder className="h-5 w-5 mr-3 text-[#9CA3AF] group-hover:text-[#2563EB]" />
                  <span>Projeto</span>
                </div>
                <div
                  className={cn(
                    "flex items-center px-4 py-2 cursor-pointer group text-[14px] font-normal text-[#6B7280] transition-all",
                    selectedFilter === 'fornecedor'
                      ? "bg-white text-[#2563EB] font-medium"
                      : "hover:bg-white hover:text-[#2563EB]"
                  )}
                  onClick={() => setSelectedFilter('fornecedor')}
                >
                  <Users className="h-5 w-5 mr-3 text-[#9CA3AF] group-hover:text-[#2563EB]" />
                  <span>Fornecedor</span>
                </div>
                <div
                  className={cn(
                    "flex items-center px-4 py-2 cursor-pointer group text-[14px] font-normal text-[#6B7280] transition-all",
                    selectedFilter === 'data'
                      ? "bg-white text-[#2563EB] font-medium"
                      : "hover:bg-white hover:text-[#2563EB]"
                  )}
                  onClick={() => setSelectedFilter('data')}
                >
                  <Calendar className="h-5 w-5 mr-3 text-[#9CA3AF] group-hover:text-[#2563EB]" />
                  <span>Data</span>
                </div>
                <div
                  className={cn(
                    "flex items-center px-4 py-2 cursor-pointer group text-[14px] font-normal text-[#6B7280] transition-all",
                    selectedFilter === 'status'
                      ? "bg-white text-[#2563EB] font-medium"
                      : "hover:bg-white hover:text-[#2563EB]"
                  )}
                  onClick={() => setSelectedFilter('status')}
                >
                  <ListChecks className="h-5 w-5 mr-3 text-[#9CA3AF] group-hover:text-[#2563EB]" />
                  <span>Status</span>
                </div>
              </div>
              {/* Divider vertical */}
              <div className="w-px bg-[#E5E7EB] self-stretch" />
              {/* Painel de conteúdo do filtro */}
              <div className="flex-1 min-w-[300px] max-w-[calc(100vw-220px)] bg-white p-6 flex flex-col gap-4 justify-start min-h-[340px]">
                {selectedFilter === 'projeto' && (
                  <div className="flex flex-col">
                    <h3 className="text-base font-medium mb-4">Selecione o projeto</h3>
                    <div className="w-full">
                      <SearchableSelect
                        options={[
                          { value: 'todos', label: 'Todos os projetos' },
                          ...projetos.map(projeto => ({ value: projeto, label: projeto }))
                        ]}
                        value={filterValues.projeto || 'todos'}
                        onValueChange={(value) => updateFilter('projeto', value === 'todos' ? undefined : value)}
                        placeholder="Todos os projetos"
                        searchPlaceholder="Buscar projeto..."
                        emptyMessage="Nenhum projeto encontrado"
                      />
                    </div>
                  </div>
                )}
                {selectedFilter !== 'projeto' && renderFilterContent()}
              </div>
            </div>
            <div className="flex items-center justify-end gap-2 p-4 border-t">
              <Button
                variant="outline"
                size="sm"
                onClick={resetFilters}
                className="border border-gray-300"
              >
                Limpar
              </Button>
              {onSaveView && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setOpen(false)
                    onSaveView()
                  }}
                  className="border border-gray-300"
                >
                  Salvar visualização
                </Button>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}