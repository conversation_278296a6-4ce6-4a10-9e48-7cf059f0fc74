# Remoção do Status "Vencido" de Parcelas

## Descrição da Alteração

Conforme solicitado, removemos o status "vencido" como um estado real das parcelas no sistema. Agora, o status "vencido" é apenas um indicador visual que aparece quando uma parcela com status "pendente" possui uma data de vencimento que já passou.

## Alterações Realizadas

### Banco de Dados
1. Atualização da definição da tabela `parcelas`, removendo "vencido" da constraint de status:
   ```sql
   ALTER TABLE parcelas 
       DROP CONSTRAINT parcelas_status_check,
       ADD CONSTRAINT parcelas_status_check CHECK (status IN ('pendente', 'pago', 'cancelado'));
   ```

2. Remoção do trigger `update_parcela_status` que atualizava automaticamente o status das parcelas para "vencido":
   ```sql
   DROP TRIGGER IF EXISTS update_parcela_status ON parcelas;
   DROP FUNCTION IF EXISTS update_parcela_status();
   ```

3. Atualização das parcelas existentes:
   ```sql
   UPDATE parcelas SET status = 'pendente' WHERE status = 'vencido';
   ```

### Tipos e Constantes
1. Atualizado o arquivo `src/types/supabase.ts` para remover "vencido" da enumeração `status_parcela`.

### Componentes
1. Atualizado o componente `drawer-parcela.tsx` para exibir parcelas atrasadas como "Pendente (Atrasada)" visualmente.
2. Atualizado o componente `drawer-pagamentos-pendentes.tsx` para buscar apenas parcelas com status "pendente".
3. Atualizado os componentes `lista-parcelas.tsx` e `lista-lancamentos.tsx` para tratar a exibição visual de parcelas atrasadas sem depender do status "vencido".

## Comportamento Esperado

- Parcelas que estão vencidas (data de vencimento no passado) e não foram pagas continuam com o status "pendente"
- Visualmente, essas parcelas são identificadas com um badge vermelho "Atrasado" ou "Pendente (Atrasada)"
- A funcionalidade de agrupar e pagar parcelas pendentes continua funcionando normalmente
- Os filtros por status mostram corretamente parcelas atrasadas

## Impacto no Código

Esta mudança simplifica o modelo de dados e a lógica de negócios, mantendo todos os estados de parcela no lado do cliente (interface) ao invés de no banco de dados. Isso facilita a manutenção futura e reduz a complexidade da aplicação. 