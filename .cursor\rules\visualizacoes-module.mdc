---
description: 
globs: 
alwaysApply: false
---
# <PERSON><PERSON>dulo de Visualizações

## Visão Geral
O módulo de visualizações permite aos usuários salvar conjuntos de filtros para reuso posterior, agilizando consultas frequentes.

## Componentes Principais

### Frontend
- [VisualizacoesDropdown](mdc:src/app/(auth)/lancamentos/components/visualizacoes-dropdown.tsx): Dropdown para selecionar/criar visualizações
- [CriarVisualizacaoModal](mdc:src/app/(auth)/lancamentos/components/criar-visualizacao-modal.tsx): Modal para criar novas visualizações

### Backend
- [API de Visualizações](mdc:src/app/api/lancamentos/visualizacoes/route.ts): Endpoints de API para listar e criar visualizações

## Banco de Dados
A tabela `visualizacoes` no esquema `public` armazena as visualizações dos usuários com a seguinte estrutura:
- `id`: UUID (chave primária)
- `user_id`: UUID do usuário proprietário
- `contexto`: Contexto de aplicação ('lancamentos', etc.)
- `nome`: Nome da visualização
- `descricao`: Descrição opcional
- `filtros_json`: Objeto JSON com os filtros salvos
- `created_at`: Data de criação
- `updated_at`: Data de atualização

Importante: O RLS (Row Level Security) foi desabilitado para esta tabela para evitar problemas de autenticação entre cliente e servidor.

## Fluxo de Funcionamento
1. Frontend obtém o ID do usuário atual via `supabase.auth.getSession()`
2. O ID é passado nas requisições à API para identificação
3. Ao criar visualizações, o ID do usuário é armazenado junto com os filtros
4. Ao carregar a página, as visualizações do usuário são recuperadas e exibidas
5. Ao selecionar uma visualização, os filtros são aplicados automaticamente

## Observações Importantes
- A última visualização selecionada é persistida no localStorage
- O componente detecta quando um usuário está autenticado e recupera seu ID
- A API suporta autenticação tanto por sessão quanto por parâmetro `userId`

