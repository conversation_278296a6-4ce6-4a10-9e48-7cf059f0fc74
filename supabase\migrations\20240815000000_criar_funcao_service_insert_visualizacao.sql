-- Função para inserir visualizações contornando o RLS
CREATE OR REPLACE FUNCTION public.service_insert_visualizacao(
  p_user_id UUID,
  p_contexto TEXT,
  p_nome TEXT,
  p_descricao TEXT,
  p_filtros_json JSONB
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_id UUID;
BEGIN
  -- Gerar um novo UUID para a visualização
  v_id := gen_random_uuid();
  
  -- Inserir a visualização com o UUID gerado
  INSERT INTO public.visualizacoes (
    id,
    user_id,
    contexto,
    nome,
    descricao,
    filtros_json,
    created_at,
    updated_at
  ) VALUES (
    v_id,
    p_user_id,
    p_contexto,
    p_nome,
    p_descricao,
    p_filtros_json,
    NOW(),
    NOW()
  );
  
  -- Retornar o ID da visualização criada
  RETURN v_id;
END;
$$;

-- Comentário para a função
COMMENT ON FUNCTION public.service_insert_visualizacao IS 'Função para inserir visualizações contornando o RLS';
