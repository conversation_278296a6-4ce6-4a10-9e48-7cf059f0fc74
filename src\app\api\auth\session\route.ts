import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';

export const dynamic = 'force-dynamic';

// GET /api/auth/session - Retorna informações da sessão atual
export async function GET(request: NextRequest) {
  try {
    const supabase = await getSupabaseRouteClient();

    // Obter a sessão atual
    const { data: { session }, error } = await supabase.auth.getSession();

    if (error) {
      
      return NextResponse.json({ error: 'Erro ao obter sessão: ' + error.message }, { status: 500 });
    }

    if (!session) {
      return NextResponse.json({ user: null });
    }

    // Retornar informações do usuário
    return NextResponse.json({
      user: {
        id: session.user.id,
        email: session.user.email,
        role: session.user.role
      }
    });
  } catch (error: any) {
    
    return NextResponse.json({ error: 'Erro interno do servidor: ' + (error?.message || 'desconhecido') }, { status: 500 });
  }
}