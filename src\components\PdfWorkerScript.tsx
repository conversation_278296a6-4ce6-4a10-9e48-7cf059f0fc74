'use client';

import Script from 'next/script';
import { useEffect } from 'react';

export function PdfWorkerScript() {
  useEffect(() => {
    // Definir a variável global para o worker do PDF.js
    if (typeof window !== 'undefined') {
      // @ts-ignore
      window.pdfjsWorkerSrc = '/pdf-worker/pdf.worker.4.8.69.min.js';
    }
  }, []);

  return (
    <>
      {/* Script para carregar o worker do PDF.js */}
      <Script 
        id="pdf-worker-script"
        strategy="beforeInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            // Definir a variável global para o worker do PDF.js
            window.pdfjsWorkerSrc = '/pdf-worker/pdf.worker.4.8.69.min.js';
          `
        }}
      />
    </>
  );
}