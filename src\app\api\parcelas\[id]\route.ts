import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';

export const dynamic = 'force-dynamic';

// GET /api/parcelas/[id] - Busca uma parcela específica
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = resolvedParams.id;

    if (!id) {
      return NextResponse.json({ error: 'ID da parcela é obrigatório' }, { status: 400 });
    }

    const supabase = await getSupabaseRouteClient();

    const { data, error } = await supabase
      .from('parcelas')
      .select(`
        *,
        lancamento:lancamentos(
          *,
          contato:contatos(*)
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Erro ao buscar parcela:', error);
      return NextResponse.json({ error: 'Erro ao buscar parcela' }, { status: 500 });
    }

    if (!data) {
      return NextResponse.json({ error: 'Parcela não encontrada' }, { status: 404 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Erro interno:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// PUT /api/parcelas/[id] - Atualiza uma parcela específica
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = resolvedParams.id;
    const body = await request.json();

    if (!id) {
      return NextResponse.json({ error: 'ID da parcela é obrigatório' }, { status: 400 });
    }

    const supabase = await getSupabaseRouteClient();

    // Separar dados de parcela dos dados de lançamento se houver
    const { lancamento, ...dadosParcela } = body;

    // Primeiro, atualizar a parcela
    const { data: parcelaAtualizada, error: errorParcela } = await supabase
      .from('parcelas')
      .update(dadosParcela)
      .eq('id', id)
      .select()
      .single();

    if (errorParcela) {
      console.error('Erro ao atualizar parcela:', errorParcela);
      return NextResponse.json({ error: 'Erro ao atualizar parcela' }, { status: 500 });
    }

    // Se há dados de lançamento, atualizá-los também
    if (lancamento && parcelaAtualizada.lancamento_id) {
      const { error: errorLancamento } = await supabase
        .from('lancamentos')
        .update(lancamento)
        .eq('id', parcelaAtualizada.lancamento_id);

      if (errorLancamento) {
        console.error('Erro ao atualizar lançamento:', errorLancamento);
        // Continuar mesmo com erro no lançamento, pois a parcela foi atualizada
      }
    }

    // Buscar a parcela completa atualizada
    const { data: parcelaCompleta, error: errorBusca } = await supabase
      .from('parcelas')
      .select(`
        *,
        lancamento:lancamentos(
          *,
          contato:contatos(*)
        )
      `)
      .eq('id', id)
      .single();

    if (errorBusca) {
      console.error('Erro ao buscar parcela atualizada:', errorBusca);
      // Retornar apenas os dados básicos da parcela
      return NextResponse.json(parcelaAtualizada);
    }

    return NextResponse.json(parcelaCompleta);
  } catch (error) {
    console.error('Erro interno:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// DELETE /api/parcelas/[id] - Remove uma parcela específica
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = resolvedParams.id;

    if (!id) {
      return NextResponse.json({ error: 'ID da parcela é obrigatório' }, { status: 400 });
    }

    const supabase = await getSupabaseRouteClient();

    // Primeiro, buscar a parcela para verificar se existe
    const { data: parcela, error: errorBusca } = await supabase
      .from('parcelas')
      .select('id, lancamento_id')
      .eq('id', id)
      .single();

    if (errorBusca) {
      if (errorBusca.code === 'PGRST116') {
        return NextResponse.json({ error: 'Parcela não encontrada' }, { status: 404 });
      }
      console.error('Erro ao buscar parcela:', errorBusca);
      return NextResponse.json({ error: 'Erro ao buscar parcela' }, { status: 500 });
    }

    // Remover a parcela
    const { error: errorRemocao } = await supabase
      .from('parcelas')
      .delete()
      .eq('id', id);

    if (errorRemocao) {
      console.error('Erro ao remover parcela:', errorRemocao);
      return NextResponse.json({ error: 'Erro ao remover parcela' }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Parcela removida com sucesso' });
  } catch (error) {
    console.error('Erro interno:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}