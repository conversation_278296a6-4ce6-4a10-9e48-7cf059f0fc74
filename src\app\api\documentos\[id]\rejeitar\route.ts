import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';
import { cookies } from 'next/headers';
import { createServiceClient } from '@/lib/supabase/service-client';

// POST /api/documentos/[id]/rejeitar - Rejeitar um documento
export const dynamic = 'force-dynamic';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const documentoId = resolvedParams.id;

    if (!documentoId) {
      return NextResponse.json(
        { error: 'ID do documento não fornecido' },
        { status: 400 }
      );
    }

    // Inicializar o cliente do Supabase
    const supabase = await getSupabaseRouteClient();

    // Verificar a sessão - Padrão recomendado para Route Handlers
    let session;

    try {
      const { data } = await supabase.auth.getSession();
      session = data.session;

      if (!session) {
        console.log('[DEBUG API] Sessão não encontrada, tentando alternativa...');
        // Tentar obter o usuário como alternativa
        const { data: userData } = await supabase.auth.getUser();
        if (!userData.user) {
          return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }
      }
    } catch (authError) {
      console.error('[DEBUG API] Erro ao verificar autenticação:', authError);
      return NextResponse.json({ error: 'Erro de autenticação' }, { status: 401 });
    }

    // Obter o body da requisição com o motivo da rejeição
    const { motivo } = await request.json();

    if (!motivo) {
      return NextResponse.json(
        { error: 'Motivo da rejeição não fornecido' },
        { status: 400 }
      );
    }

    // Criar cliente de serviço para contornar RLS
    const serviceClient = createServiceClient();
    if (!serviceClient) {
      return NextResponse.json({ error: 'Erro ao criar cliente de serviço para rejeição' }, { status: 500 });
    }

    // Atualizar o documento usando o cliente de serviço
    const { data, error } = await serviceClient
      .from('documentos')
      .update({
        status: 'rejeitado',
        motivo_rejeicao: motivo
      })
      .eq('id', documentoId)
      .select()
      .single();

    if (error) {
      return NextResponse.json(
        { error: 'Erro ao rejeitar documento: ' + error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Erro interno do servidor: ' + error.message },
      { status: 500 }
    );
  }
}
