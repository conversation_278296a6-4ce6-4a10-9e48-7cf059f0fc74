-- Modificar a função buscar_lancamentos_com_parcelas para aceitar um parâmetro de cache busting
-- <PERSON><PERSON>, vamos criar uma versão de backup da função original
CREATE OR REPLACE FUNCTION buscar_lancamentos_com_parcelas_original()
RETURNS SETOF json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    lancamento_record RECORD;
    parcelas_json json;
    contato_json json;
    obra_json json;
    categoria_json json;
    result_json json;
BEGIN
    FOR lancamento_record IN
        SELECT * FROM lancamentos
        ORDER BY created_at DESC
    LOOP
        -- Buscar parcelas para este lançamento
        SELECT json_agg(p) INTO parcelas_json
        FROM parcelas p
        WHERE p.lancamento_id = lancamento_record.id
        ORDER BY p.numero;

        -- Se não houver parcelas, definir como array vazio
        IF parcelas_json IS NULL THEN
            parcelas_json := '[]'::json;
        END IF;

        -- Buscar contato relacionado
        SELECT json_build_object(
            'id', c.id,
            'nome_empresa', c.nome_empresa,
            'nome_razao', c.nome_razao,
            'nome_contato', c.nome_contato,
            'chave_pix', c.chave_pix
        ) INTO contato_json
        FROM contatos c
        WHERE c.id = lancamento_record.contato_id;

        -- Buscar obra relacionada
        SELECT json_build_object(
            'id', o.id,
            'nome', o.nome
        ) INTO obra_json
        FROM obras o
        WHERE o.id = lancamento_record.obra_id;

        -- Buscar categoria relacionada
        SELECT json_build_object(
            'id', c.id,
            'nome', c.nome
        ) INTO categoria_json
        FROM categorias c
        WHERE c.id = lancamento_record.categoria_id;

        -- Construir o objeto JSON completo
        SELECT json_build_object(
            'id', lancamento_record.id,
            'descricao', lancamento_record.descricao,
            'valor_total', lancamento_record.valor_total,
            'data_competencia', lancamento_record.data_competencia,
            'forma_pagamento', lancamento_record.forma_pagamento,
            'tipo_lancamento', lancamento_record.tipo_lancamento,
            'status', lancamento_record.status,
            'observacoes', lancamento_record.observacoes,
            'obra_id', lancamento_record.obra_id,
            'contato_id', lancamento_record.contato_id,
            'categoria_id', lancamento_record.categoria_id,
            'created_at', lancamento_record.created_at,
            'updated_at', lancamento_record.updated_at,
            'parcelas', parcelas_json,
            'contatos', contato_json,
            'obras', obra_json,
            'categorias', categoria_json
        ) INTO result_json;

        -- Retornar o resultado
        RETURN NEXT result_json;
    END LOOP;

    RETURN;
END;
$$;

-- Agora, criar a nova função que aceita o parâmetro cache_bust
CREATE OR REPLACE FUNCTION buscar_lancamentos_com_parcelas(cache_bust BIGINT DEFAULT NULL)
RETURNS SETOF json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- O parâmetro cache_bust é ignorado, apenas usado para evitar cache
    -- Simplesmente chamar a função original
    RETURN QUERY SELECT * FROM buscar_lancamentos_com_parcelas_original();
END;
$$;

-- Conceder permissões para usuários autenticados
GRANT EXECUTE ON FUNCTION buscar_lancamentos_com_parcelas(BIGINT) TO authenticated;
GRANT EXECUTE ON FUNCTION buscar_lancamentos_com_parcelas_original() TO authenticated;
