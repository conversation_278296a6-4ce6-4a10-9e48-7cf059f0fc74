'use client';

import { useState, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { clearSupabaseCookies, resetSession, emergencyFix, checkForBrokenCookies } from '@/lib/fixCookies';

export function DebugInfoPanel() {
  const [mounted, setMounted] = useState(false);
  const [mountTime, setMountTime] = useState('');
  const [hasBrokenCookies, setHasBrokenCookies] = useState(false);
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    setMounted(true);
    setMountTime(new Date().toISOString());

    // Verificar cookies
    const broken = checkForBrokenCookies();
    setHasBrokenCookies(broken);

    return () => {
      // Cleanup function
    };
  }, [pathname]);

  const handleForceNavigate = (path: string) => {
    window.location.href = path;
  };

  const handleFixCookies = () => {
    clearSupabaseCookies();
    window.location.reload();
  };

  const handleResetSession = () => {
    resetSession();
  };

  const handleEmergencyFix = () => {
    emergencyFix();
  };

  if (!mounted) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-red-100 border border-red-300 rounded-md p-3 shadow-lg z-50 max-w-sm">
      <h3 className="text-red-800 font-bold text-sm mb-2">Painel de Diagnóstico</h3>
      <div className="space-y-1 text-xs">
        <p><strong>Página atual:</strong> {pathname}</p>
        <p><strong>Montado em:</strong> {mountTime}</p>
        <p><strong>Tempo online:</strong> {Math.floor((Date.now() - new Date(mountTime).getTime()) / 1000)} segundos</p>
        <p className={hasBrokenCookies ? "text-red-600 font-bold" : "text-green-600"}>
          <strong>Estado cookies:</strong> {hasBrokenCookies ? "Cookies quebrados detectados!" : "Cookies ok"}
        </p>
      </div>

      <div className="grid grid-cols-2 gap-2 mt-2">
        <Button
          size="sm"
          variant="destructive"
          className="text-xs py-1 px-2 h-auto col-span-1"
          onClick={() => handleForceNavigate('/lancamentos/importar')}
        >
          Forçar Navegação
        </Button>
        <Button
          size="sm"
          variant="outline"
          className="text-xs py-1 px-2 h-auto col-span-1"
          onClick={() => {}}
        >
          Log Router
        </Button>

        <Button
          size="sm"
          variant={hasBrokenCookies ? "destructive" : "outline"}
          className="text-xs py-1 px-2 h-auto col-span-1"
          onClick={handleFixCookies}
        >
          Limpar Cookies
        </Button>

        <Button
          size="sm"
          variant="outline"
          className="text-xs py-1 px-2 h-auto col-span-1"
          onClick={handleResetSession}
        >
          Resetar Sessão
        </Button>

        <Button
          size="sm"
          variant="destructive"
          className="text-xs py-1 px-2 h-auto col-span-2"
          onClick={handleEmergencyFix}
        >
          Correção de Emergência
        </Button>
      </div>
    </div>
  );
}