'use client';

import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface NotificationPortalProps {
  children: React.ReactNode;
}

export function NotificationPortal({ children }: NotificationPortalProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Só renderiza o portal no lado do cliente
  if (!mounted) return null;

  // Cria um portal para o body do documento
  return createPortal(
    <div className="notification-portal">{children}</div>,
    document.body
  );
}