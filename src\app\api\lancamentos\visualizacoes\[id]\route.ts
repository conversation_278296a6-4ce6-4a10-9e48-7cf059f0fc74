import { NextRequest, NextResponse } from 'next/server'
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';
import { createClient } from '@supabase/supabase-js';

export const dynamic = 'force-dynamic';

// Criar cliente Supabase com service_role para operações administrativas
const createServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    return null;
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

// GET - Obter visualização por ID
export async function GET(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  try {
    // Obter o ID da URL diretamente
    const pathParts = req.nextUrl.pathname.split('/');
    const id = pathParts[pathParts.length - 1]; // O ID está na última posição da URL

    if (!id) {
      return NextResponse.json({ error: 'ID não fornecido' }, { status: 400 });
    }

    // Criar cliente Supabase
    const supabase = await getSupabaseRouteClient();

    // Usar a função RPC para buscar visualização por ID (contorna o RLS)
    const { data, error } = await supabase.rpc(
      'get_visualizacao_by_id',
      { p_id: id }
    );

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (!data || data.length === 0) {
      return NextResponse.json({ error: 'Visualização não encontrada' }, { status: 404 });
    }

    return NextResponse.json(data[0]);
  } catch (error: any) {
    return NextResponse.json({
      error: 'Erro ao buscar visualização',
      message: error.message
    }, { status: 500 });
  }
}

// PATCH - Atualizar visualização
export async function PATCH(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  let id = '';
  try {
    // Obter o ID da URL diretamente
    const pathParts = req.nextUrl.pathname.split('/');
    id = pathParts[pathParts.length - 1]; // O ID está na última posição da URL

    if (!id) {
      return NextResponse.json({ error: 'ID da visualização não fornecido' }, { status: 400 });
    }

    // Criar cliente Supabase para verificar se a visualização existe
    const supabase = await getSupabaseRouteClient();

    // Verificar se a visualização existe antes de tentar atualizar
    const { data: visualizacao, error: checkError } = await supabase
      .from('visualizacoes')
      .select('id, nome')
      .eq('id', id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({
          error: 'Visualização não encontrada',
          id: id
        }, { status: 404 });
      }
      // Continuar mesmo com erro - tentaremos atualizar de qualquer forma
    }

    // Configurar o ID na query string
    req.nextUrl.searchParams.set('id', id);

    // Chamar a rota principal
    return (await import('../route')).PATCH(req);
  } catch (error: any) {
    return NextResponse.json({
      error: 'Erro ao atualizar visualização',
      message: error.message,
      id: id
    }, { status: 500 });
  }
}

// DELETE - Excluir visualização
export async function DELETE(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  let id = '';
  try {
    // Obter o ID da URL diretamente
    const pathParts = req.nextUrl.pathname.split('/');
    id = pathParts[pathParts.length - 1]; // O ID está na última posição da URL

    if (!id) {
      return NextResponse.json({ error: 'ID da visualização não fornecido' }, { status: 400 });
    }

    // Criar cliente Supabase para verificar se a visualização existe
    const supabase = await getSupabaseRouteClient();

    // Verificar se a visualização existe antes de tentar excluir
    const { data: visualizacao, error: checkError } = await supabase
      .from('visualizacoes')
      .select('id, nome')
      .eq('id', id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({
          error: 'Visualização não encontrada',
          id: id
        }, { status: 404 });
      }
      // Continuar mesmo com erro - tentaremos excluir de qualquer forma
    }

    // Configurar o ID na query string
    req.nextUrl.searchParams.set('id', id);

    // Chamar a rota principal
    return (await import('../route')).DELETE(req);
  } catch (error: any) {
    return NextResponse.json({
      error: 'Erro ao excluir visualização',
      message: error.message,
      id: id
    }, { status: 500 });
  }
}
