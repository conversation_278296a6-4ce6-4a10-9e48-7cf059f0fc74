-- Create tables for lancamentos and parcelas
CREATE TABLE lancamentos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    obra_id UUID NOT NULL REFERENCES constructions(id),
    tipo_lancamento VARCHAR(20) NOT NULL CHECK (tipo_lancamento IN ('construtora', 'terceiros')),
    valor_total DECIMAL(12,2) NOT NULL,
    data_competencia DATE NOT NULL,
    forma_pagamento VARCHAR(50) NOT NULL,
    descricao TEXT NOT NULL,
    contato_id UUID REFERENCES contacts(id),
    observacoes TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'em_aberto' CHECK (status IN ('em_aberto', 'concluido', 'cancelado')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE parcelas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lancamento_id UUID NOT NULL REFERENCES lancamentos(id) ON DELETE CASCADE,
    numero INTEGER NOT NULL,
    valor DECIMAL(12,2) NOT NULL,
    vencimento DATE NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pendente' CHECK (status IN ('pendente', 'pago', 'cancelado')),
    data_pagamento DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_lancamentos_obra_id ON lancamentos(obra_id);
CREATE INDEX idx_lancamentos_contato_id ON lancamentos(contato_id);
CREATE INDEX idx_parcelas_lancamento_id ON parcelas(lancamento_id);

-- Create triggers for updated_at
CREATE TRIGGER set_timestamp_lancamentos
    BEFORE UPDATE ON lancamentos
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_timestamp_parcelas
    BEFORE UPDATE ON parcelas
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

-- Create function to validate parcelas total value matches lancamento valor_total
CREATE OR REPLACE FUNCTION validate_parcelas_total()
RETURNS TRIGGER AS $$
DECLARE
    total_parcelas DECIMAL(12,2);
    lancamento_total DECIMAL(12,2);
BEGIN
    -- Get the total value of all parcelas for this lancamento
    SELECT COALESCE(SUM(valor), 0)
    INTO total_parcelas
    FROM parcelas
    WHERE lancamento_id = NEW.lancamento_id;

    -- Add the value of the new/updated parcela
    total_parcelas := total_parcelas + NEW.valor;

    -- If this is an UPDATE, subtract the old value
    IF TG_OP = 'UPDATE' THEN
        total_parcelas := total_parcelas - OLD.valor;
    END IF;

    -- Get the valor_total from lancamentos
    SELECT valor_total
    INTO lancamento_total
    FROM lancamentos
    WHERE id = NEW.lancamento_id;

    -- Compare the totals
    IF total_parcelas > lancamento_total THEN
        RAISE EXCEPTION 'Total value of parcelas cannot exceed lancamento valor_total';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for parcelas validation
CREATE TRIGGER validate_parcelas_total
    BEFORE INSERT OR UPDATE ON parcelas
    FOR EACH ROW
    EXECUTE FUNCTION validate_parcelas_total(); 