'use client';

import { useEffect, useState } from 'react';
import { Obra } from '@/types/obras';
import { useObrasService } from '@/services/obras';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2 } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ColumnSelector, Column } from './column-selector';

const statusMap = {
  em_andamento: { label: 'Em Andamento', variant: 'default' },
  planejada: { label: 'Planejada', variant: 'secondary' },
  concluida: { label: 'Concluída', variant: 'success' },
  pausada: { label: 'Pausada', variant: 'warning' },
  cancelada: { label: 'Cancelada', variant: 'destructive' },
} as const;

interface ListaObrasProps {
  onEdit: (obra: Obra) => void;
  version?: number;
  columns: Column[];
  onColumnsChange: (columns: Column[]) => void;
}

export function ListaObras({ onEdit, version = 0, columns, onColumnsChange }: ListaObrasProps) {
  const [obras, setObras] = useState<Obra[]>([]);
  const [loading, setLoading] = useState(true);
  const [obraParaExcluir, setObraParaExcluir] = useState<string | null>(null);
  const obrasService = useObrasService();

  // Efeito para carregar obras quando o componente monta ou a versão muda
  useEffect(() => {
    carregarObras();
  }, [version]);

  async function carregarObras() {
    try {
      setLoading(true);
      const data = await obrasService.listarObras();
      setObras(data);
    } catch (error) {
      toast.error('Erro ao carregar obras');
      
    } finally {
      setLoading(false);
    }
  }

  async function handleExcluir(e: React.MouseEvent, id: string) {
    e.stopPropagation(); // Previne que a linha seja clicada
    setObraParaExcluir(id);
  }

  async function confirmarExclusao() {
    if (!obraParaExcluir) return;

    try {
      await obrasService.excluirObra(obraParaExcluir);
      toast.success('Obra excluída com sucesso');
      carregarObras();
    } catch (error) {
      toast.error('Erro ao excluir obra');
      
    } finally {
      setObraParaExcluir(null);
    }
  }

  const visibleColumns = columns.filter(col => col.isVisible);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-black"></div>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader className="bg-muted">
            <TableRow>
              {columns
                .filter(col => col.isVisible)
                .map(col => (
                  <TableHead
                    key={col.id}
                    className={col.id === 'acoes' ? 'w-[100px]' : ''}
                  >
                    {col.label}
                  </TableHead>
                ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {obras.length === 0 ? (
              <TableRow>
                <TableCell colSpan={visibleColumns.length} className="text-center">
                  Nenhuma obra encontrada
                </TableCell>
              </TableRow>
            ) : (
              obras.map((obra) => (
                <TableRow
                  key={obra.id}
                  onClick={() => onEdit(obra)}
                  className="cursor-pointer hover:bg-muted/50"
                >
                  {columns.find(col => col.id === 'nome')?.isVisible && (
                    <TableCell className="font-medium">{obra.nome}</TableCell>
                  )}
                  {columns.find(col => col.id === 'descricao')?.isVisible && (
                    <TableCell>{obra.descricao}</TableCell>
                  )}
                  {columns.find(col => col.id === 'data_inicio')?.isVisible && (
                    <TableCell>
                      {obra.data_inicio ? (
                        format(new Date(obra.data_inicio), 'dd/MM/yy', {
                          locale: ptBR,
                        })
                      ) : (
                        '-'
                      )}
                    </TableCell>
                  )}
                  {columns.find(col => col.id === 'status')?.isVisible && (
                    <TableCell>
                      <Badge variant={statusMap[obra.status].variant as any}>
                        {statusMap[obra.status].label}
                      </Badge>
                    </TableCell>
                  )}
                  {columns.find(col => col.id === 'acoes')?.isVisible && (
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={(e) => {
                            e.stopPropagation(); // Previne que a linha seja clicada
                            onEdit(obra);
                          }}
                          className="h-7 w-7"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={(e) => handleExcluir(e, obra.id)}
                          className="h-7 w-7"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <Dialog open={!!obraParaExcluir} onOpenChange={() => setObraParaExcluir(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir esta obra? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setObraParaExcluir(null)}
              size="sm"
              className="h-8 gap-1"
            >
              <span>Cancelar</span>
            </Button>
            <Button
              variant="destructive"
              onClick={confirmarExclusao}
              size="sm"
              className="h-8 gap-1"
            >
              <span>Excluir</span>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}