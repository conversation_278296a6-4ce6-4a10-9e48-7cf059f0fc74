import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';

// Criar cliente Supabase com service_role para operações administrativas
// Isso permite contornar o RLS quando necessário
const createServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Variáveis de ambiente para service client não configuradas');
    return null;
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

// GET /api/lancamentos/visualizacoes - Lista visualizações do usuário/contexto
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Obter userId da query string, se disponível
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');
    const forceServiceRole = url.searchParams.get('forceServiceRole') === 'true';
    const allVisualizacoes = url.searchParams.get('allVisualizacoes') === 'true';

    // Se allVisualizacoes for true, buscar todas as visualizações
    if (allVisualizacoes) {

      // Criar cliente Supabase
      const supabase = await getSupabaseRouteClient();

      const { data, error } = await supabase.rpc(
        'get_all_visualizacoes',
        {
          p_contexto: 'lancamentos'
        }
      );

      if (error) {
        return NextResponse.json({ error: 'Erro ao buscar todas as visualizações: ' + error.message }, { status: 500 });
      }
      return NextResponse.json(data || []);
    }

    const supabase = await getSupabaseRouteClient();

    // Tentar obter usuário autenticado, mas não falhar se não encontrar
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    // Determinar qual userId usar (da sessão ou da query)
    const userIdToUse = user?.id || userId;

    if (!userIdToUse) {
      return NextResponse.json([]);
    }

    // Contexto fixo para lançamentos
    const contexto = 'lancamentos';

    // Usar a função RPC para buscar visualizações
    const { data, error } = await supabase.rpc(
      'get_visualizacoes',
      {
        p_user_id: userIdToUse,
        p_contexto: contexto
      }
    );

    // Se não encontrou visualizações, tentar com o ID fixo
    if ((!data || data.length === 0) && userIdToUse !== '076472da-7947-4a37-be5d-7e8deef6c380') {

      const fixedUserId = '076472da-7947-4a37-be5d-7e8deef6c380';

      const { data: fixedData, error: fixedError } = await supabase.rpc(
        'get_visualizacoes',
        {
          p_user_id: fixedUserId,
          p_contexto: contexto
        }
      );

      if (!fixedError && fixedData && fixedData.length > 0) {
        return NextResponse.json(fixedData);
      }

      // Se ainda não encontrou, buscar todas as visualizações
      const { data: allData, error: allError } = await supabase.rpc(
        'get_all_visualizacoes',
        {
          p_contexto: contexto
        }
      );

      if (!allError && allData && allData.length > 0) {
        return NextResponse.json(allData);
      }
    }

    if (error) {
      return NextResponse.json({ error: 'Erro ao buscar visualizações: ' + error.message }, { status: 500 });
    }

    return NextResponse.json(data || []);
  } catch (error: any) {
    console.error('Erro ao processar visualizações:', error);
    return NextResponse.json({ error: 'Erro interno do servidor: ' + (error?.message || 'desconhecido') }, { status: 500 });
  }
}

// POST /api/lancamentos/visualizacoes - Cria nova visualização
export async function POST(request: NextRequest) {
  try {
    // Obter o body da requisição
    const body = await request.json();

    // Criar cliente Supabase
    const supabase = await getSupabaseRouteClient();

    // Tentar obter o usuário da sessão primeiro
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    // Verificar se temos um userId no body da requisição
    const bodyUserId = body.userId;

    // Determinar qual ID de usuário usar
    let userIdToUse;

    if (user && user.id) {
      // Se temos um usuário autenticado, usar seu ID
      userIdToUse = user.id;
    } else if (bodyUserId) {
      // Se não temos usuário autenticado mas temos um ID no body, usar esse ID
      userIdToUse = bodyUserId;
    } else {
      // Se não temos nem usuário autenticado nem ID no body, retornar erro
      return NextResponse.json({
        error: 'Você precisa estar autenticado para criar uma visualização'
      }, { status: 401 });
    }

    // Validações básicas
    const { nome, descricao, filtros_json } = body;

    if (!nome || typeof nome !== 'string' || nome.trim().length === 0) {
      return NextResponse.json({ error: 'Nome é obrigatório' }, { status: 400 });
    }

    // Garantir que filtros_json seja um objeto válido, mesmo que vazio
    if (filtros_json !== undefined && typeof filtros_json !== 'object') {
      return NextResponse.json({ error: 'Filtros inválidos' }, { status: 400 });
    }

    // Se filtros_json for undefined ou null, definir como objeto vazio
    if (filtros_json === undefined || filtros_json === null) {
      filtros_json = {};
    }

    // Limpar filtros undefined, null ou vazios
    const filtrosLimpos = Object.fromEntries(
      Object.entries(filtros_json).filter(([_, valor]) =>
        valor !== undefined && valor !== null && valor !== ''
      )
    );

    // Inserir a visualização usando o cliente de serviço para contornar RLS
    const contexto = 'lancamentos';

    // Tentar primeiro com inserção direta na tabela
    try {
      const { data: directData, error: directError } = await supabase
        .from('visualizacoes')
        .insert({
          user_id: userIdToUse,
          contexto: contexto,
          nome: nome,
          descricao: descricao || null,
          filtros_json: filtrosLimpos
        })
        .select()
        .single();

      if (!directError && directData) {
        return NextResponse.json(directData, { status: 201 });
      }
    } catch (directInsertError) {
      // Falha na inserção direta, tentaremos outro método
    }

    // Se falhar, tentar com a nova função direct_insert_visualizacao
    const { data, error } = await supabase.rpc(
      'direct_insert_visualizacao',
      {
        p_user_id: userIdToUse,
        p_contexto: contexto,
        p_nome: nome,
        p_descricao: descricao || null,
        p_filtros_json: filtrosLimpos
      }
    );

    if (error) {
      // Tentar com a função de serviço como último recurso
      try {
        const serviceClient = createServiceClient();

        if (!serviceClient) {
          return NextResponse.json({
            error: 'Erro ao criar visualização: não foi possível criar o cliente de serviço',
          }, { status: 500 });
        }

        const { data: serviceData, error: serviceError } = await serviceClient.rpc(
          'service_insert_visualizacao',
          {
            p_user_id: userIdToUse,
            p_contexto: contexto,
            p_nome: nome,
            p_descricao: descricao || null,
            p_filtros_json: filtrosLimpos
          }
        );

        if (!serviceError && serviceData) {
          // Buscar a visualização completa
          const { data: visualizacao, error: fetchError } = await serviceClient
            .from('visualizacoes')
            .select('*')
            .eq('id', serviceData)
            .single();

          if (!fetchError && visualizacao) {
            return NextResponse.json(visualizacao, { status: 201 });
          }

          // Mesmo com erro no fetch, retornar sucesso com o ID
          return NextResponse.json({ id: serviceData }, { status: 201 });
        }

        console.error('Erro na inserção via service_insert_visualizacao:', serviceError);
      } catch (serviceError) {
        console.error('Exceção ao usar service_insert_visualizacao:', serviceError);
      }

      // Se chegou aqui, todas as tentativas falharam
      return NextResponse.json({
        error: 'Erro ao criar visualização: ' + error.message,
        detalhes: error
      }, { status: 500 });
    }

    // A função RPC retorna apenas o ID da visualização
    const visualizacaoId = data;

    // Buscar a visualização completa para retornar
    const { data: visualizacao, error: fetchError } = await supabase
      .from('visualizacoes')
      .select('*')
      .eq('id', visualizacaoId)
      .single();

    if (fetchError) {
      // Mesmo com erro, retornamos sucesso com o ID
      return NextResponse.json({ id: visualizacaoId }, { status: 201 });
    }

    return NextResponse.json(visualizacao, { status: 201 });

  } catch (error: any) {
    return NextResponse.json({
      error: 'Erro interno do servidor: ' + (error?.message || 'desconhecido'),
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }, { status: 500 });
  }
}

// DELETE /api/lancamentos/visualizacoes?id=<id> - Remove uma visualização
export async function DELETE(request: NextRequest) {
  try {
    // Obter ID da visualização da query string
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'ID da visualização não fornecido' }, { status: 400 });
    }

    // Criar cliente Supabase
    const supabase = await getSupabaseRouteClient();

    // Obter userId da query string
    const userId = url.searchParams.get('userId');

    // Tentar obter o usuário da sessão
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    // Determinar qual userId usar (da sessão ou da query)
    let userIdToUse = user?.id || userId;

    if (!userIdToUse) {
      return NextResponse.json({ error: 'ID do usuário não fornecido' }, { status: 400 });
    }

    // Com RLS ativado, a verificação de propriedade é automática
    // Mas vamos verificar explicitamente para fornecer mensagens de erro melhores
    const { data: visualizacao, error: checkError } = await supabase
      .from('visualizacoes')
      .select('user_id, nome')
      .eq('id', id)
      .single();

    if (checkError) {
      // Se o erro for "não encontrado", pode ser que a visualização não exista
      // ou que o usuário não tenha permissão para vê-la
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Visualização não encontrada' }, { status: 404 });
      }
      return NextResponse.json({ error: 'Erro ao verificar visualização: ' + checkError.message }, { status: 500 });
    }

    // Não verificamos mais se a visualização pertence ao usuário
    // Permitimos que qualquer usuário exclua qualquer visualização

    // Excluir a visualização usando a função RPC de administrador
    const { data: deleted, error } = await supabase.rpc(
      'admin_delete_visualizacao',
      { p_id: id }
    );

    if (error) {
      // Verificar se é um erro de permissão
      if (error.code === '42501' || error.message.includes('permission denied')) {
        return NextResponse.json({
          error: 'Permissão negada. Você não tem autorização para excluir esta visualização.',
        }, { status: 403 });
      }

      return NextResponse.json({ error: 'Erro ao excluir visualização: ' + error.message }, { status: 500 });
    }

    return NextResponse.json({ message: 'Visualização excluída com sucesso' });
  } catch (error: any) {
    console.error('Erro ao processar DELETE visualizacao:', error);
    return NextResponse.json({ error: 'Erro interno do servidor: ' + (error?.message || 'desconhecido') }, { status: 500 });
  }
}

// PATCH /api/lancamentos/visualizacoes?id=<id> - Atualiza uma visualização
export async function PATCH(request: NextRequest) {
  try {
    // Obter ID da visualização da query string OU do path
    let id = null;
    try {
      const url = new URL(request.url);
      id = url.searchParams.get('id');
    } catch (e) {
      // fallback para path
      id = request.nextUrl?.searchParams?.get('id') || null;
    }
    if (!id && request.nextUrl?.pathname) {
      const match = request.nextUrl.pathname.match(/visualizacoes\/?([\w-]+)/);
      if (match && match[1]) id = match[1];
    }

    if (!id) {
      return NextResponse.json({ error: 'ID da visualização não fornecido' }, { status: 400 });
    }
    // Obter o body da requisição
    const body = await request.json();
    // Criar cliente Supabase autenticado com os cookies da requisição
    const supabase = await getSupabaseRouteClient();
    // Obter usuário autenticado da sessão
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Usuário não autenticado. Faça login novamente.' }, { status: 401 });
    }
    const userIdToUse = user.id;
    // Buscar visualização e validar ownership
    const { data: visualizacao, error: checkError } = await supabase
      .from('visualizacoes')
      .select('user_id, nome')
      .eq('id', id)
      .single();
    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Visualização não encontrada' }, { status: 404 });
      }
      return NextResponse.json({ error: 'Erro ao verificar visualização: ' + checkError.message }, { status: 500 });
    }
    // Não verificamos mais se a visualização pertence ao usuário
    // Permitimos que qualquer usuário edite qualquer visualização
    // Validações básicas
    const { nome, descricao, filtros_json } = body;
    if (nome !== undefined && (typeof nome !== 'string' || nome.trim().length === 0)) {
      return NextResponse.json({ error: 'Nome é obrigatório' }, { status: 400 });
    }
    if (filtros_json !== undefined && typeof filtros_json !== 'object') {
      return NextResponse.json({ error: 'Filtros inválidos' }, { status: 400 });
    }
    let filtrosLimpos;
    if (filtros_json) {
      filtrosLimpos = Object.fromEntries(
        Object.entries(filtros_json).filter(([_, valor]) =>
          valor !== undefined && valor !== null && valor !== ''
        )
      );
    }
    const updateData: any = {};
    if (nome !== undefined) updateData.nome = nome;
    if (descricao !== undefined) updateData.descricao = descricao;
    if (filtros_json !== undefined) updateData.filtros_json = filtrosLimpos || filtros_json;
    updateData.updated_at = new Date().toISOString();

    // Usar a função RPC de administrador para atualizar a visualização
    const { data, error } = await supabase.rpc(
      'admin_update_visualizacao',
      {
        p_id: id,
        p_nome: updateData.nome,
        p_descricao: updateData.descricao,
        p_filtros_json: updateData.filtros_json
      }
    );
    if (error) {
      if (error.code === '42501' || error.message.includes('permission denied')) {
        return NextResponse.json({
          error: 'Permissão negada. Você não tem autorização para atualizar esta visualização.',
        }, { status: 403 });
      }
      return NextResponse.json({ error: 'Erro ao atualizar visualização: ' + error.message }, { status: 500 });
    }
    return NextResponse.json(data);
  } catch (error: any) {
    return NextResponse.json({ error: 'Erro interno do servidor: ' + (error?.message || 'desconhecido') }, { status: 500 });
  }
}