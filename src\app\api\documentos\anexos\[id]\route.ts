import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';
import { cookies } from 'next/headers';
import { createServiceClient } from '@/lib/supabase/service-client';
import { verificarAutenticacao } from '@/lib/auth-helpers';

// DELETE /api/documentos/anexos/[id] - Excluir um anexo
export const dynamic = 'force-dynamic';

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Inicializar o cliente do Supabase
    const supabase = await getSupabaseRouteClient();

    // Verificar a autenticação
    const auth = await verificarAutenticacao(supabase);
    if (!auth.autenticado) {
      return auth.resposta || NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const { id: anexoId } = await params;

    // Criar cliente de serviço para contornar RLS
    const serviceClient = createServiceClient();
    if (!serviceClient) {
      return NextResponse.json({ error: 'Erro ao criar cliente de serviço' }, { status: 500 });
    }

    // Primeiro, buscar o anexo para obter o caminho do arquivo
    const { data: anexo, error: fetchError } = await serviceClient
      .from('documento_anexos')
      .select('arquivo_path')
      .eq('id', anexoId)
      .single();

    if (fetchError) {
      return NextResponse.json({ error: fetchError.message }, { status: 500 });
    }

    if (!anexo) {
      return NextResponse.json({ error: 'Anexo não encontrado' }, { status: 404 });
    }

    // Excluir o arquivo do storage
    const { error: storageError } = await serviceClient.storage
      .from('anexos')
      .remove([anexo.arquivo_path]);

    if (storageError) {
      console.error('Erro ao excluir arquivo do storage:', storageError);
      // Continuar mesmo com erro no storage para garantir que o registro seja removido
    }

    // Excluir o registro do banco de dados
    const { error: deleteError } = await serviceClient
      .from('documento_anexos')
      .delete()
      .eq('id', anexoId);

    if (deleteError) {
      return NextResponse.json({ error: deleteError.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Erro interno do servidor: ' + error.message },
      { status: 500 }
    );
  }
}
