'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Trash2, FolderOpen, Tag, X, Check } from 'lucide-react';
import { toast } from 'sonner';
import { useLancamentosService } from '@/services/lancamentos';
import { useObrasService } from '@/services/obras';
import { useCategoriasService } from '@/services/categorias';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';

interface AcoesEmMassaProps {
  selectedIds: string[];
  onClearSelection: () => void;
  onActionComplete: (forceRefresh?: boolean) => void;
}

export function AcoesEmMassa({ selectedIds, onClearSelection, onActionComplete }: AcoesEmMassaProps) {
  const [isExcluirDialogOpen, setIsExcluirDialogOpen] = useState(false);
  const [isObraDialogOpen, setIsObraDialogOpen] = useState(false);
  const [isCategoriaDialogOpen, setIsCategoriaDialogOpen] = useState(false);
  const [selectedObraId, setSelectedObraId] = useState<string>('');
  const [selectedCategoriaId, setSelectedCategoriaId] = useState<string>('');
  const [obras, setObras] = useState<any[]>([]);
  const [categorias, setCategorias] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const lancamentosService = useLancamentosService();
  const obrasService = useObrasService();
  const categoriasService = useCategoriasService();

  // Carregar obras quando o diálogo de obras for aberto
  const handleOpenObraDialog = async () => {
    try {
      setIsLoading(true);
      const obrasData = await obrasService.listarObras();
      setObras(obrasData);
      setIsObraDialogOpen(true);
    } catch (error) {
      toast.error('Erro ao carregar obras');
    } finally {
      setIsLoading(false);
    }
  };

  // Carregar categorias quando o diálogo de categorias for aberto
  const handleOpenCategoriaDialog = async () => {
    try {
      setIsLoading(true);
      const categoriasData = await categoriasService.listarCategorias();
      setCategorias(categoriasData);
      setIsCategoriaDialogOpen(true);
    } catch (error) {
      toast.error('Erro ao carregar categorias');
    } finally {
      setIsLoading(false);
    }
  };

  // Excluir lançamentos selecionados
  const handleExcluirLancamentos = async () => {
    try {
      setIsLoading(true);
      const loadingToast = toast.loading(`Excluindo ${selectedIds.length} lançamentos...`);

      // Chamar o serviço para excluir todos os lançamentos de uma vez
      // O serviço já implementa o processamento em lotes internamente
      const result = await lancamentosService.deleteManyLancamentos(selectedIds);

      // Determinar quantos foram excluídos com sucesso
      const successCount = selectedIds.length - (result.errors.length || 0);

      // Remover o toast de carregamento
      toast.dismiss(loadingToast);

      // Aguardar um momento para garantir que o banco de dados processou as alterações
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Verificar se houve erros
      if (result.success && result.errors.length === 0) {
        // Sucesso total - todos os lançamentos foram excluídos
        toast.success(`${successCount} lançamentos excluídos com sucesso`);
        onClearSelection();

        // Atualizar a lista de lançamentos
        onActionComplete(true);
      } else if (successCount > 0) {
        // Sucesso parcial - alguns lançamentos foram excluídos
        toast.success(`${successCount} de ${selectedIds.length} lançamentos excluídos com sucesso`);
        onClearSelection();

        // Mostrar erros de forma mais organizada
        if (result.errors.length === 1) {
          // Se houver apenas um erro, mostrar completo
          toast.warning(`Um lançamento não pôde ser excluído: ${result.errors[0]}`, {
            duration: 5000
          });
        } else if (result.errors.length <= 3) {
          // Se houver poucos erros, mostrar todos
          toast.warning(`${result.errors.length} lançamentos não puderam ser excluídos: ${result.errors.join('; ')}`, {
            duration: 5000
          });
        } else {
          // Se houver muitos erros, mostrar apenas o número e os primeiros
          toast.warning(
            `${result.errors.length} lançamentos não puderam ser excluídos. Primeiros erros: ${result.errors.slice(0, 2).join('; ')}...`,
            { duration: 6000 }
          );
        }

        // Atualizar a lista de lançamentos
        onActionComplete(true);
      } else {
        // Falha total - nenhum lançamento foi excluído
        toast.error(`Falha ao excluir lançamentos. Nenhum lançamento foi excluído.`);

        // Mostrar erros de forma mais organizada
        if (result.errors.length === 1) {
          // Se houver apenas um erro, mostrar completo
          toast.error(`Erro: ${result.errors[0]}`, {
            duration: 5000
          });
        } else if (result.errors.length <= 3) {
          // Se houver poucos erros, mostrar todos
          toast.error(`Erros: ${result.errors.join('; ')}`, {
            duration: 5000
          });
        } else {
          // Se houver muitos erros, mostrar apenas o número e os primeiros
          toast.error(
            `${result.errors.length} erros. Primeiros erros: ${result.errors.slice(0, 2).join('; ')}...`,
            { duration: 6000 }
          );
        }
      }
    } catch (error: any) {
      toast.error(`Erro ao excluir lançamentos: ${error.message}`);
    } finally {
      setIsLoading(false);
      setIsExcluirDialogOpen(false);
    }
  };

  // Atualizar obra dos lançamentos selecionados
  const handleAtualizarObra = async () => {
    if (!selectedObraId) {
      toast.error('Selecione uma obra');
      return;
    }

    try {
      setIsLoading(true);
      const loadingToast = toast.loading(`Atualizando obra de ${selectedIds.length} lançamentos...`);

      const result = await lancamentosService.updateManyLancamentosObra(selectedIds, selectedObraId);

      toast.dismiss(loadingToast);

      if (result.success) {
        toast.success(`Obra atualizada para ${selectedIds.length} lançamentos`);
        onActionComplete();
      } else {
        toast.error(`Erro ao atualizar obra: ${result.errors.join(', ')}`);
      }
    } catch (error: any) {
      toast.error(`Erro ao atualizar obra: ${error.message}`);
    } finally {
      setIsLoading(false);
      setIsObraDialogOpen(false);
      setSelectedObraId('');
    }
  };

  // Atualizar categoria dos lançamentos selecionados
  const handleAtualizarCategoria = async () => {
    if (!selectedCategoriaId) {
      toast.error('Selecione uma categoria');
      return;
    }

    try {
      setIsLoading(true);
      const loadingToast = toast.loading(`Atualizando categoria de ${selectedIds.length} lançamentos...`);

      const result = await lancamentosService.updateManyLancamentosCategoria(selectedIds, selectedCategoriaId);

      toast.dismiss(loadingToast);

      if (result.success) {
        toast.success(`Categoria atualizada para ${selectedIds.length} lançamentos`);
        onActionComplete();
      } else {
        toast.error(`Erro ao atualizar categoria: ${result.errors.join(', ')}`);
      }
    } catch (error: any) {
      toast.error(`Erro ao atualizar categoria: ${error.message}`);
    } finally {
      setIsLoading(false);
      setIsCategoriaDialogOpen(false);
      setSelectedCategoriaId('');
    }
  };

  return (
    <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 bg-slate-900 text-white rounded-lg shadow-lg p-4 flex items-center gap-3 border border-slate-700">
      <div className="flex items-center gap-2">
        <span className="text-sm font-semibold text-white">{selectedIds.length} itens selecionados</span>
        <Button
          variant="secondary"
          size="sm"
          onClick={onClearSelection}
          className="h-8 px-3 bg-slate-700 text-white hover:bg-slate-600 font-medium"
        >
          <X className="h-4 w-4 mr-1" />
          <span>Limpar</span>
        </Button>
      </div>

      <div className="h-8 w-px bg-slate-600 mx-2" />

      <div className="flex items-center gap-3">
        <Button
          variant="secondary"
          size="sm"
          onClick={() => setIsExcluirDialogOpen(true)}
          className="h-8 px-3 bg-slate-700 text-white hover:bg-slate-600 font-medium"
        >
          <Trash2 className="h-4 w-4 mr-1" />
          <span>Excluir</span>
        </Button>

        <Button
          variant="secondary"
          size="sm"
          onClick={handleOpenObraDialog}
          className="h-8 px-3 bg-slate-700 text-white hover:bg-slate-600 font-medium"
        >
          <FolderOpen className="h-4 w-4 mr-1" />
          <span>Editar Projeto</span>
        </Button>

        <Button
          variant="secondary"
          size="sm"
          onClick={handleOpenCategoriaDialog}
          className="h-8 px-3 bg-slate-700 text-white hover:bg-slate-600 font-medium"
        >
          <Tag className="h-4 w-4 mr-1" />
          <span>Editar Categoria</span>
        </Button>
      </div>

      {/* Diálogo de confirmação para excluir */}
      <Dialog open={isExcluirDialogOpen} onOpenChange={setIsExcluirDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir {selectedIds.length} lançamentos? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsExcluirDialogOpen(false)}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleExcluirLancamentos}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Excluindo...
                </>
              ) : (
                <>Excluir</>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para selecionar obra */}
      <Dialog open={isObraDialogOpen} onOpenChange={setIsObraDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Selecionar Projeto</DialogTitle>
            <DialogDescription>
              Selecione o projeto para os {selectedIds.length} lançamentos selecionados.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Select value={selectedObraId} onValueChange={setSelectedObraId}>
              <SelectTrigger className={cn("w-full", !selectedObraId && "text-muted-foreground")}>
                <SelectValue placeholder="Selecione um projeto" />
              </SelectTrigger>
              <SelectContent>
                {obras.map((obra) => (
                  <SelectItem key={obra.id} value={obra.id}>
                    {obra.nome}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsObraDialogOpen(false)}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button
              variant="default"
              onClick={handleAtualizarObra}
              disabled={isLoading || !selectedObraId}
            >
              {isLoading ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Atualizando...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-1" />
                  Confirmar
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para selecionar categoria */}
      <Dialog open={isCategoriaDialogOpen} onOpenChange={setIsCategoriaDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Selecionar Categoria</DialogTitle>
            <DialogDescription>
              Selecione a categoria para os {selectedIds.length} lançamentos selecionados.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Select value={selectedCategoriaId} onValueChange={setSelectedCategoriaId}>
              <SelectTrigger className={cn("w-full", !selectedCategoriaId && "text-muted-foreground")}>
                <SelectValue placeholder="Selecione uma categoria" />
              </SelectTrigger>
              <SelectContent>
                {categorias.map((categoria) => (
                  <SelectItem key={categoria.id} value={categoria.id}>
                    {categoria.nome}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsCategoriaDialogOpen(false)}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button
              variant="default"
              onClick={handleAtualizarCategoria}
              disabled={isLoading || !selectedCategoriaId}
            >
              {isLoading ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Atualizando...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-1" />
                  Confirmar
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}