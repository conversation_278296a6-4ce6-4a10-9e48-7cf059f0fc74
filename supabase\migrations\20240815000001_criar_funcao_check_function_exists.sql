-- Função para verificar se uma função SQL existe
CREATE OR REPLACE FUNCTION public.check_function_exists(function_name TEXT)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_exists BOOLEAN;
BEGIN
  -- Verificar se a função existe
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.routines
    WHERE routine_type = 'FUNCTION'
    AND routine_name = function_name
  ) INTO v_exists;
  
  -- Retornar o resultado como JSONB
  RETURN jsonb_build_object('exists', v_exists);
END;
$$;

-- Comentário para a função
COMMENT ON FUNCTION public.check_function_exists IS 'Função para verificar se uma função SQL existe';
