/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Configuração de logging para reduzir logs em desenvolvimento
  logging: {
    fetches: {
      fullUrl: false,
    },
    // Desabilitar logs de incoming requests em desenvolvimento
    incomingRequests: {
      ignore: [/\/_next\/static\/.*/, /\/favicon\.ico/, /\/api\/.*/, /\/_next\/webpack-hmr/, /\/hot-reloader-client/, /\/report-hmr-latency/],
    },
  },
  // Configuração webpack para reduzir logs do HMR/Fast Refresh
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // Configurações para reduzir logs em desenvolvimento
      config.infrastructureLogging = {
        level: 'error',
      };
      
      // Filtrar logs específicos do HMR
      if (config.devServer) {
        config.devServer.client = {
          ...config.devServer.client,
          logging: 'error',
        };
      }
    }
    return config;
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        port: '',
        pathname: '**',
      },
    ],
  },

  // Adicionar proxy para contornar problemas de CORS
  async rewrites() {
    return [
      {
        source: '/api/supabase/:path*',
        destination: 'https://ivmnkctopksvvzmbulah.supabase.co/rest/v1/:path*',
      },
    ];
  },
  // Configuração para servir o worker do PDF.js corretamente e lidar com CORS
  headers: async () => {
    return [
      {
        source: '/pdf-worker/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Content-Type',
            value: 'application/javascript',
          },
        ],
      },
      {
        // Configuração para todas as rotas
        source: '/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*', // Permitir qualquer origem
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET,OPTIONS,PATCH,DELETE,POST,PUT',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization',
          },
        ],
      },
    ];
  },
}

module.exports = nextConfig