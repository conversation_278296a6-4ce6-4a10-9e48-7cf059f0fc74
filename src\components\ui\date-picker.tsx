"use client"

import * as React from "react"
import { format, parse, isValid } from "date-fns"
import { ptBR } from "date-fns/locale"
import { Calendar as CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
  PopoverAnchor,
} from "@/components/ui/popover"

interface DatePickerProps {
  date?: Date
  setDate?: (date: Date | undefined) => void
  className?: string
  placeholder?: string
  inputClassName?: string
}

export function DatePicker({
  date,
  setDate,
  className,
  placeholder = "DD/MM/AAAA",
  inputClassName,
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false)
  const [inputValue, setInputValue] = React.useState<string>(
    date ? format(date, "P", { locale: ptBR }) : ""
  )
  const inputRef = React.useRef<HTMLInputElement>(null)

  React.useEffect(() => {
    setInputValue(date ? format(date, "P", { locale: ptBR }) : "")
  }, [date])

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value)
  }

  const handleInputBlur = () => {
    const parsedDate = parse(inputValue, "P", new Date(), { locale: ptBR })
    if (isValid(parsedDate)) {
      if (setDate) {
        if (!date || parsedDate.getTime() !== date.getTime()) {
           setDate(parsedDate)
        }
      } else {
         setInputValue(format(parsedDate, "P", { locale: ptBR }))
      }
    } else if (inputValue === "") {
        if (setDate && date !== undefined) {
            setDate(undefined)
        }
    } else {
      setInputValue(date ? format(date, "P", { locale: ptBR }) : "")
    }
  }

  const handleDateSelect = (selectedDate: Date | undefined) => {
    if (setDate) {
      setDate(selectedDate)
    }
    setInputValue(selectedDate ? format(selectedDate, "P", { locale: ptBR }) : "")
    setOpen(false)
    inputRef.current?.focus()
  }

  return (
    <Popover open={open} onOpenChange={setOpen} modal={false}>
      <PopoverAnchor asChild>
        <div className={cn("relative w-full", className)}>
          <Input
            ref={inputRef}
            placeholder={placeholder}
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            className={cn("pr-8 h-9 flex items-center text-sm", inputClassName)}
          />
          <PopoverTrigger asChild>
             <Button
               variant="ghost"
               size="icon"
               className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-1 text-muted-foreground hover:text-foreground"
               onClick={() => setOpen(!open)}
               aria-label="Abrir calendário"
             >
               <CalendarIcon className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
        </div>
      </PopoverAnchor>

      <PopoverContent
         className="w-auto p-0 absolute z-50 bg-white border shadow-md rounded-md"
         align="start"
         onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleDateSelect}
          locale={ptBR}
        />
      </PopoverContent>
    </Popover>
  )
}