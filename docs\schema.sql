-- Enable UUID generation (already enabled on Supabase, but keep for local dev)
create extension if not exists "pgcrypto";

--------------------------------------------------------
-- TABELA: contatos
-- (fornecedores, prestadores e clientes)
--------------------------------------------------------
create table public.contatos (
  id              uuid primary key default gen_random_uuid(),
  nome            text not null,
  tipo            text not null check (tipo in ('fornecedor','prestador','cliente')), -- pode ser estendido
  telefone        text,
  email           text,
  empresa         text,
  observacoes     text,
  created_at      timestamptz default now()
);

--------------------------------------------------------
-- TABELA: obras  (projetos / obras em andamento)
--------------------------------------------------------
create table public.obras (
  id                  uuid primary key default gen_random_uuid(),
  nome                text not null,
  descricao           text,
  cliente_id          uuid references public.contatos(id) on delete set null,
  data_inicio         date not null,
  data_fim            date,
  status              text not null default 'em_andamento' check (status in ('planejada','em_andamento','concluida','cancelada')),
  created_at          timestamptz default now()
);

--------------------------------------------------------
-- ENUMS auxiliares
--------------------------------------------------------
create type public.tipo_lancamento_enum as enum ('construtora','terceiros');
create type public.tipo_pagamento_enum  as enum ('avista','parcelado');
create type public.status_aprovacao_enum as enum ('pendente','aprovado','rejeitado');
create type public.status_pagamento_enum as enum ('pendente','pago','vencido');

--------------------------------------------------------
-- TABELA: usuarios  (perfil dos usuários do sistema)
-- 1:1 com auth.users
--------------------------------------------------------
create table public.usuarios (
  id         uuid primary key references auth.users(id) on delete cascade,
  nome       text not null,
  perfil     text default 'padrao' check (perfil in ('admin','padrao')),
  created_at timestamptz default now()
);


--------------------------------------------------------
-- RLS (Row Level Security) – simples, para uso interno único desativado
--------------------------------------------------------
-- Se quiser ativar multi‑tenant depois:
-- alter table public.obras       enable row level security;
-- alter table public.contatos    enable row level security;
-- alter table public.usuarios    enable row level security;
-- (Adicionar policies filtrando por company_id, etc.)

