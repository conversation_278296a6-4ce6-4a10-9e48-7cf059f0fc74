'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Spinner } from '@/components/ui/spinner';
import { AlertCircle, CheckCircle2, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { clearSupabaseCookies, resetSession } from '@/lib/fixCookies';
import { fetchWithAuth } from '@/lib/fetch-with-auth';

export function AuthDiagnostic() {
  const [loading, setLoading] = useState(true);
  const [apiStatus, setApiStatus] = useState<Record<string, boolean>>({});
  const [error, setError] = useState<string | null>(null);

  const endpoints = [
    { name: 'Estatísticas', url: '/api/documentos/estatisticas' },
    { name: 'Documentos Pendentes', url: '/api/documentos?status=pendente' },
    { name: 'Documentos Aprovados', url: '/api/documentos?status=aprovado' },
    { name: 'Documentos Rejeitados', url: '/api/documentos?status=rejeitado' },
  ];

  const checkEndpoints = async () => {
    setLoading(true);
    setError(null);

    const results: Record<string, boolean> = {};

    try {
      await Promise.all(
        endpoints.map(async (endpoint) => {
          try {
            const response = await fetchWithAuth(endpoint.url);
            results[endpoint.name] = response.ok;
          } catch (error) {
            results[endpoint.name] = false;
          }
        })
      );

      setApiStatus(results);
    } catch (error: any) {
      setError(error.message || 'Erro ao verificar endpoints');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkEndpoints();
  }, []);

  const handleFixAuth = () => {
    try {
      // Limpar cookies do Supabase
      const result = clearSupabaseCookies();

      if (result) {
        toast.success('Cookies de autenticação limpos com sucesso');

        // Recarregar a página após um breve delay
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        toast.error('Erro ao limpar cookies');
      }
    } catch (error) {
      toast.error('Erro ao tentar corrigir autenticação');
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">Diagnóstico de API</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={checkEndpoints}
            disabled={loading}
          >
            {loading ? (
              <Spinner className="h-4 w-4 mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Verificar
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {error ? (
          <div className="p-4 bg-destructive/10 rounded-md text-destructive flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            <span>{error}</span>
          </div>
        ) : (
          <div className="space-y-2">
            {endpoints.map((endpoint) => (
              <div key={endpoint.name} className="flex justify-between items-center p-2 border rounded-md">
                <div className="flex items-center">
                  {apiStatus[endpoint.name] === undefined ? (
                    <Spinner className="h-4 w-4 mr-2" />
                  ) : apiStatus[endpoint.name] ? (
                    <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 mr-2 text-destructive" />
                  )}
                  <span>{endpoint.name}</span>
                </div>
                <Badge variant={apiStatus[endpoint.name] ? "outline" : "destructive"}>
                  {apiStatus[endpoint.name] ? "OK" : "Erro"}
                </Badge>
              </div>
            ))}

            <div className="mt-4 flex justify-end">
              <Button
                variant="destructive"
                size="sm"
                onClick={handleFixAuth}
              >
                Corrigir Autenticação
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}