'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { FileDown, Plus, CreditCard } from 'lucide-react'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { LancamentoComParcelas } from '@/types/lancamentos'
import { ListaLancamentos } from './components/lista-lancamentos'
import { DrawerLancamento } from './components/drawer-lancamento'
import { PageHeader } from '@/components/Layout/PageHeader'
import { useLancamentosService } from '@/services/lancamentos'
import { useFiltrosService } from '@/services/filtros'
import { toast } from 'sonner'
import { DrawerPagamentosPendentes } from '../parcelas/components/drawer-pagamentos-pendentes'
import FiltrosContainer from './components/filtros/filtros-container'

export default function LancamentosPage() {
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [selectedLancamento, setSelectedLancamento] = useState<LancamentoComParcelas | undefined>()
  const [version, setVersion] = useState(0)
  const [filtroProjeto, setFiltroProjeto] = useState<string>('todos')
  const [projetos, setProjetos] = useState<string[]>([])
  const [fornecedores, setFornecedores] = useState<string[]>([])
  const [carregandoProjetos, setCarregandoProjetos] = useState(true)
  const [carregandoFornecedores, setCarregandoFornecedores] = useState(true)
  const [loadingLancamentoById, setLoadingLancamentoById] = useState(false)
  const [drawerPagamentosOpen, setDrawerPagamentosOpen] = useState(false)

  const searchParams = useSearchParams()
  const lancamentoId = searchParams.get('id')
  const lancamentosService = useLancamentosService()
  const filtrosService = useFiltrosService()
  
  // Criar referência estável para evitar re-renders desnecessários
  const lancamentosServiceRef = React.useRef(lancamentosService)
  React.useEffect(() => {
    lancamentosServiceRef.current = lancamentosService
  }, [lancamentosService])

  // Carregar projetos disponíveis para o filtro
  useEffect(() => {
    async function carregarProjetos() {
      try {
        setCarregandoProjetos(true)
        const lancamentos = await lancamentosService.getLancamentos()

        // Extrair projetos únicos
        const projetosUnicos = Array.from(new Set(
          lancamentos
            .map(l => l.obras?.nome)
            .filter(Boolean) as string[]
        ))

        setProjetos(projetosUnicos)
      } catch (error) {
        // Erro silencioso ao carregar projetos
      } finally {
        setCarregandoProjetos(false)
      }
    }

    carregarProjetos()
  }, [version])

  // Carregar fornecedores para o filtro
  useEffect(() => {
    async function carregarFornecedores() {
      try {
        setCarregandoFornecedores(true)
        const listaFornecedores = await filtrosService.listarFornecedores()
        setFornecedores(listaFornecedores)
      } catch (error) {
        // Erro silencioso ao carregar fornecedores
      } finally {
        setCarregandoFornecedores(false)
      }
    }

    carregarFornecedores()
  }, [])

  // Carregar e abrir o lançamento quando o ID estiver na URL
  useEffect(() => {
    let isMounted = true;
    let errorCount = 0;
    const MAX_ERRORS = 3; // Limite de erros para evitar loop infinito

    async function carregarLancamentoPorId() {
      if (!lancamentoId) return;

      try {
        setLoadingLancamentoById(true);
        const lancamento = await lancamentosServiceRef.current.getLancamentoById(lancamentoId);

        if (!isMounted) return;

        if (lancamento) {
          setSelectedLancamento(lancamento);
          setDrawerOpen(true);
        } else {
          toast.error(`Lançamento não encontrado ou indisponível`);
          // Limpar URL para evitar loop
          clearUrlParam();
        }
      } catch (error: any) {
        if (!isMounted) return;

        errorCount++;
        toast.error(`Erro ao carregar o lançamento: ${error?.message || 'Erro desconhecido'}`);

        if (errorCount >= MAX_ERRORS) {
          clearUrlParam();
        }
      } finally {
        if (isMounted) {
          setLoadingLancamentoById(false);
        }
      }
    }

    function clearUrlParam() {
      // Remover o ID da URL para evitar loops infinitos
      if (window?.history?.replaceState) {
        window.history.replaceState({}, document.title, '/lancamentos');
      }
    }

    carregarLancamentoPorId();

    return () => {
      isMounted = false;
    };
  }, [lancamentoId]);

  function handleNovoLancamento() {
    setSelectedLancamento(undefined)
    setDrawerOpen(true)
  }

  function handleEditarLancamento(lancamento: LancamentoComParcelas) {
    setSelectedLancamento(lancamento)
    setDrawerOpen(true)
  }

  function handleSubmitSuccess() {
    setVersion(v => v + 1);
  }

  return (
    <div>
      <PageHeader title="Lançamentos" />

      <div className="page-container">
        {/* NOVO LAYOUT: Topo com visualizações + filtros à esquerda, novo lançamento à direita */}
        <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4 mb-4">
          <div className="flex flex-col md:flex-row md:items-center gap-2 w-full">
            <FiltrosContainer
              projetos={projetos}
              fornecedores={fornecedores}
              statusOptions={filtrosService.getStatusOptions()}
            />
          </div>
          <div className="flex justify-end md:justify-end w-full md:w-auto gap-2">
            <Button
              onClick={() => setDrawerPagamentosOpen(true)}
              variant="outline"
              size="sm"
              className="h-8 gap-1"
            >
              <CreditCard className="h-4 w-4" />
              <span>Pagar pendentes</span>
            </Button>
            <Button
              onClick={handleNovoLancamento}
              variant="outline"
              size="sm"
              className="h-8 gap-1"
            >
              <Plus className="h-4 w-4" />
              <span>Novo Lançamento</span>
            </Button>
          </div>
        </div>

        <ListaLancamentos
          onEdit={handleEditarLancamento}
          version={version}
          filtroProjetoGlobal={filtroProjeto}
        />

        <DrawerLancamento
          open={drawerOpen}
          onOpenChange={setDrawerOpen}
          lancamento={selectedLancamento}
          onSubmit={handleSubmitSuccess}
        />
        <DrawerPagamentosPendentes
          open={drawerPagamentosOpen}
          onOpenChange={setDrawerPagamentosOpen}
        />
      </div>
    </div>
  )
}