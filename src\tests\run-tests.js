// Script para executar os testes de lançamentos
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Função para executar um teste
function runTest(testFile) {try {
    // Executar o teste usando ts-node
    execSync(`npx ts-node ${testFile}`, { stdio: 'inherit' });return true;
  } catch (error) {
    console.error(`\n❌ Erro ao executar o teste ${testFile}:`);
    console.error(error.message);
    return false;
  }
}

// Função principal
function main() {// Definir os testes a serem executados
  const tests = [
    path.join(__dirname, 'lancamentos-delete.test.ts'),
    path.join(__dirname, 'lancamentos-bulk-delete.test.ts')
  ];
  
  // Verificar se os arquivos existem
  for (const test of tests) {
    if (!fs.existsSync(test)) {
      console.error(`Arquivo de teste não encontrado: ${test}`);
      process.exit(1);
    }
  }
  
  // Executar os testes
  let successCount = 0;
  let failCount = 0;
  
  for (const test of tests) {
    const success = runTest(test);
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }
  
  // Exibir resumo// Retornar código de saída adequado
  process.exit(failCount > 0 ? 1 : 0);
}

// Executar o script
main();