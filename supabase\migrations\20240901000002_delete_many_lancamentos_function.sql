-- Função para excluir múltiplos lançamentos e suas parcelas
-- Retorna um array com os resultados para cada ID
CREATE OR REPLACE FUNCTION delete_many_lancamentos(p_ids UUID[])
RETURNS TABLE(id UUID, success BOOLEAN, error_message TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_id UUID;
  v_success BOOLEAN;
  v_error_message TEXT;
BEGIN
  -- Para cada ID no array
  FOREACH v_id IN ARRAY p_ids
  LOOP
    BEGIN
      -- Atualizar documentos para remover referência ao lançamento
      UPDATE documentos SET lancamento_id = NULL WHERE lancamento_id = v_id;
      
      -- Excluir parcelas primeiro
      DELETE FROM parcelas WHERE lancamento_id = v_id;
      
      -- Excluir o lançamento
      DELETE FROM lancamentos WHERE id = v_id;
      
      -- Verificar se o lançamento foi excluído
      IF EXISTS (SELECT 1 FROM lancamentos WHERE id = v_id) THEN
        v_success := FALSE;
        v_error_message := 'Lançamento não foi excluído';
      ELSE
        v_success := TRUE;
        v_error_message := NULL;
      END IF;
      
      -- Retornar resultado para este ID
      id := v_id;
      success := v_success;
      error_message := v_error_message;
      RETURN NEXT;
      
    EXCEPTION
      WHEN OTHERS THEN
        -- Em caso de erro, retornar informações sobre o erro
        id := v_id;
        success := FALSE;
        error_message := SQLERRM;
        RETURN NEXT;
    END;
  END LOOP;
  
  RETURN;
END;
$$;

-- Conceder permissões para usuários autenticados
GRANT EXECUTE ON FUNCTION delete_many_lancamentos(UUID[]) TO authenticated;

COMMENT ON FUNCTION delete_many_lancamentos(UUID[]) IS 'Função para excluir múltiplos lançamentos e suas parcelas em transações separadas.';
