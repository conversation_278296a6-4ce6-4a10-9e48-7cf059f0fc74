'use client';

import { useState, useEffect } from 'react';
import { PageHeader } from '@/components/Layout/PageHeader';
import { DocumentosSummaryCards } from './components/documentos-summary-cards';
import { DocumentosUploadFloating } from './components/documentos-upload-floating';
import { DocumentosTabs } from './components/documentos-tabs';
import { DrawerDocumento } from './components/drawer-documento';
import { Documento } from '@/types/documentos';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Trash2, Upload } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { excluirDocumento, getDocumentos, limparDocumentos } from '@/services/documentos';
import { DocumentProcessingNotificationLight } from '@/components/ui/document-processing-notification-light';

export default function DocumentosPage() {
  const [selectedDocumento, setSelectedDocumento] = useState<Documento | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [cleanupDialogOpen, setCleanupDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [statusToClean, setStatusToClean] = useState<'all' | 'pendente' | 'aprovado' | 'rejeitado'>('all');
  
  // Estados para gerenciar os documentos carregados
  const [documentos, setDocumentos] = useState<Documento[]>([]);
  const [documentosLoading, setDocumentosLoading] = useState(true);
  
  // Estados para a notificação de processamento
  const [processingFile, setProcessingFile] = useState<File | null>(null);
  const [processingStep, setProcessingStep] = useState<number>(0);
  const [showProcessingNotification, setShowProcessingNotification] = useState(false);
  const [processingComplete, setProcessingComplete] = useState(false);

  const handleSelectDocumento = (documento: Documento) => {
    setSelectedDocumento(documento);
    setDrawerOpen(true);
  };

  const handleDocumentosLoaded = (novosDocumentos: Documento[]) => {
    setDocumentos(novosDocumentos);
    setDocumentosLoading(false);
  };

  const handleUploadSuccess = (file?: File, step?: number, isComplete?: boolean) => {
    // Atualizar os estados de processamento para a notificação
    if (file) {
      setProcessingFile(file);
      setShowProcessingNotification(true);
    }

    if (typeof step === 'number') {
      setProcessingStep(step);
    }

    if (isComplete !== undefined) {
      setProcessingComplete(isComplete);

      // Se o processamento estiver completo, esconder a notificação após um tempo
      // e só então atualizar a lista de documentos
      if (isComplete) {
        setTimeout(() => {
          // Incrementar o trigger para forçar o recarregamento dos documentos
          // somente após o processamento estar completo
          setRefreshTrigger(prev => prev + 1);

          // Esconder a notificação após mais um tempo
          setTimeout(() => {
            setShowProcessingNotification(false);
          }, 1500);
        }, 1000);
      }
    }
  };

  const handleDrawerSuccess = async () => {
    // Buscar o documento atualizado primeiro
    if (selectedDocumento) {
      try {
        // Adicionar um timestamp para evitar cache
        const timestamp = new Date().getTime();

        // Buscar o documento atualizado
        const response = await fetch(`/api/documentos/${selectedDocumento.id}?forceServiceRole=true&t=${timestamp}`, {
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (response.ok) {
          const data = await response.json();

          // Atualizar o documento selecionado
          setSelectedDocumento(data);

          // Pequena pausa para garantir que o estado foi atualizado
          await new Promise(resolve => setTimeout(resolve, 200));

          // Incrementar o trigger para forçar o recarregamento dos documentos
          setRefreshTrigger(prev => prev + 1);
        } else {
          // Incrementar o trigger mesmo em caso de erro
          setRefreshTrigger(prev => prev + 1);
        }
      } catch (error) {
        // Incrementar o trigger mesmo em caso de erro
        setRefreshTrigger(prev => prev + 1);
      }
    } else {
      // Se não houver documento selecionado, apenas incrementar o trigger
      setRefreshTrigger(prev => prev + 1);
    }
  };

  const handleCleanupDocumentos = async () => {
    setIsDeleting(true);

    try {
      // Usar a nova API para limpar documentos
      const result = await limparDocumentos(statusToClean);

      // Mostrar mensagem apropriada baseada no resultado
      if (result.success > 0 && result.errors === 0) {
        toast.success(`${result.success} documento(s) excluído(s) com sucesso!`);
      } else if (result.success > 0 && result.errors > 0) {
        toast.warning(`${result.success} documento(s) excluído(s) com sucesso, mas ${result.errors} falhou(aram).`);
      } else if (result.total === 0) {
        toast.info('Não há documentos para excluir');
      } else {
        toast.error(`Falha ao excluir documentos. Tente novamente.`);
      }

      // Pequena pausa para garantir que os dados foram atualizados no banco
      await new Promise(resolve => setTimeout(resolve, 500));

      // Atualizar a lista
      setRefreshTrigger(prev => prev + 1);
    } catch (error: any) {
      toast.error(error.message || 'Erro ao excluir documentos');
    } finally {
      setIsDeleting(false);
      setCleanupDialogOpen(false);
    }
  };

  const openCleanupDialog = (status: 'all' | 'pendente' | 'aprovado' | 'rejeitado') => {
    setStatusToClean(status);
    setCleanupDialogOpen(true);
  };

  return (
    <div>
      <PageHeader
        title="Documentos"
        description="Gerencie seus documentos e extraia informações automaticamente"
        actions={
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Encontrar o input de arquivo do componente flutuante
                const input = document.getElementById('file-upload-floating');
                if (input) {
                  input.click();
                } else {
                  toast.info("Clique no botão de upload no canto inferior direito");
                }
              }}
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => openCleanupDialog('all')}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Limpar Documentos
            </Button>
          </div>
        }
      />

      <div className="space-y-6">
        {/* Cards de resumo com dados otimizados */}
        <DocumentosSummaryCards 
          documentos={documentos}
          loading={documentosLoading}
        />

        {/* Componente de upload flutuante */}
        <DocumentosUploadFloating onUploadSuccess={handleUploadSuccess} />

        <Separator />

        {/* Tabs e tabelas de documentos */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-lg font-medium">Documentos</h2>
          </div>
          <DocumentosTabs
            onSelectDocumento={handleSelectDocumento}
            refreshTrigger={refreshTrigger}
            onDocumentosLoaded={handleDocumentosLoaded}
          />
        </div>

        {/* Drawer de detalhes do documento */}
        <DrawerDocumento
          open={drawerOpen}
          onOpenChange={setDrawerOpen}
          documento={selectedDocumento}
          onSuccess={handleDrawerSuccess}
        />

        {/* Dialog de confirmação para limpeza */}
        <AlertDialog open={cleanupDialogOpen} onOpenChange={setCleanupDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {statusToClean === 'all'
                  ? 'Limpar todos os documentos'
                  : statusToClean === 'pendente'
                    ? 'Limpar documentos da Inbox'
                    : `Limpar documentos ${statusToClean}s`}
              </AlertDialogTitle>
              <AlertDialogDescription>
                Tem certeza que deseja excluir
                {statusToClean === 'all'
                  ? ' todos os documentos'
                  : statusToClean === 'pendente'
                    ? ' todos os documentos da Inbox'
                    : ` todos os documentos ${statusToClean}s`}?
                Esta ação não pode ser desfeita.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isDeleting}>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={(e) => {
                  e.preventDefault();
                  handleCleanupDocumentos();
                }}
                disabled={isDeleting}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isDeleting ? (
                  <>
                    <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                    Excluindo...
                  </>
                ) : (
                  'Excluir'
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      {/* Renderizar a notificação de processamento no final da página */}
      <DocumentProcessingNotificationLight
        fileName={processingFile?.name || "documento.pdf"}
        currentStep={processingStep}
        isComplete={processingComplete}
        onClose={() => setShowProcessingNotification(false)}
        isVisible={showProcessingNotification && !!processingFile}
      />

    </div>
  );
}