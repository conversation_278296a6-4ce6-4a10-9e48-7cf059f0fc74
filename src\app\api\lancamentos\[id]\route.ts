import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';
import { Database } from '@/types/supabase';

export const dynamic = 'force-dynamic';

// GET /api/lancamentos/[id] - Busca um lançamento específico
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = resolvedParams.id;

    if (!id) {
      return NextResponse.json({ error: 'ID do lançamento é obrigatório' }, { status: 400 });
    }

    const supabase = await getSupabaseRouteClient();

    const { data, error } = await supabase
      .from('lancamentos')
      .select(`
        *,
        contato:contatos(*),
        obra:obras(*),
        parcelas(*),
        documentos(*)
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Erro ao buscar lançamento:', error);
      return NextResponse.json({ error: 'Erro ao buscar lançamento' }, { status: 500 });
    }

    if (!data) {
      return NextResponse.json({ error: 'Lançamento não encontrado' }, { status: 404 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Erro interno:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// PUT /api/lancamentos/[id] - Atualiza um lançamento específico
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = resolvedParams.id;
    const body = await request.json();

    if (!id) {
      return NextResponse.json({ error: 'ID do lançamento é obrigatório' }, { status: 400 });
    }

    // Validações básicas
    if (body.valor_total !== undefined && body.valor_total <= 0) {
      return NextResponse.json(
        { error: 'Valor total deve ser maior que zero' },
        { status: 400 }
      );
    }

    const supabase = await getSupabaseRouteClient();

    // Separar dados de lançamento das parcelas se houver
    const { parcelas, ...dadosLancamento } = body;

    // Primeiro, atualizar o lançamento
    const { data: lancamentoAtualizado, error: errorLancamento } = await supabase
      .from('lancamentos')
      .update(dadosLancamento)
      .eq('id', id)
      .select()
      .single();

    if (errorLancamento) {
      console.error('Erro ao atualizar lançamento:', errorLancamento);
      return NextResponse.json({ error: 'Erro ao atualizar lançamento' }, { status: 500 });
    }

    // Se há parcelas para atualizar
    if (parcelas && Array.isArray(parcelas)) {
      // Primeiro, remover parcelas existentes
      await supabase
        .from('parcelas')
        .delete()
        .eq('lancamento_id', id);

      // Depois, inserir as novas parcelas
      if (parcelas.length > 0) {
        const parcelasComId = parcelas.map((parcela: any) => ({
          ...parcela,
          lancamento_id: id
        }));

        const { error: errorParcelas } = await supabase
          .from('parcelas')
          .insert(parcelasComId);

        if (errorParcelas) {
          console.error('Erro ao atualizar parcelas:', errorParcelas);
          // Continuar mesmo com erro nas parcelas
        }
      }
    }

    // Buscar o lançamento completo atualizado
    const { data: lancamentoCompleto, error: errorBusca } = await supabase
      .from('lancamentos')
      .select(`
        *,
        contato:contatos(*),
        obra:obras(*),
        parcelas(*),
        documentos(*)
      `)
      .eq('id', id)
      .single();

    if (errorBusca) {
      console.error('Erro ao buscar lançamento atualizado:', errorBusca);
      // Retornar apenas os dados básicos do lançamento
      return NextResponse.json(lancamentoAtualizado);
    }

    return NextResponse.json(lancamentoCompleto);
  } catch (error) {
    console.error('Erro interno:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// PATCH /api/lancamentos/[id] - Atualiza parcialmente um lançamento específico
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = resolvedParams.id;
    const body = await request.json();

    if (!id) {
      return NextResponse.json({ error: 'ID do lançamento é obrigatório' }, { status: 400 });
    }

    // Validações básicas
    if (body.valor_total !== undefined && body.valor_total <= 0) {
      return NextResponse.json(
        { error: 'Valor total deve ser maior que zero' },
        { status: 400 }
      );
    }

    const supabase = await getSupabaseRouteClient();

    // Para PATCH, apenas atualizar os campos fornecidos (sem parcelas)
    const { parcelas, ...dadosLancamento } = body;

    // Atualizar apenas os campos fornecidos
    const { data: lancamentoAtualizado, error: errorLancamento } = await supabase
      .from('lancamentos')
      .update(dadosLancamento)
      .eq('id', id)
      .select()
      .single();

    if (errorLancamento) {
      console.error('Erro ao atualizar lançamento:', errorLancamento);
      return NextResponse.json({ error: 'Erro ao atualizar lançamento' }, { status: 500 });
    }

    return NextResponse.json(lancamentoAtualizado);
  } catch (error) {
    console.error('Erro interno:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// DELETE /api/lancamentos/[id] - Remove um lançamento específico
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = resolvedParams.id;

    if (!id) {
      return NextResponse.json({ error: 'ID do lançamento é obrigatório' }, { status: 400 });
    }

    const supabase = await getSupabaseRouteClient();

    // Primeiro, verificar se o lançamento existe
    const { data: lancamento, error: errorBusca } = await supabase
      .from('lancamentos')
      .select('id')
      .eq('id', id)
      .single();

    if (errorBusca) {
      if (errorBusca.code === 'PGRST116') {
        return NextResponse.json({ error: 'Lançamento não encontrado' }, { status: 404 });
      }
      console.error('Erro ao buscar lançamento:', errorBusca);
      return NextResponse.json({ error: 'Erro ao buscar lançamento' }, { status: 500 });
    }

    // Remover parcelas associadas primeiro (se houver)
    await supabase
      .from('parcelas')
      .delete()
      .eq('lancamento_id', id);

    // Remover o lançamento
    const { error: errorRemocao } = await supabase
      .from('lancamentos')
      .delete()
      .eq('id', id);

    if (errorRemocao) {
      console.error('Erro ao remover lançamento:', errorRemocao);
      return NextResponse.json({ error: 'Erro ao remover lançamento' }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Lançamento removido com sucesso' });
  } catch (error) {
    console.error('Erro interno:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}