'use client';

import { useEffect, useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Edit2, Trash2 } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { supabase } from '@/lib/supabase';
import { formatCurrency } from '@/lib/utils';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Spinner } from '@/components/ui/spinner';
import { toast } from 'sonner';

interface Contrato {
  id: string;
  numero: string;
  descricao: string;
  valor: number;
  data_inicio: string;
  data_fim: string;
  status: string;
  created_at: string;
}

interface ListaContratosProps {
  onEdit: (id: string) => void;
}

export function ListaContratos({ onEdit }: ListaContratosProps) {
  const [contratos, setContratos] = useState<Contrato[]>([]);
  const [loading, setLoading] = useState(true);
  const [contratoParaExcluir, setContratoParaExcluir] = useState<string | null>(null);
  const [excluindo, setExcluindo] = useState(false);

  useEffect(() => {
    loadContratos();
  }, []);

  async function loadContratos() {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('contratos')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setContratos(data || []);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      toast.error(`Erro ao carregar contratos: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }

  async function handleExcluir() {
    if (!contratoParaExcluir) return;

    try {
      setExcluindo(true);
      const { error } = await supabase
        .from('contratos')
        .delete()
        .eq('id', contratoParaExcluir);

      if (error) throw error;

      toast.success('Contrato excluído com sucesso');
      loadContratos();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      toast.error(`Erro ao excluir contrato: ${errorMessage}`);
    } finally {
      setExcluindo(false);
      setContratoParaExcluir(null);
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <Spinner className="w-6 h-6" />
      </div>
    );
  }

  return (
    <>
      <div className="border rounded-md">
        <Table>
          <TableHeader className="bg-muted">
            <TableRow>
              <TableHead>Número</TableHead>
              <TableHead>Descrição</TableHead>
              <TableHead>Valor</TableHead>
              <TableHead>Data Início</TableHead>
              <TableHead>Data Fim</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[100px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {contratos.map((contrato) => (
              <TableRow key={contrato.id}>
                <TableCell className="font-medium">{contrato.numero}</TableCell>
                <TableCell>{contrato.descricao}</TableCell>
                <TableCell>{formatCurrency(contrato.valor)}</TableCell>
                <TableCell>{format(new Date(contrato.data_inicio), 'dd/MM/yy', { locale: ptBR })}</TableCell>
                <TableCell>{format(new Date(contrato.data_fim), 'dd/MM/yy', { locale: ptBR })}</TableCell>
                <TableCell>{contrato.status}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onEdit(contrato.id)}
                      className="h-7 w-7"
                    >
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setContratoParaExcluir(contrato.id)}
                      className="h-7 w-7"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <Dialog open={!!contratoParaExcluir} onOpenChange={() => setContratoParaExcluir(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Excluir contrato</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir este contrato? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setContratoParaExcluir(null)}
              disabled={excluindo}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleExcluir}
              disabled={excluindo}
            >
              {excluindo ? (
                <Spinner className="mr-2 h-4 w-4" />
              ) : (
                <Trash2 className="mr-2 h-4 w-4" />
              )}
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}