import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação - Padrão recomendado para Route Handlers
    let supabase;
    let session;

    try {
      supabase = await getSupabaseRouteClient();
      const { data } = await supabase.auth.getSession();
      session = data.session;

      if (!session) {
        // Falha silenciosa para permitir operação com API
      }
    } catch (authError) {
      // Continuar mesmo com erro de autenticação
    }

    // Processar o arquivo enviado
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { message: 'Arquivo não encontrado' },
        { status: 400 }
      );
    }

    // Ler o arquivo como array buffer
    const arrayBuffer = await file.arrayBuffer();
    const workbook = XLSX.read(arrayBuffer, { type: 'array' });

    // Obter a primeira planilha
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];

    // Converter para JSON
    const data = XLSX.utils.sheet_to_json(worksheet);

    if (data.length === 0) {
      return NextResponse.json(
        { message: 'A planilha está vazia' },
        { status: 400 }
      );
    }

    // Validar estrutura dos dados
    const requiredFields = ['obra_id', 'tipo_lancamento', 'valor_total', 'data_competencia',
                            'forma_pagamento', 'descricao'];

    // Verificar se todos os campos obrigatórios estão presentes
    const firstRow = data[0] as Record<string, unknown>;
    const missingFields = requiredFields.filter(field => !Object.keys(firstRow).includes(field));

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          message: `Campos obrigatórios ausentes na planilha: ${missingFields.join(', ')}`
        },
        { status: 400 }
      );
    }

    // Array para armazenar lançamentos processados
    const lancamentosProcessados = [];
    const erros = [];

    // Processar cada linha da planilha
    for (let i = 0; i < data.length; i++) {
      const row = data[i] as any;

      try {
        // Verificar se os campos obrigatórios têm valores
        for (const field of requiredFields) {
          if (!row[field]) {
            throw new Error(`Campo '${field}' ausente na linha ${i + 2}`);
          }
        }

        // Converter data_competencia para formato ISO
        let dataCompetencia = row.data_competencia;

        // Se a data estiver em formato Excel (número)
        if (typeof dataCompetencia === 'number') {
          dataCompetencia = XLSX.SSF.format('yyyy-mm-dd', dataCompetencia);
        }
        // Se estiver em formato brasileiro DD/MM/YYYY
        else if (typeof dataCompetencia === 'string' && dataCompetencia.includes('/')) {
          const [day, month, year] = dataCompetencia.split('/').map(Number);
          dataCompetencia = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        }

        // Validar e converter valor_total
        let valorTotal = row.valor_total;
        if (typeof valorTotal === 'string') {
          // Remover formatação monetária e converter para número
          valorTotal = parseFloat(valorTotal.replace(/[^0-9.,]/g, '').replace(',', '.'));
        }

        if (isNaN(valorTotal)) {
          throw new Error(`Valor total inválido na linha ${i + 2}`);
        }

        // Criar objeto do lançamento
        const lancamento = {
          obra_id: row.obra_id,
          tipo_lancamento: row.tipo_lancamento,
          valor_total: valorTotal,
          data_competencia: dataCompetencia,
          forma_pagamento: row.forma_pagamento,
          descricao: row.descricao,
          contato_id: row.contato_id || null,
          observacoes: row.observacoes || null,
          status: row.status || 'Em aberto',
        };

        // Verificar se o objeto parcelas existe
        let parcelas = [];

        // Se tivermos informações sobre parcelas na planilha
        if (row.numero_parcelas && row.valor_parcela && row.data_vencimento) {
          const numeroParcelas = parseInt(row.numero_parcelas);

          // Converter data_vencimento para formato ISO
          let dataVencimento = row.data_vencimento;

          // Se a data estiver em formato Excel (número)
          if (typeof dataVencimento === 'number') {
            dataVencimento = XLSX.SSF.format('yyyy-mm-dd', dataVencimento);
          }
          // Se estiver em formato brasileiro DD/MM/YYYY
          else if (typeof dataVencimento === 'string' && dataVencimento.includes('/')) {
            const [day, month, year] = dataVencimento.split('/').map(Number);
            dataVencimento = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
          }

          // Validar e converter valor_parcela
          let valorParcela = row.valor_parcela;
          if (typeof valorParcela === 'string') {
            // Remover formatação monetária e converter para número
            valorParcela = parseFloat(valorParcela.replace(/[^0-9.,]/g, '').replace(',', '.'));
          }

          if (isNaN(valorParcela)) {
            throw new Error(`Valor de parcela inválido na linha ${i + 2}`);
          }

          // Gerar parcelas
          for (let j = 0; j < numeroParcelas; j++) {
            // Calcular nova data de vencimento adicionando meses
            const dataObj = new Date(dataVencimento);
            dataObj.setMonth(dataObj.getMonth() + j);
            const novaDataVencimento = dataObj.toISOString().split('T')[0];

            parcelas.push({
              numero: j + 1,
              valor: valorParcela,
              vencimento: novaDataVencimento,
              status: 'pendente'
            });
          }
        }

        lancamentosProcessados.push({
          lancamento,
          parcelas
        });
      } catch (error) {
        erros.push({
          linha: i + 2,
          mensagem: error instanceof Error ? error.message : 'Erro desconhecido'
        });
      }
    }

    // Se houver erros, retornar antes de inserir no banco
    if (erros.length > 0 && lancamentosProcessados.length === 0) {
      return NextResponse.json(
        {
          message: 'Nenhum lançamento válido encontrado',
          erros
        },
        { status: 400 }
      );
    }

    // Verificar se temos acesso ao supabase
    if (!supabase) {
      return NextResponse.json({
        message: 'Importação processada, mas não foi possível salvar no banco de dados. Problema de autenticação.',
        lancamentos: lancamentosProcessados.length,
        erros: erros.length > 0 ? erros : undefined
      }, { status: 401 });
    }

    // Inserir lançamentos no banco de dados
    const lancamentosInseridos = [];
    const errosInsercao = [];

    for (const item of lancamentosProcessados) {
      try {
        // Inserir lançamento
        const { data: lancamentoData, error: lancamentoError } = await supabase
          .from('lancamentos')
          .insert(item.lancamento)
          .select('id')
          .single();

        if (lancamentoError) throw lancamentoError;

        // Se temos parcelas, inserir cada uma
        if (item.parcelas.length > 0) {
          const parcelasComLancamentoId = item.parcelas.map(parcela => ({
            ...parcela,
            lancamento_id: lancamentoData.id
          }));

          const { error: parcelasError } = await supabase
            .from('parcelas')
            .insert(parcelasComLancamentoId);

          if (parcelasError) throw parcelasError;
        }

        lancamentosInseridos.push(lancamentoData.id);
      } catch (error) {
        errosInsercao.push({
          lancamento: item.lancamento,
          mensagem: error instanceof Error ? error.message : 'Erro desconhecido'
        });
      }
    }

    // Retornar resultado
    return NextResponse.json({
      imported: lancamentosInseridos.length,
      failed: errosInsercao.length + erros.length,
      errors: erros.length > 0 ? erros : undefined,
      insertionErrors: errosInsercao.length > 0 ? errosInsercao : undefined
    });

  } catch (error) {
    return NextResponse.json(
      { message: error instanceof Error ? error.message : 'Erro ao processar a planilha' },
      { status: 500 }
    );
  }
}

// Aumentar limite de tamanho do payload para 10MB
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb',
    },
  },
};