'use client';

import { useState, useEffect, useRef } from 'react';
import { Upload, FileText, CheckCircle2, AlertCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { uploadDocumento, getDocumentoById } from '@/services/documentos';
import { analisarDocumento } from '@/services/ai-analysis';
import { supabase } from '@/lib/supabase/client';

interface DrawerDropzoneProps {
  onDrop: (files: File[]) => void;
  onEnrichmentComplete?: () => void;
  documentoId?: string;
}

interface ProcessingFile {
  file: File;
  status: 'uploading' | 'analyzing' | 'completed' | 'error';
  progress: number;
  error?: string;
}

export function DrawerDropzone({ onDrop, onEnrichmentComplete, documentoId }: DrawerDropzoneProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [processingFiles, setProcessingFiles] = useState<ProcessingFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const dropzoneRef = useRef<HTMLDivElement>(null);

  console.log('🎯 DrawerDropzone: Renderizado para documento:', documentoId);
  console.log('🎯 DrawerDropzone: Body tem data-drawer-documento-open?', document.body.hasAttribute('data-drawer-documento-open'));

  // Debug do elemento quando renderizar e adicionar listener nativo
  useEffect(() => {
    if (dropzoneRef.current) {
      console.log('🎯 DrawerDropzone: Elemento ref disponível:', dropzoneRef.current);
      console.log('🎯 DrawerDropzone: Posição:', dropzoneRef.current.getBoundingClientRect());

      const element = dropzoneRef.current;

      // Adicionar listener nativo como backup
      const nativeDropHandler = (e: DragEvent) => {
        console.log('🎯 DrawerDropzone: NATIVE DROP HANDLER TRIGGERED!');
        console.log('🎯 Native drop - files:', e.dataTransfer?.files?.length || 0);

        // Converter para React event e chamar handler
        const reactEvent = {
          preventDefault: () => e.preventDefault(),
          stopPropagation: () => e.stopPropagation(),
          dataTransfer: e.dataTransfer,
          target: e.target,
          currentTarget: e.currentTarget
        } as React.DragEvent<HTMLDivElement>;

        handleDrop(reactEvent);
      };

      element.addEventListener('drop', nativeDropHandler);

      return () => {
        element.removeEventListener('drop', nativeDropHandler);
      };
    }
  }, []);

  // Prevenir comportamento padrão do navegador de forma simples
  useEffect(() => {
    const handleGlobalDragOver = (e: DragEvent) => {
      // Verificar se o evento está no dropzone
      const isDropzoneTarget = (e.target as Element)?.closest('[data-dropzone="true"]');
      if (!isDropzoneTarget) {
        e.preventDefault();
        e.stopPropagation();
      }
    };

    const handleGlobalDrop = (e: DragEvent) => {
      console.log('🎯 GLOBAL DROP INTERCEPTADO!', e.target);
      console.log('🎯 Global drop - dataTransfer:', e.dataTransfer);
      console.log('🎯 Global drop - files:', e.dataTransfer?.files?.length || 0);

      // Verificar se o drop foi no dropzone usando coordenadas
      const dropzoneElement = dropzoneRef.current;
      if (dropzoneElement) {
        const rect = dropzoneElement.getBoundingClientRect();
        const isInDropzone = e.clientX >= rect.left &&
                           e.clientX <= rect.right &&
                           e.clientY >= rect.top &&
                           e.clientY <= rect.bottom;

        console.log('🎯 Global drop - coordenadas:', { x: e.clientX, y: e.clientY });
        console.log('🎯 Global drop - dropzone rect:', rect);
        console.log('🎯 Global drop - está no dropzone?', isInDropzone);

        if (!isInDropzone) {
          // SEMPRE prevenir primeiro para garantir que não abra em nova aba
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          console.log('🎯 Global drop prevented - fora do dropzone');
        } else {
          console.log('🎯 Global drop - permitindo processamento no dropzone');
          // NÃO prevenir - deixar o evento chegar ao dropzone
          // O handleDrop do componente processará os arquivos
        }
      } else {
        // Se não conseguir encontrar o dropzone, prevenir por segurança
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        console.log('🎯 Global drop prevented - dropzone não encontrado');
      }
    };

    // Adicionar listeners com capture: true para maior prioridade
    document.addEventListener('dragover', handleGlobalDragOver, { capture: true });
    document.addEventListener('drop', handleGlobalDrop, { capture: true });

    console.log('🎯 Global listeners adicionados para DrawerDropzone');

    return () => {
      document.removeEventListener('dragover', handleGlobalDragOver, { capture: true });
      document.removeEventListener('drop', handleGlobalDrop, { capture: true });
      console.log('🎯 Global listeners removidos para DrawerDropzone');
    };
  }, []);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('🎯 DrawerDropzone: Drag Over no componente');
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('🎯 DrawerDropzone: Drag Leave no componente');
    setIsDragOver(false);
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    console.log('🎯 DrawerDropzone: COMPONENT DROP EVENT TRIGGERED!');
    console.log('📁 DrawerDropzone: DROP EVENT!', e.dataTransfer?.files?.length || 0, 'arquivos');
    console.log('📁 DrawerDropzone: Event target:', e.target);
    console.log('📁 DrawerDropzone: Current target:', e.currentTarget);
    console.log('📁 DrawerDropzone: DataTransfer:', e.dataTransfer);
    console.log('📁 DrawerDropzone: Document ID:', documentoId);

    // Prevenir comportamento padrão
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    // Verificar se dataTransfer existe
    if (!e.dataTransfer || !e.dataTransfer.files) {
      console.error('❌ DrawerDropzone: DataTransfer ou files não disponível');
      toast.error('Erro: Não foi possível acessar os arquivos');
      onDrop([]);
      return;
    }

    const files = Array.from(e.dataTransfer.files);
    console.log('📁 DrawerDropzone: Arquivos recebidos:', files.map(f => `${f.name} (${f.type})`));

    if (files.length === 0) {
      console.log('📁 DrawerDropzone: Nenhum arquivo válido');
      toast.info('Nenhum arquivo foi detectado');
      onDrop([]); // Notificar o pai que o drop terminou
      return;
    }

    // Filtrar apenas arquivos válidos
    const validFiles = files.filter(file => {
      const isValid =
        file.type === 'application/pdf' ||
        file.type === 'image/png' ||
        file.type === 'image/jpeg' ||
        file.name.toLowerCase().endsWith('.pdf') ||
        file.name.toLowerCase().endsWith('.png') ||
        file.name.toLowerCase().endsWith('.jpg') ||
        file.name.toLowerCase().endsWith('.jpeg');

      if (!isValid) {
        console.log(`❌ Arquivo rejeitado: ${file.name} (tipo: ${file.type})`);
        toast.error(`Arquivo não suportado: ${file.name}. Apenas PDF, PNG e JPEG são aceitos.`);
      } else {
        console.log(`✅ Arquivo aceito: ${file.name} (tipo: ${file.type})`);
      }

      return isValid;
    });

    console.log('📁 DrawerDropzone: Arquivos válidos:', validFiles.length, 'de', files.length);

    if (validFiles.length === 0) {
      toast.warning('Nenhum arquivo válido foi encontrado');
      onDrop([]); // Notificar o pai que o drop terminou
      return;
    }

    // Verificar se temos documento ID
    if (!documentoId) {
      console.error('❌ DrawerDropzone: Document ID não fornecido');
      toast.error('Erro: ID do documento não encontrado');
      onDrop([]);
      return;
    }

    // Processar enriquecimento
    console.log('🚀 Iniciando processamento de enriquecimento...');
    await processEnrichment(validFiles);

    // Notificar o pai que o drop foi processado (sem arquivos para evitar loop)
    onDrop([]);
  };

  const processEnrichment = async (files: File[]) => {
    try {
      console.log('🚀 Iniciando processamento de', files.length, 'arquivo(s)');
      console.log('🆔 Document ID:', documentoId);

      if (!documentoId) {
        console.error('❌ ID do documento não fornecido');
        toast.error('Erro: ID do documento não encontrado');
        return;
      }

      setIsProcessing(true);

      // Inicializar arquivos no estado de processamento
      const initialProcessingFiles = files.map(file => ({
        file,
        status: 'uploading' as const,
        progress: 0
      }));
      setProcessingFiles(initialProcessingFiles);

      // Mostrar toast de início do processamento
      toast.info(`Processando ${files.length} arquivo(s)...`);

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        console.log('📁 Processando arquivo:', file.name, 'Tamanho:', file.size, 'bytes', 'Tipo:', file.type);

        // Atualizar status para uploading
        setProcessingFiles(prev => prev.map((item, index) =>
          index === i ? { ...item, status: 'uploading', progress: 25 } : item
        ));

        try {
          console.log('📤 Enviando arquivo para upload...');

          const formData = new FormData();
          formData.append('file', file);

          const response = await fetch('/api/documentos', {
            method: 'POST',
            body: formData,
          });

          // Atualizar progresso
          setProcessingFiles(prev => prev.map((item, index) =>
            index === i ? { ...item, progress: 75 } : item
          ));

          if (!response.ok) {
            console.error('❌ Erro no upload - Status:', response.status);
            const errorText = await response.text();
            console.error('❌ Detalhes do erro:', errorText);

            // Marcar como erro
            setProcessingFiles(prev => prev.map((item, index) =>
              index === i ? { ...item, status: 'error', error: `Erro ${response.status}` } : item
            ));

            toast.error(`Erro ao processar ${file.name}: ${response.status}`);
            continue;
          }

          const result = await response.json();
          console.log('✅ Arquivo enviado com sucesso:', result);

          // Marcar como completo
          setProcessingFiles(prev => prev.map((item, index) =>
            index === i ? { ...item, status: 'completed', progress: 100 } : item
          ));

          toast.success(`✅ ${file.name} anexado com sucesso!`);

        } catch (error) {
          console.error('❌ Erro ao processar arquivo:', file.name, error);

          // Marcar como erro
          setProcessingFiles(prev => prev.map((item, index) =>
            index === i ? { ...item, status: 'error', error: 'Erro de rede' } : item
          ));

          toast.error(`Erro ao processar ${file.name}`);
        }
      }

      // Aguardar um pouco para mostrar o resultado
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Chamar callback de enriquecimento completo
      if (onEnrichmentComplete) {
        onEnrichmentComplete();
      }

      // Limpar estado de processamento
      setProcessingFiles([]);
      setIsProcessing(false);

    } catch (error) {
      console.error('❌ Erro geral no processamento:', error);
      toast.error('Erro no processamento dos arquivos');
      setProcessingFiles([]);
      setIsProcessing(false);
    }
  };

  const associateDocumentToParent = async (documentoId: string, parentDocumentoId: string) => {
    try {
      // Implementar lógica de associação conforme necessário
      // Por exemplo, atualizar um campo de relacionamento ou criar entrada em tabela de anexos
      const response = await fetch(`/api/documentos/${documentoId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documento_relacionado_id: parentDocumentoId
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao associar documento');
      }
    } catch (error) {
      console.error('Erro ao associar documento:', error);
      // Não falhar o processo por causa disso
    }
  };

  const updatePaymentStatus = async (documentoId: string, file: File) => {
    try {
      console.log('🔄 Atualizando status de pagamento para documento:', documentoId);
      
      // Detectar se é um comprovante de pagamento baseado no nome do arquivo
      const isComprovante = file.name.toLowerCase().includes('comprovante') ||
                           file.name.toLowerCase().includes('pago') ||
                           file.name.toLowerCase().includes('pagamento') ||
                           file.name.toLowerCase().includes('transferencia') ||
                           file.name.toLowerCase().includes('pix') ||
                           file.type.includes('image'); // Assumir que imagens são comprovantes
      
      if (!isComprovante) {
        console.log('📄 Arquivo não identificado como comprovante de pagamento');
        toast.info('Documento anexado com sucesso!');
        return;
      }

      console.log('📄 Comprovante detectado, buscando lançamentos relacionados...');
      
      // Abordagem simplificada: Buscar todos os lançamentos pendentes recentes
      try {
        console.log('🔍 Buscando todos os lançamentos...');
        const lancamentosResponse = await fetch('/api/lancamentos');
        
        if (!lancamentosResponse.ok) {
          console.log('❌ Erro ao buscar lançamentos - Status:', lancamentosResponse.status);
          toast.info('Comprovante anexado com sucesso!');
          return;
        }
        
        const { data: todosLancamentos } = await lancamentosResponse.json();
        console.log('📊 Total de lançamentos encontrados:', todosLancamentos?.length || 0);
        
        if (!todosLancamentos || todosLancamentos.length === 0) {
          console.log('⚠️ Nenhum lançamento encontrado no sistema');
          toast.info('Comprovante anexado com sucesso!');
          return;
        }
        
        // Filtrar lançamentos pendentes criados recentemente (últimas 48 horas)
        const agora = new Date();
        const limite = new Date(agora.getTime() - 48 * 60 * 60 * 1000); // 48 horas atrás
        
        const lancamentosPendentes = todosLancamentos.filter((lancamento: any) => {
          const statusPendente = lancamento.status === 'Em aberto';
          const criadoRecentemente = new Date(lancamento.created_at) > limite;
          
          console.log(`📋 Lançamento ${lancamento.id}: status=${lancamento.status}, criado=${lancamento.created_at}, recente=${criadoRecentemente}`);
          
          return statusPendente && criadoRecentemente;
        });
        
        console.log('🎯 Lançamentos pendentes recentes encontrados:', lancamentosPendentes.length);
        
        if (lancamentosPendentes.length === 0) {
          console.log('⚠️ Nenhum lançamento pendente recente encontrado');
          console.log('📊 Resumo dos lançamentos encontrados:');
          todosLancamentos.forEach((lancamento: any, index: number) => {
            console.log(`  ${index + 1}. ID: ${lancamento.id}, Status: ${lancamento.status}, Criado: ${lancamento.created_at}`);
          });
          toast.info('Comprovante anexado, mas nenhum lançamento pendente encontrado para atualizar.');
          return;
        }
        
        // Atualizar todos os lançamentos pendentes recentes
        await updateLancamentosStatus(lancamentosPendentes);
        
      } catch (error) {
        console.error('❌ Erro na busca de lançamentos:', error);
        toast.info('Comprovante anexado com sucesso!');
      }
      
    } catch (error) {
      console.error('❌ Erro geral ao atualizar status de pagamento:', error);
      toast.info('Documento anexado com sucesso!');
    }
  };

  const updateLancamentosStatus = async (lancamentos: any[]) => {
    try {
      console.log('🔄 Atualizando status de', lancamentos.length, 'lançamentos...');
      
      const updatePromises = lancamentos.map(async (lancamento) => {
        try {
          console.log(`📝 Atualizando lançamento ${lancamento.id} para status "Pago"`);
          
          const response = await fetch(`/api/lancamentos/${lancamento.id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              status: 'Pago',
              data_pagamento: new Date().toISOString().split('T')[0]
            }),
          });
          
          if (!response.ok) {
            console.error(`❌ Erro ao atualizar lançamento ${lancamento.id} - Status:`, response.status);
            const errorText = await response.text();
            console.error('❌ Detalhes do erro:', errorText);
            return { success: false, id: lancamento.id, error: errorText };
          }
          
          console.log(`✅ Lançamento ${lancamento.id} atualizado com sucesso`);
          return { success: true, id: lancamento.id };
          
        } catch (error) {
          console.error(`❌ Erro ao atualizar lançamento ${lancamento.id}:`, error);
          return { success: false, id: lancamento.id, error: error };
        }
      });
      
      const results = await Promise.all(updatePromises);
      const sucessos = results.filter(r => r.success).length;
      const falhas = results.filter(r => !r.success).length;
      
      console.log(`📊 Resultado: ${sucessos} sucessos, ${falhas} falhas`);
      
      if (sucessos > 0) {
        toast.success(`✅ ${sucessos} lançamento(s) marcado(s) como pago!`);
      }
      
      if (falhas > 0) {
        console.log('❌ Falhas detalhadas:', results.filter(r => !r.success));
        toast.warning(`⚠️ ${falhas} lançamento(s) não puderam ser atualizados`);
      }
      
    } catch (error) {
      console.error('❌ Erro geral ao atualizar lançamentos:', error);
      toast.error('Erro ao atualizar status dos lançamentos');
    }
  };

  const getStatusIcon = (status: ProcessingFile['status']) => {
    switch (status) {
      case 'uploading':
      case 'analyzing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: ProcessingFile['status']) => {
    switch (status) {
      case 'uploading':
        return 'Enviando...';
      case 'analyzing':
        return 'Analisando...';
      case 'completed':
        return 'Concluído';
      case 'error':
        return 'Erro';
      default:
        return '';
    }
  };

  return (
    <div
      ref={dropzoneRef}
      data-dropzone="true"
      className={cn(
        "fixed top-0 right-0 w-[600px] h-full z-[99999]",
        "border-4 border-dashed border-blue-400",
        "bg-blue-50/95 backdrop-blur-sm",
        "flex flex-col items-center justify-center",
        "transition-all duration-300",
        "animate-pulse-border-drawer",
        isDragOver && "border-blue-600 bg-blue-100/90"
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onDragEnter={(e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('🎯 DrawerDropzone: Drag Enter no componente');
        setIsDragOver(true);
      }}
      onClick={() => {
        console.log('🎯 DrawerDropzone: Clique detectado - componente está responsivo');
        toast.info('Dropzone está responsivo! Tente arrastar um arquivo.');
      }}
      style={{
        cursor: 'pointer',
        userSelect: 'none'
      }}
    >
      <div className="text-center space-y-4 p-8">
        <div className="animate-pulse-icon-drawer">
          <Upload className="h-16 w-16 text-blue-500 mx-auto" />
        </div>
        
        <div className="space-y-2">
          <h3 className="text-xl font-semibold text-blue-700">
            Arraste arquivos aqui para enriquecer este documento
          </h3>
          <p className="text-blue-600">
            Exemplos: comprovantes, notas fiscais, contratos relacionados
          </p>
          <p className="text-xs text-blue-500 mt-2">
            🎯 Dropzone ativo - ID: {documentoId} | Clique para testar responsividade
          </p>
        </div>

        {/* Lista de arquivos sendo processados */}
        {processingFiles.length > 0 && (
          <div className="mt-6 space-y-2 max-w-md">
            {processingFiles.map((item, index) => (
              <div key={index} className="flex items-center gap-3 p-3 bg-white/80 rounded-lg">
                <FileText className="h-5 w-5 text-gray-500 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {item.file.name}
                  </p>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(item.status)}
                    <span className="text-xs text-gray-600">
                      {getStatusText(item.status)}
                    </span>
                    {item.error && (
                      <span className="text-xs text-red-600 truncate">
                        {item.error}
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-xs text-gray-500">
                  {item.progress}%
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 