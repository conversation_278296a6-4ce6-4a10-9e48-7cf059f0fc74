import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/types/supabase';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';

interface Obra {
  id: string;
  name: string;
}

interface Contato {
  id: string;
  name: string;
  type: string;
}

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticação - Padrão recomendado para Route Handlers
    let session;
    let supabase;
    let obras: Obra[] = [];
    let contatos: Contato[] = [];

    try {
      supabase = await getSupabaseRouteClient();
      const { data } = await supabase.auth.getSession();
      session = data.session;

      if (!session) {} else {
        // Buscar obras do usuário para incluir no modelo
        const obrasResult = await supabase
          .from('obras')
          .select('id, nome')
          .order('nome');

        if (obrasResult.error) {
          
        } else {
          // Mapear os campos para o formato esperado
          obras = (obrasResult.data || []).map(obra => ({
            id: obra.id,
            name: obra.nome
          }));
        }

        // Buscar contatos para incluir no modelo
        const contatosResult = await supabase
          .from('contatos')
          .select('id, nome, tipo')
          .order('nome');

        if (contatosResult.error) {
          
        } else {
          // Mapear os campos para o formato esperado
          contatos = (contatosResult.data || []).map(contato => ({
            id: contato.id,
            name: contato.nome,
            type: contato.tipo
          }));
        }
      }
    } catch (authError) {
      
      // Continuar com dados vazios
    }

    // Criar planilha de exemplo
    const workbook = XLSX.utils.book_new();

    // Dados de exemplo para a planilha
    const exampleData = [
      {
        obra_id: obras && obras.length > 0 ? obras[0].id : 'ID_DA_OBRA',
        tipo_lancamento: 'construtora',
        valor_total: 5000.00,
        data_competencia: '15/04/2024',
        forma_pagamento: 'pix',
        descricao: 'Pagamento de serviço',
        contato_id: contatos && contatos.length > 0 ? contatos[0].id : null,
        observacoes: 'Observações opcionais',
        status: 'Em aberto',
        numero_parcelas: 3,
        valor_parcela: 1666.67,
        data_vencimento: '30/04/2024'
      },
      {
        obra_id: obras && obras.length > 0 ? (obras.length > 1 ? obras[1].id : obras[0].id) : 'ID_DA_OBRA',
        tipo_lancamento: 'terceiros',
        valor_total: 2500.00,
        data_competencia: '20/04/2024',
        forma_pagamento: 'boleto',
        descricao: 'Materiais de construção',
        contato_id: contatos && contatos.length > 0 ? (contatos.length > 1 ? contatos[1].id : contatos[0].id) : null,
        observacoes: 'Entrega no local',
        status: 'Em aberto',
        numero_parcelas: 2,
        valor_parcela: 1250.00,
        data_vencimento: '05/05/2024'
      }
    ];

    // Criar planilha principal
    const worksheet = XLSX.utils.json_to_sheet(exampleData);

    // Adicionar planilha ao workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Lançamentos');

    // Criar planilha de referência para obras
    const obrasData = obras.map((obra: Obra) => ({
      id: obra.id,
      nome: obra.name
    }));

    const obrasWorksheet = XLSX.utils.json_to_sheet(obrasData);
    XLSX.utils.book_append_sheet(workbook, obrasWorksheet, 'Referência - Obras');

    // Criar planilha de referência para contatos
    const contatosData = contatos.map((contato: Contato) => ({
      id: contato.id,
      nome: contato.name,
      tipo: contato.type
    }));

    const contatosWorksheet = XLSX.utils.json_to_sheet(contatosData);
    XLSX.utils.book_append_sheet(workbook, contatosWorksheet, 'Referência - Contatos');

    // Criar planilha de instruções
    const instrucoesData = [
      { campo: 'obra_id', obrigatorio: 'Sim', descricao: 'ID da obra (ver aba Referência - Obras)' },
      { campo: 'tipo_lancamento', obrigatorio: 'Sim', descricao: 'Tipo do lançamento: "construtora" ou "terceiros"' },
      { campo: 'valor_total', obrigatorio: 'Sim', descricao: 'Valor total do lançamento' },
      { campo: 'data_competencia', obrigatorio: 'Sim', descricao: 'Data de competência no formato DD/MM/AAAA' },
      { campo: 'forma_pagamento', obrigatorio: 'Sim', descricao: 'Forma de pagamento (ex: pix, boleto, dinheiro, etc)' },
      { campo: 'descricao', obrigatorio: 'Sim', descricao: 'Descrição do lançamento' },
      { campo: 'contato_id', obrigatorio: 'Não', descricao: 'ID do contato (ver aba Referência - Contatos)' },
      { campo: 'observacoes', obrigatorio: 'Não', descricao: 'Observações adicionais' },
      { campo: 'status', obrigatorio: 'Não', descricao: 'Status: "Em aberto", "Pago" ou "Cancelado". Padrão: "Em aberto"' },
      { campo: 'numero_parcelas', obrigatorio: 'Não', descricao: 'Número de parcelas (apenas se tiver parcelas)' },
      { campo: 'valor_parcela', obrigatorio: 'Não', descricao: 'Valor de cada parcela (apenas se tiver parcelas)' },
      { campo: 'data_vencimento', obrigatorio: 'Não', descricao: 'Data de vencimento da primeira parcela no formato DD/MM/AAAA (apenas se tiver parcelas)' }
    ];

    const instrucoesWorksheet = XLSX.utils.json_to_sheet(instrucoesData);
    XLSX.utils.book_append_sheet(workbook, instrucoesWorksheet, 'Instruções');

    // Converter para buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Retornar como arquivo para download
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="modelo_lancamentos.xlsx"'
      }
    });

  } catch (error) {
    return NextResponse.json(
      { message: error instanceof Error ? error.message : 'Erro ao gerar modelo de planilha' },
      { status: 500 }
    );
  }
}