-- Função para desabilitar o trigger de validação de parcelas
CREATE OR REP<PERSON>CE FUNCTION disable_parcelas_validation_trigger()
RETURNS BOOLEAN AS $$
BEGIN
    -- Desabilitar o trigger temporariamente na tabela parcelas
    EXECUTE 'ALTER TABLE parcelas DISABLE TRIGGER validate_parcelas_total';
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Erro ao desabilitar trigger: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para habilitar o trigger de validação de parcelas
CREATE OR REPLACE FUNCTION enable_parcelas_validation_trigger()
RETURNS BOOLEAN AS $$
BEGIN
    -- Habilitar o trigger novamente na tabela parcelas
    EXECUTE 'ALTER TABLE parcelas ENABLE TRIGGER validate_parcelas_total';
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Erro ao habilitar trigger: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Conceder permissões de execução para a função anônima e autenticada
GRANT EXECUTE ON FUNCTION disable_parcelas_validation_trigger TO anon, authenticated;
GRANT EXECUTE ON FUNCTION enable_parcelas_validation_trigger TO anon, authenticated; 