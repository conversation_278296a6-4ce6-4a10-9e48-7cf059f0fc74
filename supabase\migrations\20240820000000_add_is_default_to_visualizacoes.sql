-- Adicionar coluna is_default à tabela visualizacoes
ALTER TABLE public.visualizacoes ADD COLUMN IF NOT EXISTS is_default BOOLEAN DEFAULT FALSE;

-- Adicionar comentário à coluna
COMMENT ON COLUMN public.visualizacoes.is_default IS 'Indica se esta é a visualização padrão para o usuário neste contexto';

-- <PERSON><PERSON>r índice para melhorar a performance de consultas
CREATE INDEX IF NOT EXISTS visualizacoes_is_default_idx ON public.visualizacoes(is_default);
CREATE INDEX IF NOT EXISTS visualizacoes_user_contexto_default_idx ON public.visualizacoes(user_id, contexto, is_default);

-- Criar função para garantir que apenas uma visualização seja padrão por usuário e contexto
CREATE OR REPLACE FUNCTION ensure_single_default_visualization()
RETURNS TRIGGER AS $$
BEGIN
  -- Se a nova visualização está sendo definida como padrão
  IF NEW.is_default = TRUE THEN
    -- Remover o status de padrão de todas as outras visualizações do mesmo usuário e contexto
    UPDATE public.visualizacoes
    SET is_default = FALSE
    WHERE user_id = NEW.user_id 
      AND contexto = NEW.contexto 
      AND id != NEW.id
      AND is_default = TRUE;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criar trigger para executar a função antes de inserir ou atualizar
DROP TRIGGER IF EXISTS ensure_single_default_visualization_trigger ON public.visualizacoes;
CREATE TRIGGER ensure_single_default_visualization_trigger
BEFORE INSERT OR UPDATE OF is_default ON public.visualizacoes
FOR EACH ROW
WHEN (NEW.is_default = TRUE)
EXECUTE FUNCTION ensure_single_default_visualization();

-- Criar função para definir uma visualização como padrão
CREATE OR REPLACE FUNCTION set_default_visualization(
  p_visualization_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_contexto TEXT;
  v_count INTEGER;
BEGIN
  -- Verificar se a visualização existe e pertence ao usuário
  SELECT contexto, COUNT(*)
  INTO v_contexto, v_count
  FROM public.visualizacoes
  WHERE id = p_visualization_id AND user_id = p_user_id;
  
  -- Se a visualização não existe ou não pertence ao usuário, retornar falso
  IF v_count = 0 THEN
    RETURN FALSE;
  END IF;
  
  -- Remover o status de padrão de todas as outras visualizações do mesmo usuário e contexto
  UPDATE public.visualizacoes
  SET is_default = FALSE
  WHERE user_id = p_user_id 
    AND contexto = v_contexto 
    AND id != p_visualization_id
    AND is_default = TRUE;
  
  -- Definir a visualização especificada como padrão
  UPDATE public.visualizacoes
  SET is_default = TRUE
  WHERE id = p_visualization_id;
  
  RETURN TRUE;
END;
$$;

-- Criar função para obter a visualização padrão de um usuário em um contexto
CREATE OR REPLACE FUNCTION get_default_visualization(
  p_user_id UUID,
  p_contexto TEXT
)
RETURNS SETOF visualizacoes
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM public.visualizacoes
  WHERE user_id = p_user_id
    AND contexto = p_contexto
    AND is_default = TRUE
  LIMIT 1;
END;
$$;
