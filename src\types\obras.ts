export interface Obra {
  id: string;
  nome: string;
  descricao: string | null;
  cliente_id: string | null;
  orcamento_previsto: number | null;
  data_inicio: string | null;
  data_fim: string | null;
  status: 'planejada' | 'em_andamento' | 'concluida' | 'pausada' | 'cancelada';
  created_at?: string;
  updated_at?: string;
}

export type CreateObraDTO = Omit<Obra, 'id' | 'created_at' | 'updated_at'>;
export type UpdateObraDTO = Partial<CreateObraDTO>;