---
description: 
globs: 
alwaysApply: false
---
# Módulo de Contatos

## Estrutura de Dados

O módulo de contatos gerencia informações de empresas e pessoas associadas a elas, com os seguintes dados principais:

- **Nome Empresa**: Nome comercial da empresa (obrigatório)
- **Razão Social**: Razão social da empresa (opcional)
- **Nome do Contato**: Nome da pessoa de contato na empresa (opcional)
- **Tipo de Pessoa**: Classificação entre Pessoa Física ou Pessoa Jurídica
- **CPF/CNPJ**: Documento fiscal associado (depende do tipo de pessoa)
- **Tipo**: Classificação entre Cliente, Fornecedor ou Prestador de Serviço
- **Informações de Contato**: Email, telefone e chave PIX

A entidade é definida em [src/types/contatos.ts](mdc:src/types/contatos.ts) e mapeada para o Supabase em [src/types/supabase.ts](mdc:src/types/supabase.ts).

## UI Components

Os principais componentes são:

- **Lista**: [src/app/(auth)/contatos/components/lista-contatos.tsx](mdc:src/app/(auth)/contatos/components/lista-contatos.tsx) - Exibe a tabela de contatos
- **Form de Criação**: [src/app/(auth)/contatos/components/form-contato.tsx](mdc:src/app/(auth)/contatos/components/form-contato.tsx) - Usado no drawer para criar contatos
- **Form de Edição**: [src/app/(auth)/contatos/components/form-contato-edit.tsx](mdc:src/app/(auth)/contatos/components/form-contato-edit.tsx) - Usado no diálogo para editar contatos
- **Drawer**: [src/app/(auth)/contatos/components/drawer-contato.tsx](mdc:src/app/(auth)/contatos/components/drawer-contato.tsx) - Painel lateral para criação
- **Diálogo de Edição**: [src/app/(auth)/contatos/components/editar-contato.tsx](mdc:src/app/(auth)/contatos/components/editar-contato.tsx) - Diálogo modal para edição

## Convenções de UI

Para os dropdowns e elementos de seleção:
- Todos os `SelectContent` devem ter as classes `bg-background text-foreground z-50` para garantir que apareçam corretamente dentro de diálogos e drawers
- Campos dependentes (como CPF/CNPJ que depende do tipo de pessoa) devem ser limpos quando o campo pai muda
- O placeholder do campo dependente deve refletir o estado do campo pai

O serviço para operações CRUD está em [src/services/contatos.ts](mdc:src/services/contatos.ts).

