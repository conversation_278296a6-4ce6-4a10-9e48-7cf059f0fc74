'use client';

import {
  Sheet,
  She<PERSON><PERSON>ontent,
  Sheet<PERSON>eader,
  SheetTitle,
  SheetDescription,
} from '@/components/ui/sheet';
import { FormObra } from './form-obra';
import { Obra } from '@/types/obras';

interface DrawerObraProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  obra?: Obra;
  onSubmit: () => Promise<void>;
}

export function DrawerObra({ open, onOpenChange, obra, onSubmit }: DrawerObraProps) {
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-[600px]">
        <SheetHeader>
          <SheetTitle>{obra ? 'Editar Obra' : 'Nova Obra'}</SheetTitle>
          <SheetDescription>
            {obra 
              ? 'Faça as alterações necessárias nos dados da obra.' 
              : 'Preencha os dados para criar uma nova obra.'}
          </SheetDescription>
        </SheetHeader>
        <div className="py-6">
          <FormObra 
            obra={obra} 
            onSuccess={onSubmit} 
            onCancel={() => onOpenChange(false)} 
          />
        </div>
      </SheetContent>
    </Sheet>
  );
}