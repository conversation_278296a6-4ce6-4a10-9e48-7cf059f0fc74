import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Criar cliente Supabase com service_role para acesso direto ao banco
const createServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    return null;
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

// GET /api/visualizacoes/migrations - Aplica as migrações SQL
export async function GET(request: NextRequest) {
  try {
    // Criar cliente Supabase com service role
    const supabase = createServiceClient();
    if (!supabase) {
      return NextResponse.json({ error: 'Erro ao criar cliente Supabase' }, { status: 500 });
    }

    // Definir as migrações SQL a serem aplicadas
    const migrations = [
      {
        name: 'check_function_exists',
        sql: `
          CREATE OR REPLACE FUNCTION public.check_function_exists(function_name TEXT)
          RETURNS JSONB
          LANGUAGE plpgsql
          SECURITY DEFINER
          AS $$
          DECLARE
            v_exists BOOLEAN;
          BEGIN
            -- Verificar se a função existe
            SELECT EXISTS (
              SELECT 1
              FROM information_schema.routines
              WHERE routine_type = 'FUNCTION'
              AND routine_name = function_name
            ) INTO v_exists;

            -- Retornar o resultado como JSONB
            RETURN jsonb_build_object('exists', v_exists);
          END;
          $$;
        `
      },
      {
        name: 'execute_sql',
        sql: `
          CREATE OR REPLACE FUNCTION public.execute_sql(sql_query TEXT, params JSONB DEFAULT '{}'::jsonb)
          RETURNS JSONB
          LANGUAGE plpgsql
          SECURITY DEFINER
          AS $$
          DECLARE
            result JSONB;
          BEGIN
            EXECUTE sql_query INTO result;
            RETURN result;
          EXCEPTION WHEN OTHERS THEN
            RETURN jsonb_build_object(
              'error', SQLERRM,
              'detail', SQLSTATE,
              'query', sql_query
            );
          END;
          $$;
        `
      },
      {
        name: 'service_insert_visualizacao',
        sql: `
          CREATE OR REPLACE FUNCTION public.service_insert_visualizacao(
            p_user_id UUID,
            p_contexto TEXT,
            p_nome TEXT,
            p_descricao TEXT,
            p_filtros_json JSONB
          )
          RETURNS UUID
          LANGUAGE plpgsql
          SECURITY DEFINER
          AS $$
          DECLARE
            v_id UUID;
          BEGIN
            -- Gerar um novo UUID para a visualização
            v_id := gen_random_uuid();

            -- Inserir a visualização com o UUID gerado
            INSERT INTO public.visualizacoes (
              id,
              user_id,
              contexto,
              nome,
              descricao,
              filtros_json,
              created_at,
              updated_at
            ) VALUES (
              v_id,
              p_user_id,
              p_contexto,
              p_nome,
              p_descricao,
              p_filtros_json,
              NOW(),
              NOW()
            );

            -- Retornar o ID da visualização criada
            RETURN v_id;
          END;
          $$;
        `
      }
    ];

    // Aplicar cada migração
    const resultados = [];
    for (const migration of migrations) {
      try {
        // Verificar se a função já existe
        const { data: checkData, error: checkError } = await supabase.rpc(
          'check_function_exists',
          { function_name: migration.name }
        ).catch(() => ({ data: null, error: { message: 'Função de verificação não existe' } }));

        if (!checkError && checkData && checkData.exists) {
          resultados.push({ name: migration.name, status: 'já existe' });
          continue;
        }

        // Executar o SQL da migração
        const { error: sqlError } = await supabase.rpc(
          'execute_sql',
          { sql_query: migration.sql }
        ).catch(() => {
          // Se a função execute_sql não existir, tentar executar diretamente
          return { error: { message: 'Função execute_sql não existe' } };
        });

        if (sqlError) {
          // Se for a primeira migração (check_function_exists), tentar criar diretamente
          if (migration.name === 'check_function_exists') {
            try {
              // Executar SQL diretamente
              const { error: directError } = await supabase.from('_migrations_temp')
                .insert({ sql: migration.sql })
                .select();

              if (directError) {
                // Tentar criar a tabela temporária
                await supabase.rpc('execute_sql', {
                  sql_query: `
                    CREATE TABLE IF NOT EXISTS _migrations_temp (
                      id SERIAL PRIMARY KEY,
                      sql TEXT,
                      created_at TIMESTAMPTZ DEFAULT NOW()
                    );
                  `
                }).catch(() => null);

                // Inserir o SQL na tabela temporária
                await supabase.from('_migrations_temp')
                  .insert({ sql: migration.sql });

                resultados.push({ name: migration.name, status: 'inserido na tabela temporária' });
              }
            } catch (tempError) {
              resultados.push({ name: migration.name, status: 'erro', error: sqlError.message });
            }
          } else {
            resultados.push({ name: migration.name, status: 'erro', error: sqlError.message });
          }
        } else {
          resultados.push({ name: migration.name, status: 'sucesso' });
        }
      } catch (migrationError: any) {
        resultados.push({ name: migration.name, status: 'erro', error: migrationError.message });
      }
    }

    return NextResponse.json({ success: true, resultados });
  } catch (error: any) {
    return NextResponse.json({ error: 'Erro interno do servidor: ' + (error?.message || 'desconhecido') }, { status: 500 });
  }
}