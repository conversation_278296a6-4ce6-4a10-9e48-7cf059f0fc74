# Sistema de Gestão Financeira de Obras

Sistema web para gestão financeira de obras, permitindo o controle de receitas, despesas e contratos de forma centralizada por obra.

## Tecnologias Utilizadas

- Next.js 14 (App Router)
- TypeScript
- Tailwind CSS
- Supabase (Autenticação e Banco de Dados)
- Shadcn/ui (Componentes)

## Funcionalidades Principais

- Dashboard financeiro geral e por obra
- Gestão de obras (cadastro, edição, listagem)
- Controle de lançamentos financeiros
- Gestão de contratos com terceiros
- Cadastro de contatos (fornecedores e clientes)
- Autenticação e controle de acesso
- Integração com WhatsApp (Evolution API)

## Configuração do Ambiente

1. Clone o repositório
2. Instale as dependências:
```bash
npm install
```

3. Configure as variáveis de ambiente:
- Crie um arquivo `.env.local` na raiz do projeto
- Adicione as seguintes variáveis:
```env
NEXT_PUBLIC_SUPABASE_URL=sua_url_do_supabase
NEXT_PUBLIC_SUPABASE_ANON_KEY=sua_chave_anonima_do_supabase
```

4. Inicie o servidor de desenvolvimento:
```bash
npm run dev
```

## Estrutura do Projeto

```
src/
  ├── app/                    # Rotas e páginas
  │   ├── (auth)/            # Rotas autenticadas
  │   │   ├── dashboard/     # Dashboard principal
  │   │   ├── obras/        # Gestão de obras
  │   │   ├── lancamentos/  # Lançamentos financeiros
  │   │   ├── contratos/    # Contratos com terceiros
  │   │   ├── contatos/     # Gestão de contatos
  │   │   └── configuracoes/ # Configurações do sistema
  │   └── login/            # Página de login
  ├── components/            # Componentes reutilizáveis
  │   ├── ui/              # Componentes de UI básicos
  │   ├── forms/           # Formulários
  │   ├── layout/          # Componentes de layout
  │   └── shared/          # Componentes compartilhados
  ├── lib/                 # Utilitários e configurações
  │   ├── supabase/       # Cliente e tipos do Supabase
  │   ├── utils/          # Funções utilitárias
  │   └── constants/      # Constantes do sistema
  ├── services/           # Serviços de API
  └── types/              # Definições de tipos

```

## Banco de Dados

O projeto utiliza o Supabase como banco de dados. As principais tabelas são:

- `constructions`: Obras
- `transactions`: Lançamentos financeiros
- `contracts`: Contratos com terceiros
- `measurements`: Medições dos contratos
- `contacts`: Contatos (clientes e fornecedores)
- `categories`: Categorias de lançamentos

## Contribuição

1. Faça o fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/nova-feature`)
3. Faça commit das suas alterações (`git commit -m 'Adiciona nova feature'`)
4. Faça push para a branch (`git push origin feature/nova-feature`)
5. Abra um Pull Request

## Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.
