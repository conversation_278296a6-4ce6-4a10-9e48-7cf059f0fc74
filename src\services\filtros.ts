'use client'

import { supabase } from '@/lib/supabase/client'

export function useFiltrosService() {
  return {
    /**
     * Busca todos os fornecedores para uso nos filtros
     */
    async listarFornecedores(): Promise<string[]> {
      try {
        // Buscar contatos do tipo fornecedor
        const { data, error } = await supabase
          .from('contatos')
          .select('nome_empresa, nome_razao, nome_contato')
          .eq('tipo', 'fornecedor')
          .order('nome_empresa')

        if (error) {
          throw error;
        }

        return data?.map(contato => 
          contato.nome_empresa || contato.nome_razao || contato.nome_contato || 'Sem nome'
        ).filter(Boolean) || [];
      } catch (error) {
        throw error;
      }
    },

    /**
     * Busca todos os status de lançamentos disponíveis
     */
    async listarStatusLancamentos(): Promise<{ value: string; label: string }[]> {
      return [
        { value: 'Em aberto', label: 'Em aberto' },
        { value: 'Pago', label: 'Pago' },
        { value: 'Cancelado', label: 'Cancelado' }
      ]
    },

    /**
     * Retorna as opções de status disponíveis (função síncrona)
     */
    getStatusOptions(): { value: string; label: string }[] {
      return [
        { value: 'Em aberto', label: 'Em aberto' },
        { value: 'Pago', label: 'Pago' },
        { value: 'Cancelado', label: 'Cancelado' }
      ]
    }
  }
}