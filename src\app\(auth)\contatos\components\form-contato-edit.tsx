'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SelectTrigger, SelectValue, SelectContent, SelectItem, Select } from '@/components/ui/select';
import { toast } from 'sonner';
import { Contato } from '@/types/contatos';
import { useContatosService } from '@/services/contatos';

interface FormContatoEditProps {
  contato: Contato;
  onCancel: () => void;
  onSuccess: () => void;
}

export function FormContatoEdit({ contato, onCancel, onSuccess }: FormContatoEditProps) {
  const [nome_empresa, setNomeEmpresa] = useState(contato.nome_empresa || '');
  const [nome_razao, setNomeRazao] = useState(contato.nome_razao || '');
  const [nome_contato, setNomeContato] = useState(contato.nome_contato || '');
  const [email, setEmail] = useState(contato.email || '');
  const [telefone, setTelefone] = useState(contato.telefone || '');
  const [chave_pix, setChavePix] = useState(contato.chave_pix || '');
  const [tipo, setTipo] = useState<'fornecedor' | 'prestador' | 'cliente'>(contato.tipo);
  const [tipo_pessoa, setTipoPessoa] = useState<'fisica' | 'juridica' | null>(contato.tipo_pessoa || null);
  const [cpf_cnpj, setCpfCnpj] = useState(contato.cpf_cnpj || '');
  
  const [saving, setSaving] = useState(false);
  const contatosService = useContatosService();

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    
    const formData = {
      nome_empresa,
      nome_razao,
      nome_contato,
      email,
      telefone,
      chave_pix,
      tipo,
      tipo_pessoa,
      cpf_cnpj
    };
    
    try {
      setSaving(true);
      await contatosService.atualizarContato(contato.id, formData);
      toast.success('Contato atualizado com sucesso');
      onSuccess();
    } catch (error: any) {
      toast.error(`Erro ao atualizar contato: ${error.message}`);
    } finally {
      setSaving(false);
    }
  }

  const handleTipoPessoaChange = (value: string) => {
    setTipoPessoa(value as 'fisica' | 'juridica');
    setCpfCnpj(''); // Limpar CPF/CNPJ ao mudar o tipo
  };

  const getCpfCnpjPlaceholder = () => {
    if (!tipo_pessoa) return "Selecione o tipo de pessoa primeiro";
    return tipo_pessoa === 'fisica' ? "CPF (apenas números)" : "CNPJ (apenas números)";
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="nome_empresa">Nome Empresa</Label>
        <Input 
          id="nome_empresa"
          value={nome_empresa} 
          onChange={(e) => setNomeEmpresa(e.target.value)} 
          placeholder="C&S Blocos Itatiba"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="nome_razao">Razão Social</Label>
        <Input 
          id="nome_razao"
          value={nome_razao} 
          onChange={(e) => setNomeRazao(e.target.value)} 
          placeholder="Digite a razão social"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="nome_contato">Nome do Contato</Label>
        <Input 
          id="nome_contato"
          value={nome_contato} 
          onChange={(e) => setNomeContato(e.target.value)} 
          placeholder="Digite o nome do contato"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="tipo_pessoa">Tipo de Pessoa</Label>
          <Select 
            value={tipo_pessoa || ''} 
            onValueChange={handleTipoPessoaChange}
          >
            <SelectTrigger id="tipo_pessoa">
              <SelectValue placeholder="Selecione o tipo de pessoa" />
            </SelectTrigger>
            <SelectContent className="bg-background text-foreground z-50">
              <SelectItem value="fisica">Pessoa Física</SelectItem>
              <SelectItem value="juridica">Pessoa Jurídica</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="cpf_cnpj">
            {tipo_pessoa === 'fisica' ? 'CPF' : 
             tipo_pessoa === 'juridica' ? 'CNPJ' : 'CPF/CNPJ'}
          </Label>
          <Input 
            id="cpf_cnpj"
            value={cpf_cnpj} 
            onChange={(e) => setCpfCnpj(e.target.value)} 
            placeholder={getCpfCnpjPlaceholder()}
            disabled={!tipo_pessoa}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input 
          id="email"
          type="email" 
          value={email} 
          onChange={(e) => setEmail(e.target.value)} 
          placeholder="Digite o email"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="telefone">Telefone</Label>
        <Input 
          id="telefone"
          value={telefone} 
          onChange={(e) => setTelefone(e.target.value)} 
          placeholder="Digite o telefone"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="chave_pix">Chave PIX</Label>
        <Input 
          id="chave_pix"
          value={chave_pix} 
          onChange={(e) => setChavePix(e.target.value)} 
          placeholder="11964029361"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="tipo">Tipo</Label>
        <Select 
          value={tipo} 
          onValueChange={(value: string) => setTipo(value as 'fornecedor' | 'prestador' | 'cliente')}
        >
          <SelectTrigger id="tipo">
            <SelectValue placeholder="Selecione o tipo de contato" />
          </SelectTrigger>
          <SelectContent className="bg-background text-foreground z-50">
            <SelectItem value="cliente">Cliente</SelectItem>
            <SelectItem value="fornecedor">Fornecedor</SelectItem>
            <SelectItem value="prestador">Prestador de Serviço</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-end space-x-4 pt-4">
        <Button 
          type="button" 
          variant="outline" 
          onClick={onCancel}
          disabled={saving}
        >
          Cancelar
        </Button>
        <Button 
          type="submit"
          disabled={saving}
        >
          Salvar
        </Button>
      </div>
    </form>
  );
}