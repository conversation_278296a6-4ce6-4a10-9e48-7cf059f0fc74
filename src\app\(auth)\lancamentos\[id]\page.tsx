'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { DrawerLancamento } from '../components/drawer-lancamento';
import { LancamentoComParcelas } from '@/types/lancamentos';
import { useLancamentosService } from '@/services/lancamentos';

export default function LancamentoDetalhesPage() {
  const params = useParams();
  const [lancamento, setLancamento] = useState<LancamentoComParcelas | null>(null);
  const [loading, setLoading] = useState(true);
  const lancamentosService = useLancamentosService();

  useEffect(() => {
    async function carregarLancamento() {
      if (!params.id || !lancamentosService) return;
      
      try {
        setLoading(true);
        const data = await lancamentosService.getLancamentoById(params.id as string);
        setLancamento(data);
      } catch (error) {
  
      } finally {
        setLoading(false);
      }
    }

    carregarLancamento();
  }, [params.id, lancamentosService]);

  if (loading) {
    return <div>Carregando...</div>;
  }

  if (!lancamento) {
    return <div>Lançamento não encontrado.</div>;
  }

  return (
    <div>
      <DrawerLancamento
        lancamento={lancamento}
        open={true}
        onOpenChange={() => {}}
        onSubmit={async () => {}}
      />
    </div>
  );
} 