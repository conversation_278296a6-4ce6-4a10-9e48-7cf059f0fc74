'use client'

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { PageHeader } from '@/components/Layout/PageHeader';
import { ListaContratos } from './components/lista-contratos';

export default function ContratosPage() {
  const [refreshKey, setRefreshKey] = useState(0);

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const handleEditContrato = (id: string) => {
    // TODO: Implementar edição de contrato
  };

  return (
    <div className="space-y-6">
      <PageHeader 
        title="Contratos"
        description="Gerencie contratos e medições"
        actions={
          <Button onClick={handleRefresh}>
            <Plus className="h-4 w-4 mr-2" />
            Novo Contrato
          </Button>
        }
      />

      <ListaContratos 
        key={refreshKey}
        onEdit={handleEditContrato}
      />
    </div>
  );
}