/* Copyright 2012 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// This is a simplified version of the worker file
// The actual worker file is much larger, but this will serve as a placeholder
// that will allow the PDF.js library to initialize properly

"use strict";

// Worker initialization
self.onmessage = function (event) {
  const data = event.data;

  // Respond with a fake initialization confirmation
  self.postMessage({
    type: "ready",
    id: data.id,
    numWorkers: 1
  });
};

// Handle PDF loading requests
self.addEventListener("message", function(event) {
  const data = event.data;

  if (data.type === "GetDocRequest") {
    // Respond with a simplified document structure
    self.postMessage({
      type: "GetDoc",
      id: data.id,
      result: {
        numPages: 1,
        info: {
          PDFFormatVersion: "1.7",
          IsAcroFormPresent: false,
          IsXFAPresent: false,
          Title: "PDF Document",
          Author: "PDF.js",
          Subject: "",
          Keywords: "",
          Creator: "PDF.js",
          Producer: "PDF.js",
          CreationDate: null,
          ModDate: null
        }
      }
    });
  }
});


