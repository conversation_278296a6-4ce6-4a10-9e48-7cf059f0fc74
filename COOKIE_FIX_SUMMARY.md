# Correção do Problema de Cookies Assíncronos - Next.js 15

## Problema Identificado

O erro estava ocorrendo quando os detalhes do lançamento eram acessados:

```
Error: Route "/api/obras" used 'cookies().get('sb-ivmnkctopksvvzmbulah-auth-token')'. 
'cookies()' should be awaited before using its value.
Failed to parse cookie string: SyntaxError: Unexpected token 'b', "base64-eyJ"... is not valid JSON
```

## Causa Raiz

No Next.js 15, a função `cookies()` se tornou **assíncrona** e deve ser aguardada antes do uso. O código do projeto ainda estava usando o padrão síncrono do Next.js 14.

## Solução Aplicada

### 1. Arquivo Corrigido: `src/lib/supabase/route-client.ts`

**Antes (Next.js 14 - síncrono):**
```typescript
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export const getSupabaseRouteClient = cache(async () => {
  const cookieStore = cookies(); // ❌ Síncrono
  return createRouteHandlerClient({ cookies: () => cookieStore });
});
```

**Depois (Next.js 15 - assíncrono + @supabase/ssr):**
```typescript
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

export const getSupabaseRouteClient = cache(async () => {
  const cookieStore = await cookies(); // ✅ Assíncrono
  
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          cookieStore.set({ name, value, ...options });
        },
        remove(name: string, options: any) {
          cookieStore.set({ name, value: '', ...options });
        },
      },
    }
  );
});
```

### 2. Mudanças Realizadas

1. **Substituição da biblioteca**: Migrou de `@supabase/auth-helpers-nextjs` para `@supabase/ssr` que é mais compatível com Next.js 15
2. **Aguardando cookies**: Adicionou `await` na chamada da função `cookies()`
3. **Configuração manual de cookies**: Implementou as funções `get`, `set` e `remove` para gerenciar cookies adequadamente

## Arquivos Beneficiados

A correção beneficia automaticamente **todos** os arquivos que usam `getSupabaseRouteClient()`:

- ✅ `/api/obras/route.ts`
- ✅ `/api/lancamentos/[id]/route.ts` (detalhes do lançamento)
- ✅ `/api/lancamentos/route.ts`
- ✅ `/api/documentos/**`
- ✅ `/api/parcelas/**`
- ✅ `/api/contatos/route.ts`
- ✅ E todos os outros Route Handlers

## Teste de Verificação

**Antes da correção:**
```
Error: cookies() should be awaited before using its value
Failed to parse cookie string: SyntaxError: Unexpected token 'b'...
```

**Depois da correção:**
```bash
curl http://localhost:3000/api/obras
# ✅ Resposta: {"data":[...]} - Sem erros de cookies
```

## Referências

- [Next.js 15 Documentation - Cookies](https://nextjs.org/docs/app/api-reference/functions/cookies)
- [Supabase SSR Documentation](https://supabase.com/docs/guides/auth/server-side/nextjs)

## Status

✅ **RESOLVIDO** - O problema de cookies assíncronos foi corrigido e o sistema está funcionando normalmente. 