import { Building2, TrendingUp, TrendingDown } from 'lucide-react';

export default function DashboardPage() {
  return (
    <div className="space-y-8">
      {/* Cards de resumo */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-xl border bg-card p-6 text-card-foreground shadow">
          <div className="flex items-center gap-4">
            <Building2 className="h-8 w-8 text-blue-500" />
            <div>
              <p className="text-sm font-medium">Obras em Andamento</p>
              <p className="text-2xl font-bold">12</p>
            </div>
          </div>
        </div>

        <div className="rounded-xl border bg-card p-6 text-card-foreground shadow">
          <div className="flex items-center gap-4">
            <TrendingUp className="h-8 w-8 text-green-500" />
            <div>
              <p className="text-sm font-medium">Receitas (Mês)</p>
              <p className="text-2xl font-bold">R$ 250.000</p>
            </div>
          </div>
        </div>

        <div className="rounded-xl border bg-card p-6 text-card-foreground shadow">
          <div className="flex items-center gap-4">
            <TrendingDown className="h-8 w-8 text-red-500" />
            <div>
              <p className="text-sm font-medium">Despesas (Mês)</p>
              <p className="text-2xl font-bold">R$ 180.000</p>
            </div>
          </div>
        </div>

        <div className="rounded-xl border bg-card p-6 text-card-foreground shadow">
          <div className="flex items-center gap-4">
            <div className="rounded-full bg-blue-100 p-2">
              <span className="text-xl font-bold text-blue-700">R$</span>
            </div>
            <div>
              <p className="text-sm font-medium">Saldo Total</p>
              <p className="text-2xl font-bold">R$ 70.000</p>
            </div>
          </div>
        </div>
      </div>

      {/* Lista de obras recentes */}
      <div className="rounded-xl border shadow">
        <div className="border-b p-6">
          <h3 className="text-lg font-medium">Obras Recentes</h3>
        </div>
        <div className="p-6">
          <div className="divide-y">
            {[1, 2, 3].map((obra) => (
              <div key={obra} className="flex items-center justify-between py-4">
                <div>
                  <p className="font-medium">Obra {obra}</p>
                  <p className="text-sm text-muted-foreground">Cliente XYZ</p>
                </div>
                <div className="text-right">
                  <p className="font-medium">R$ 50.000</p>
                  <p className="text-sm text-green-600">Em andamento</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}