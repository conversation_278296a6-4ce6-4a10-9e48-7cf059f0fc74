-- Insert initial categories
INSERT INTO categories (name, type, description) VALUES
  ('Mão de obra', 'expense', 'Custos com mão de obra direta e terceirizada'),
  ('Material', 'expense', 'Custos com materiais de construção'),
  ('Equipamentos', 'expense', 'Custos com aluguel e manutenção de equipamentos'),
  ('Administrativo', 'expense', 'Custos administrativos e overhead'),
  ('Impostos', 'expense', 'Impostos e taxas'),
  ('Medição', 'income', 'Receitas de medições de obra'),
  ('Adiantamento', 'income', 'Adiantamentos de clientes'),
  ('Outros', 'expense', 'Outros custos não categorizados'),
  ('Outras Receitas', 'income', 'Outras receitas não categorizadas');

-- Insert sample contacts
INSERT INTO contacts (name, type, cnpj_cpf, email, phone) VALUES
  ('Construtora ABC', 'client', '12.345.678/0001-90', '<EMAIL>', '(11) 99999-9999'),
  ('Fornecedor XYZ', 'contractor', '98.765.432/0001-10', '<EMAIL>', '(11) 88888-8888');

-- Insert sample construction
INSERT INTO constructions (name, description, client_id, budget, start_date, status) VALUES
  ('Edifício Residencial Aurora', 'Construção de edifício residencial com 10 andares', NULL, 5000000.00, '2024-01-01', 'in_progress');

-- Get the construction ID
DO $$
DECLARE
  v_construction_id UUID;
BEGIN
  SELECT id INTO v_construction_id FROM constructions WHERE name = 'Edifício Residencial Aurora';

  -- Insert sample contract
  INSERT INTO contracts (construction_id, contractor_id, description, total_value, number_of_payments, start_date, status)
  SELECT 
    v_construction_id,
    id,
    'Fornecimento e instalação de esquadrias',
    150000.00,
    3,
    '2024-02-01',
    'active'
  FROM contacts 
  WHERE name = 'Fornecedor XYZ'
  LIMIT 1;

  -- Insert sample transactions
  INSERT INTO transactions (construction_id, description, amount, type, category, date, due_date, status, payment_type, created_by)
  VALUES
    (v_construction_id, 'Compra de material básico', 50000.00, 'expense', 'Material', '2024-02-15', '2024-03-15', 'paid', 'construtora', '00000000-0000-0000-0000-000000000000'),
    (v_construction_id, 'Medição inicial', 100000.00, 'income', 'Medição', '2024-02-01', '2024-03-01', 'paid', 'construtora', '00000000-0000-0000-0000-000000000000');
END $$; 