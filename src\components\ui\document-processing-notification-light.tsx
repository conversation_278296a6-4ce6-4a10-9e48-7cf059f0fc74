'use client';

import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { CheckCircle2, FileText, X, Sparkles } from 'lucide-react';
import { DOCUMENT_PROCESSING_STEPS } from '@/components/ui/document-processing-notification';

interface DocumentProcessingNotificationLightProps {
  fileName: string;
  onClose?: () => void;
  currentStep?: number;
  isVisible?: boolean;
  isComplete?: boolean;
}

export function DocumentProcessingNotificationLight({
  fileName,
  onClose,
  currentStep = 0,
  isVisible = true,
  isComplete = false,
}: DocumentProcessingNotificationLightProps) {
  const [visible, setVisible] = useState(isVisible);
  const [fadeOut, setFadeOut] = useState(false);
  const [step, setStep] = useState(currentStep);
  const [messageVisible, setMessageVisible] = useState(true);
  const [completionAnimationDone, setCompletionAnimationDone] = useState(false);
  const notificationRef = useRef<HTMLDivElement>(null);

  // Calcular o progresso atual com base na etapa
  const currentProgress = Math.min(Math.round((step / 7) * 100), 100);

  // Obter a mensagem atual com base na etapa
  const currentMessage = DOCUMENT_PROCESSING_STEPS[step]?.message || "Inicializando...";

  useEffect(() => {
    if (isVisible) {
      setVisible(true);
      setFadeOut(false);
    }
  }, [isVisible]);

  useEffect(() => {
    if (currentStep !== step) {
      // Animar a transição da mensagem
      setMessageVisible(false);
      setTimeout(() => {
        setStep(currentStep);
        setMessageVisible(true);
      }, 300);
    }
  }, [currentStep, step]);

  useEffect(() => {
    if (isComplete && !completionAnimationDone) {
      setTimeout(() => {
        setCompletionAnimationDone(true);
      }, 1000);
    }
  }, [isComplete, completionAnimationDone]);

  // Auto-hide when complete
  useEffect(() => {
    if (isComplete && completionAnimationDone) {
      const timer = setTimeout(() => {
        setFadeOut(true);

        // After fade out animation, set visible to false
        setTimeout(() => {
          setVisible(false);
          if (onClose) onClose();
        }, 800); // Match the fade-out animation duration
      }, 2500); // Wait longer before starting to fade out for a better experience

      return () => {
        clearTimeout(timer);
      };
    }
  }, [isComplete, completionAnimationDone, onClose]);

  // Criar partículas para o efeito futurista
  const renderParticles = () => {
    const particles = [];
    for (let i = 0; i < 10; i++) {
      const style = {
        top: `${Math.random() * 100}%`,
        left: `${Math.random() * 100}%`,
        animationDelay: `${Math.random() * 3}s`,
        animationDuration: `${4 + Math.random() * 4}s`
      };
      particles.push(<div key={i} className="variant8-light2-particle" style={style} />);
    }
    return particles;
  };

  if (!visible) return null;

  return (
    <div
      ref={notificationRef}
      className={cn(
        "fixed bottom-8 right-8 w-[340px] z-[9999]",
        "animate-fade-in transition-all duration-500",
        fadeOut && "opacity-0 translate-y-4",
        "variant8-light2-card" // Classe personalizada para a variante light 2
      )}
      style={{
        position: 'fixed',
        bottom: '2rem',
        right: '2rem',
        width: '340px',
        zIndex: 9999
      }}
    >
      {/* Fundo com efeito de grade */}
      <div className="variant8-light2-grid"></div>

      {/* Fundo com efeito de partículas */}
      <div className="variant8-light2-particles">
        {renderParticles()}
      </div>

      {/* Conteúdo principal */}
      <div className="p-5 relative z-10">
        {/* Botão de fechar no canto superior direito */}
        <button
          type="button"
          className="absolute top-3 right-3 h-6 w-6 text-gray-400 hover:text-gray-600 bg-transparent rounded-full p-0 flex items-center justify-center"
          onClick={() => {
            setFadeOut(true);
            setTimeout(() => {
              setVisible(false);
              if (onClose) onClose();
            }, 500);
          }}
        >
          <X className="h-3.5 w-3.5" />
          <span className="sr-only">Fechar</span>
        </button>

        {/* Cabeçalho com ícone e título */}
        <div className="flex items-center gap-4 mb-5">
          <div className="variant8-light2-icon-container">
            {isComplete && completionAnimationDone ? (
              <CheckCircle2 className="h-5 w-5 text-green-500 relative z-10" />
            ) : (
              <FileText className="h-5 w-5 text-blue-500 relative z-10" />
            )}
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-800">
              {isComplete && completionAnimationDone ? "Processamento concluído" : "Processando documento"}
            </h3>
            <p className="text-xs text-gray-500 mt-1 truncate" title={fileName}>
              {fileName}
            </p>
          </div>
        </div>

        {/* Status e progresso */}
        <div className="mb-1 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <div className={cn(
              "h-1.5 w-1.5 rounded-full",
              isComplete && completionAnimationDone ? "bg-green-500" : "bg-blue-500 animate-pulse"
            )}></div>
            <span className="text-xs text-gray-500">
              {isComplete && completionAnimationDone ? "Concluído" : "Processando..."}
            </span>
          </div>

          <span className="text-xs font-medium text-gray-700">
            {isComplete && completionAnimationDone ? "100%" : `${currentProgress}%`}
          </span>
        </div>

        {/* Barra de progresso com efeito de brilho */}
        <div className="variant8-light2-progress-track mb-5">
          <div
            className="variant8-light2-progress-fill"
            style={{ width: `${currentProgress}%` }}
          >
            {!isComplete && currentProgress > 5 && (
              <div className="variant8-light2-progress-glow"></div>
            )}
          </div>
        </div>

        {/* Mensagem atual com fundo futurista */}
        <div className="variant8-light2-message-container p-4 mb-5">
          <p
            className={cn(
              "text-sm leading-relaxed transition-opacity duration-300 text-gray-700",
              messageVisible ? "opacity-100" : "opacity-0"
            )}
          >
            {isComplete && completionAnimationDone ? "Documento pronto para revisão" : currentMessage}
          </p>
        </div>

        {/* Rodapé com ações */}
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-500">
            {isComplete && completionAnimationDone ? "Processamento finalizado" : `Etapa ${step} de 7`}
          </span>

          {isComplete && completionAnimationDone ? (
            <button
              type="button"
              className="variant8-light2-button flex items-center gap-1.5"
              onClick={() => {
                setFadeOut(true);
                setTimeout(() => {
                  setVisible(false);
                  if (onClose) onClose();
                }, 500);
              }}
            >
              <Sparkles className="h-3.5 w-3.5" />
              <span>Fechar</span>
            </button>
          ) : (
            <span className="text-xs text-gray-500">
              Analisando dados...
            </span>
          )}
        </div>
      </div>
    </div>
  );
}