-- Função para limpar documentos antigos
CREATE OR REPLACE FUNCTION limpar_documentos_antigos(
  p_status TEXT DEFAULT NULL,
  p_dias_limite INTEGER DEFAULT 30,
  p_user_id UUID DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_documentos_ids UUID[];
  v_count INTEGER;
  v_resultado JSONB;
BEGIN
  -- Selecionar IDs dos documentos a serem excluídos
  SELECT array_agg(id) INTO v_documentos_ids
  FROM documentos
  WHERE 
    (p_status IS NULL OR status = p_status) AND
    created_at < (CURRENT_DATE - p_dias_limite) AND
    (p_user_id IS NULL OR user_id = p_user_id) AND
    (lancamento_id IS NULL); -- Não excluir documentos associados a lançamentos
  
  -- Se não houver documentos para excluir
  IF v_documentos_ids IS NULL OR array_length(v_documentos_ids, 1) IS NULL THEN
    RETURN jsonb_build_object(
      'status', 'success',
      'mensagem', 'Nenhum documento encontrado para exclusão',
      'count', 0
    );
  END IF;
  
  -- Excluir os documentos
  DELETE FROM documentos
  WHERE id = ANY(v_documentos_ids);
  
  GET DIAGNOSTICS v_count = ROW_COUNT;
  
  -- Construir o resultado
  v_resultado := jsonb_build_object(
    'status', 'success',
    'mensagem', 'Documentos antigos excluídos com sucesso',
    'count', v_count
  );
  
  RETURN v_resultado;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Erro ao limpar documentos antigos: %', SQLERRM;
END;
$$;

-- Comentário para a função
COMMENT ON FUNCTION limpar_documentos_antigos IS 'Limpa documentos antigos com base no status e dias limite. Não exclui documentos associados a lançamentos.';

-- Função para obter documentos por lançamento
CREATE OR REPLACE FUNCTION obter_documentos_por_lancamento(
  p_lancamento_id UUID
)
RETURNS SETOF documentos
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM documentos
  WHERE lancamento_id = p_lancamento_id
  ORDER BY created_at DESC;
END;
$$;

-- Comentário para a função
COMMENT ON FUNCTION obter_documentos_por_lancamento IS 'Retorna todos os documentos associados a um lançamento específico';
