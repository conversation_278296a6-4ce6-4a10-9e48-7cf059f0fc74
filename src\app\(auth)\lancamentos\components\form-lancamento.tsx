'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Database } from '@/types/supabase';
import { toast } from 'sonner';
import { Loader2, Check, ChevronsUpDown, PlusCircle, MoreVertical, Pencil, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { createLancamento, updateLancamento } from '@/services/lancamentos';
import type { CreateLancamentoArgs } from '@/services/lancamentos';
import { useContatosService } from '@/services/contatos';
import { useObrasService } from '@/services/obras';
import { useCategoriasService } from '@/services/categorias';
import { DatePicker } from '@/components/ui/date-picker';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from '@/lib/utils';
import React from 'react';
import { DrawerContato } from '@/app/(auth)/contatos/components/drawer-contato';

type FormaPagamento = Database['public']['Enums']['forma_pagamento'];
type TipoLancamento = Database['public']['Enums']['tipo_lancamento'];

const statusLancamentoValues = ["Em aberto", "Pago", "Cancelado"] as const;

const formSchema = z.object({
  descricao: z.string().min(1, 'A descrição é obrigatória'),
  valor_total: z.string().min(1, 'O valor é obrigatório'),
  data_competencia: z.date({
    required_error: 'A data de competência é obrigatória',
  }),
  forma_pagamento: z.enum(['dinheiro', 'pix', 'cartao_credito', 'cartao_debito', 'cheque', 'transferencia', 'boleto'] as const),
  tipo_lancamento: z.enum(['receita', 'despesa'] as const),
  status: z.enum(statusLancamentoValues, {
    required_error: 'O status é obrigatório',
  }),
  observacoes: z.string().nullish(),
  obra_id: z.string().nullish(),
  contato_id: z.string().nullish(),
  categoria_id: z.string().nullish(),
  numero_parcelas: z.number().int({
    message: 'Número de parcelas deve ser um inteiro'
  }).min(1, {
    message: 'Deve haver pelo menos 1 parcela'
  }),
});

type FormValues = z.infer<typeof formSchema>;

interface FormLancamentoProps {
  lancamento?: Database['public']['Tables']['lancamentos']['Row'] & {
    contatos?: Pick<Database['public']['Tables']['contatos']['Row'], 'id' | 'nome_empresa'> | null;
    obras?: Pick<Database['public']['Tables']['obras']['Row'], 'id' | 'nome'> | null;
    categorias?: Pick<Database['public']['Tables']['categorias']['Row'], 'id' | 'nome'> | null;
  };
  onSuccess?: () => void;
  onCancel?: () => void;
  onInvalid?: () => void;
}

export function FormLancamento({ lancamento, onSuccess, onCancel, onInvalid }: FormLancamentoProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [loadingLists, setLoadingLists] = useState(true);
  const [contatos, setContatos] = useState<any[]>([]);
  const [obras, setObras] = useState<Database["public"]["Tables"]["obras"]["Row"][]>([]);
  const [categorias, setCategorias] = useState<Database["public"]["Tables"]["categorias"]["Row"][]>([]);

  const [openCategoriaPopover, setOpenCategoriaPopover] = useState(false);
  const [searchTermCategoria, setSearchTermCategoria] = useState("");
  const [creatingCategoria, setCreatingCategoria] = useState(false);
  const [openContatoPopover, setOpenContatoPopover] = useState(false);
  const [searchTermContato, setSearchTermContato] = useState("");
  const [creatingContato, setCreatingContato] = useState(false);
  const [openObraPopover, setOpenObraPopover] = useState(false);
  const [searchTermObra, setSearchTermObra] = useState("");
  const [creatingObra, setCreatingObra] = useState(false);

  const { listarContatos, criarContato } = useContatosService();
  const { listarObras, criarObra } = useObrasService();
  const { listarCategorias, criarCategoria } = useCategoriasService();

  const clearCategoriaRef = React.useRef<HTMLButtonElement>(null);
  const clearContatoRef = React.useRef<HTMLButtonElement>(null);
  const clearObraRef = React.useRef<HTMLButtonElement>(null);

  // Adiciona estados para controlar o drawer de edição de contato
  const [editContatoId, setEditContatoId] = useState<string | null>(null);
  const [contatoDrawerOpen, setContatoDrawerOpen] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const [loadingCategoria, setLoadingCategoria] = useState(false);
  const [loadingContato, setLoadingContato] = useState(false);
  const [loadingObra, setLoadingObra] = useState(false);

  useEffect(() => {
    async function loadLists() {
      try {
        setLoadingLists(true);
        const [contatosData, obrasData, categoriasData] = await Promise.all([
          listarContatos(),
          listarObras(),
          listarCategorias(),
        ]);
        setContatos(contatosData as any);
        setObras(obrasData);
        setCategorias(categoriasData);
      } catch (error) {
        
        toast.error("Erro ao carregar dados de Contatos, Obras ou Categorias.");
      } finally {
        setLoadingLists(false);
      }
    }
    loadLists();
  }, []);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      descricao: lancamento?.descricao || '',
      valor_total: lancamento?.valor_total ? lancamento.valor_total.toString() : '',
      data_competencia: lancamento?.data_competencia ? new Date(lancamento.data_competencia) : new Date(),
      forma_pagamento: lancamento?.forma_pagamento || 'dinheiro',
      tipo_lancamento: lancamento?.tipo_lancamento || 'receita',
      status: lancamento?.status || 'Em aberto',
      observacoes: lancamento?.observacoes ?? '',
      obra_id: lancamento?.obra_id ?? undefined,
      contato_id: lancamento?.contato_id ?? undefined,
      categoria_id: lancamento?.categoria_id ?? undefined,
      numero_parcelas: 1,
    },
  });

  // Funções específicas para limpar campos
  function limparCategoria(e?: React.MouseEvent | React.KeyboardEvent) {
    if (e) {
      e.stopPropagation();
    }
    form.setValue("categoria_id", null);

    if (clearCategoriaRef.current) {
      clearCategoriaRef.current.click();
    }
  }

  function limparContato(e?: React.MouseEvent | React.KeyboardEvent) {
    if (e) {
      e.stopPropagation();
    }
    form.setValue("contato_id", null);

    if (clearContatoRef.current) {
      clearContatoRef.current.click();
    }
  }

  function limparObra(e?: React.MouseEvent | React.KeyboardEvent) {
    if (e) {
      e.stopPropagation();
    }
    form.setValue("obra_id", null);

    if (clearObraRef.current) {
      clearObraRef.current.click();
    }
  }

  async function onSubmit(data: FormValues) {
    try {
      setLoading(true);

      // Converte o valor para decimal corretamente
      let valorTotalDecimal = 0;
      if (data.valor_total) {
        // Corrigir regex: remover todos os caracteres não numéricos exceto vírgula e ponto
        const valorLimpo = data.valor_total.replace(/[^\d,.]/g, '').replace(',', '.');
        valorTotalDecimal = parseFloat(valorLimpo);
      }

      if (isNaN(valorTotalDecimal) || valorTotalDecimal <= 0) {
        toast.error('Por favor, informe um valor válido');
        setLoading(false);
        if (onInvalid) onInvalid();
        return;
      }

      // Garantir que o status esteja sempre definido
      if (!data.status) {
        data.status = 'Em aberto';
        form.setValue('status', 'Em aberto');
      }

      if (lancamento?.id) {
        // Para edição
        const dataParaAtualizar = {
          descricao: data.descricao,
          valor_total: valorTotalDecimal,
          data_competencia: data.data_competencia.toISOString().split('T')[0],
          forma_pagamento: data.forma_pagamento,
          tipo_lancamento: data.tipo_lancamento,
          status: data.status,
          observacoes: data.observacoes || null,
          obra_id: data.obra_id || null,
          contato_id: data.contato_id || null,
          categoria_id: data.categoria_id || null,
        };

        await updateLancamento(lancamento.id, dataParaAtualizar);
        toast.success('Lançamento atualizado com sucesso!');
      } else {
        // Para criação
        const dataParaCriar = {
          descricao: data.descricao,
          valor_total: valorTotalDecimal,
          data_competencia: data.data_competencia.toISOString().split('T')[0],
          forma_pagamento: data.forma_pagamento,
          tipo_lancamento: data.tipo_lancamento,
          status: data.status,
          observacoes: data.observacoes || null,
          obra_id: data.obra_id || null,
          contato_id: data.contato_id || null,
          categoria_id: data.categoria_id || null,
          numero_parcelas: data.numero_parcelas || 1,
        };

        await createLancamento(dataParaCriar as any);
        toast.success('Lançamento criado com sucesso!');
      }

      router.refresh();

      onSuccess?.();
    } catch (error: any) {

      toast.error(error?.message || 'Erro ao salvar lançamento');
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    if (lancamento) {
      form.reset({
        descricao: lancamento.descricao,
        valor_total: lancamento.valor_total ? lancamento.valor_total.toString() : '',
        data_competencia: new Date(lancamento.data_competencia),
        forma_pagamento: lancamento.forma_pagamento as FormaPagamento,
        tipo_lancamento: lancamento.tipo_lancamento as TipoLancamento,
        status: lancamento.status,
        observacoes: lancamento.observacoes ?? '',
        obra_id: lancamento.obra_id ?? undefined,
        contato_id: lancamento.contato_id ?? undefined,
        categoria_id: lancamento.categoria_id ?? undefined,
        numero_parcelas: 1,
      });
    } else {
      form.reset({
        descricao: '',
        valor_total: '',
        data_competencia: new Date(),
        forma_pagamento: 'dinheiro',
        tipo_lancamento: 'receita',
        status: 'Em aberto',
        observacoes: '',
        obra_id: undefined,
        contato_id: undefined,
        categoria_id: undefined,
        numero_parcelas: 1,
      });
    }
  }, [lancamento, form]);

  async function handleCreateCategoria(nomeCategoria: string) {
    if (!nomeCategoria || creatingCategoria) return;
    setCreatingCategoria(true);
    try {
      const novaCategoria = await criarCategoria({ nome: nomeCategoria });
      if (novaCategoria) {
        setCategorias((prev) => [...prev, novaCategoria]);
        form.setValue('categoria_id', novaCategoria.id);
        toast.success(`Categoria "${novaCategoria.nome}" criada com sucesso!`);
        setSearchTermCategoria("");
        setOpenCategoriaPopover(false);
      } else {
        throw new Error("Falha ao criar categoria, resposta vazia.");
      }
    } catch (error: any) {
      
      toast.error(error.message || "Erro ao criar categoria");
    } finally {
      setCreatingCategoria(false);
    }
  }

  async function handleCreateContato(nomeContato: string) {
    if (!nomeContato || creatingContato) return;
    setCreatingContato(true);
    try {
      const novoContato = await criarContato({ nome_empresa: nomeContato, tipo: 'cliente' });
      if (novoContato) {
        setContatos((prev) => [...prev, novoContato]);
        form.setValue('contato_id', novoContato.id);
        toast.success(`Contato "${novoContato.nome_empresa}" criado com sucesso!`);
        setSearchTermContato("");
        setOpenContatoPopover(false);
      } else {
        throw new Error("Falha ao criar contato, resposta vazia.");
      }
    } catch (error: any) {
      
      toast.error(error.message || "Erro ao criar contato");
    } finally {
      setCreatingContato(false);
    }
  }

  async function handleCreateObra(nomeObra: string) {
    if (!nomeObra || creatingObra) return;
    setCreatingObra(true);
    try {
      const novaObra = await criarObra({ nome: nomeObra, status: 'planejada' });
      if (novaObra) {
        setObras((prev) => [...prev, novaObra]);
        form.setValue('obra_id', novaObra.id);
        toast.success(`Projeto/Obra "${novaObra.nome}" criado com sucesso!`);
        setSearchTermObra("");
        setOpenObraPopover(false);
      } else {
        throw new Error("Falha ao criar obra, resposta vazia.");
      }
    } catch (error: any) {
      
      toast.error(error.message || "Erro ao criar obra");
    } finally {
      setCreatingObra(false);
    }
  }

  const filteredCategorias = searchTermCategoria
    ? categorias.filter((cat) =>
        cat.nome.toLowerCase().includes(searchTermCategoria.toLowerCase())
      )
    : categorias;

  const filteredContatos = searchTermContato
    ? contatos.filter(cont => {
        const searchTerm = searchTermContato.toLowerCase();
        return (
          (cont.nome_empresa && cont.nome_empresa.toLowerCase().includes(searchTerm)) ||
          (cont.nome_razao && cont.nome_razao.toLowerCase().includes(searchTerm)) ||
          (cont.nome_contato && cont.nome_contato.toLowerCase().includes(searchTerm))
        );
      })
    : contatos;

  const filteredObras = searchTermObra
    ? obras.filter(obra => obra.nome.toLowerCase().includes(searchTermObra.toLowerCase()))
    : obras;

  // Função para abrir o drawer de edição de contato
  const handleEditContato = (id: string | null | undefined) => {
    if (id) {
      // Define o ID do contato para edição antes de abrir o drawer
      setEditContatoId(id);
      // Abre o drawer logo em seguida para que a animação inicie
      requestAnimationFrame(() => {
        setContatoDrawerOpen(true);
      });
    }
  };

  // Função para recarregar a lista de contatos
  const fetchContatos = async () => {
    try {
      setLoadingContato(true);
      const contatosData = await listarContatos();
      setContatos(contatosData);
    } catch (error) {
      
      toast.error("Erro ao recarregar contatos.");
    } finally {
      setLoadingContato(false);
    }
  };

  // Função para lidar com o sucesso da edição/criação de contato
  const handleContatoSuccess = async (): Promise<void> => {
    // Recarrega os contatos primeiro
    await fetchContatos();
    // Fecha o drawer com animação
    setContatoDrawerOpen(false);
  };

  // Função para controlar o fechamento do drawer com animação
  const handleDrawerOpenChange = (open: boolean) => {
    // Atualiza o estado de abertura do drawer
    setContatoDrawerOpen(open);

    // Se o drawer foi fechado, aguarde a animação de saída
    if (!open) {
      // Aguarda o tempo da animação antes de limpar o ID
      setTimeout(() => {
        setEditContatoId(null);
      }, 300); // A animação leva 300ms para completar
    }
  };

  return (
    <>
      <Form {...form}>
        <form
          id="lancamento-form-id"
          onSubmit={form.handleSubmit(onSubmit, onInvalid)}
          className="grid grid-cols-1 md:grid-cols-2 gap-4"
          noValidate
        >
          {/* Botões invisíveis para uso em fallbacks */}
          <button
            type="button"
            className="hidden"
            ref={clearCategoriaRef}
            aria-label="Limpar categoria"
            onClick={() => {
              form.setValue("categoria_id", null);
              form.trigger("categoria_id");
            }}
          />
          <button
            type="button"
            className="hidden"
            ref={clearContatoRef}
            aria-label="Limpar contato"
            onClick={() => {
              form.setValue("contato_id", null);
              form.trigger("contato_id");
            }}
          />
          <button
            type="button"
            className="hidden"
            ref={clearObraRef}
            aria-label="Limpar obra"
            onClick={() => {
              form.setValue("obra_id", null);
              form.trigger("obra_id");
            }}
          />

          {loading && (
            <div className="md:col-span-2 text-center py-2 mb-2">
              <div className="inline-flex items-center justify-center gap-2 text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Processando...</span>
              </div>
            </div>
          )}

          <FormField
            control={form.control}
            name="descricao"
            render={({ field }) => (
              <FormItem className="md:col-span-2">
                <FormLabel>Descrição</FormLabel>
                <FormControl>
                  <Input placeholder="Digite a descrição" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="valor_total"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Valor</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Digite o valor"
                    {...field}
                    onChange={(e) => {
                      const v = e.target.value.replace(/[^\d,.]/g, '');
                      field.onChange(v);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="data_competencia"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Data de Competência</FormLabel>
                <FormControl>
                  <DatePicker
                    date={field.value}
                    setDate={(d) => field.onChange(d)}
                    placeholder="Selecione a data"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="categoria_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Categoria</FormLabel>
                <div className="flex gap-2 items-stretch">
                  <div className="flex-1 min-w-0">
                    <Popover open={openCategoriaPopover} onOpenChange={setOpenCategoriaPopover}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openCategoriaPopover}
                            className={cn(
                              "w-full justify-between h-10",
                              !field.value && "text-muted-foreground"
                            )}
                            disabled={loadingLists || creatingCategoria}
                          >
                            <div className="flex items-center justify-between w-full">
                              <span className="text-left truncate max-w-[calc(100%-30px)]">
                                {field.value
                                  ? categorias.find(
                                      (cat) => cat.id === field.value
                                    )?.nome
                                  : "Selecione a categoria"}
                              </span>
                              <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50 ml-1" />
                            </div>
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent
                        className={cn(
                          "w-[--radix-popover-trigger-width] p-0 z-[100]",
                          "bg-background text-foreground"
                        )}
                      >
                        <Command>
                          <CommandInput
                            placeholder="Buscar ou criar categoria..."
                            value={searchTermCategoria}
                            onValueChange={setSearchTermCategoria}
                          />
                          <CommandList>
                            <CommandEmpty>
                              {searchTermCategoria ? `Nenhuma categoria encontrada.` : 'Nenhuma categoria cadastrada.'}
                            </CommandEmpty>
                            <CommandGroup>
                              {filteredCategorias.map((cat) => (
                                <CommandItem
                                  key={cat.id}
                                  value={cat.nome}
                                  onSelect={() => {
                                    form.setValue("categoria_id", cat.id);
                                    setOpenCategoriaPopover(false);
                                    setSearchTermCategoria("");
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      field.value === cat.id ? "opacity-100" : "opacity-0"
                                    )}
                                  />
                                  {cat.nome}
                                </CommandItem>
                              ))}
                              {field.value && (
                                <CommandItem
                                  key="clear"
                                  value="limpar"
                                  className="text-destructive hover:text-destructive/90"
                                  onSelect={() => {
                                    limparCategoria();
                                    setOpenCategoriaPopover(false);
                                    setSearchTermCategoria("");
                                  }}
                                >
                                  <span className="mr-2">✕</span>
                                  Limpar seleção
                                </CommandItem>
                              )}
                              {searchTermCategoria && !filteredCategorias.some(c => c.nome.toLowerCase() === searchTermCategoria.toLowerCase()) && (
                                 <CommandItem
                                   key="create"
                                   value={searchTermCategoria}
                                   onSelect={() => handleCreateCategoria(searchTermCategoria)}
                                   disabled={creatingCategoria}
                                   className="text-muted-foreground italic"
                                 >
                                   <PlusCircle className={cn("mr-2 h-4 w-4", creatingCategoria && "animate-spin")} />
                                   {creatingCategoria ? `Criando "${searchTermCategoria}"...` : `Criar "${searchTermCategoria}"` }
                                 </CommandItem>
                              )}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>

                  {/* Dropdown menu com ações para a categoria */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-10 w-10 flex-shrink-0"
                        disabled={loadingLists}
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-background text-foreground z-[100]">
                      <DropdownMenuItem
                        onClick={() => limparCategoria()}
                        disabled={!field.value}
                        className={!field.value ? "text-muted-foreground" : "text-destructive"}
                      >
                        <X className="mr-2 h-4 w-4" />
                        Limpar seleção
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="contato_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contato</FormLabel>
                <div className="flex gap-2 items-stretch">
                  <div className="flex-1 min-w-0">
                    <Popover open={openContatoPopover} onOpenChange={setOpenContatoPopover}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openContatoPopover}
                            className={cn("w-full justify-between h-10", !field.value && "text-muted-foreground")}
                            disabled={loadingLists || creatingContato}
                          >
                            <div className="flex items-center justify-between w-full">
                              <span className="text-left truncate max-w-[calc(100%-30px)]">
                                {field.value
                                  ? (() => {
                                      const contato = contatos.find(c => c.id === field.value);
                                      return contato?.nome_empresa || contato?.nome_razao || contato?.nome_contato || "Contato selecionado";
                                    })()
                                  : "Selecione o contato"}
                              </span>
                              <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50 ml-1" />
                            </div>
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className={cn("w-[--radix-popover-trigger-width] p-0 z-[100]", "bg-background text-foreground")}>
                        <Command>
                          <CommandInput
                            placeholder="Buscar ou criar contato..."
                            value={searchTermContato}
                            onValueChange={setSearchTermContato}
                          />
                          <CommandList>
                            <CommandEmpty>
                              {searchTermContato ? `Nenhum contato encontrado.` : 'Nenhum contato cadastrado.'}
                            </CommandEmpty>
                            <CommandGroup>
                              {filteredContatos.map((cont) => (
                                <CommandItem
                                  key={cont.id}
                                  value={cont.nome_empresa}
                                  onSelect={() => {
                                    form.setValue("contato_id", cont.id);
                                    setOpenContatoPopover(false);
                                    setSearchTermContato("");
                                  }}
                                >
                                  <Check className={cn("mr-2 h-4 w-4", field.value === cont.id ? "opacity-100" : "opacity-0")}/>
                                  {cont.nome_empresa || cont.nome_razao || cont.nome_contato || "Contato"}
                                </CommandItem>
                              ))}
                              {searchTermContato && !filteredContatos.some(c =>
                                (c.nome_empresa && c.nome_empresa.toLowerCase() === searchTermContato.toLowerCase()) ||
                                (c.nome_razao && c.nome_razao.toLowerCase() === searchTermContato.toLowerCase()) ||
                                (c.nome_contato && c.nome_contato.toLowerCase() === searchTermContato.toLowerCase())
                              ) && (
                                <CommandItem
                                  key="create"
                                  value={searchTermContato}
                                  onSelect={() => handleCreateContato(searchTermContato)}
                                  disabled={creatingContato}
                                  className="text-muted-foreground italic"
                                >
                                  <PlusCircle className={cn("mr-2 h-4 w-4", creatingContato && "animate-spin")} />
                                  {creatingContato ? `Criando "${searchTermContato}"...` : `Criar "${searchTermContato}"` }
                                </CommandItem>
                              )}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>

                  {/* Dropdown menu com ações para o contato */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-10 w-10 flex-shrink-0"
                        disabled={loadingLists}
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-background text-foreground z-[100]">
                      <DropdownMenuItem
                        onClick={() => handleEditContato(field.value)}
                        disabled={!field.value}
                        className={!field.value ? "text-muted-foreground" : ""}
                      >
                        <Pencil className="mr-2 h-4 w-4" />
                        Editar Contato
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => limparContato()}
                        disabled={!field.value}
                        className={!field.value ? "text-muted-foreground" : "text-destructive"}
                      >
                        <X className="mr-2 h-4 w-4" />
                        Limpar seleção
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="tipo_lancamento"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tipo de Lançamento</FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione..." />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="bg-background text-foreground z-[100]">
                    <SelectItem value="receita">Receita</SelectItem>
                    <SelectItem value="despesa">Despesa</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione..." />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="bg-background text-foreground z-[100]">
                    {statusLancamentoValues.map(status => (
                      <SelectItem key={status} value={status}> {status} </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="obra_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Projeto/Obra</FormLabel>
                <div className="flex gap-2 items-stretch">
                  <div className="flex-1 min-w-0">
                    <Popover open={openObraPopover} onOpenChange={setOpenObraPopover}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openObraPopover}
                            className={cn("w-full justify-between h-10", !field.value && "text-muted-foreground")}
                            disabled={loadingLists || creatingObra}
                          >
                            <div className="flex items-center justify-between w-full">
                              <span className="text-left truncate max-w-[calc(100%-30px)]">
                                {field.value
                                  ? obras.find(o => o.id === field.value)?.nome
                                  : "Selecione o projeto/obra"}
                              </span>
                              <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50 ml-1" />
                            </div>
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className={cn("w-[--radix-popover-trigger-width] p-0 z-[100]", "bg-background text-foreground")}>
                        <Command>
                          <CommandInput
                            placeholder="Buscar ou criar projeto/obra..."
                            value={searchTermObra}
                            onValueChange={setSearchTermObra}
                          />
                          <CommandList>
                            <CommandEmpty>
                              {searchTermObra ? `Nenhum projeto/obra encontrado.` : 'Nenhum projeto/obra cadastrado.'}
                            </CommandEmpty>
                            <CommandGroup>
                              {filteredObras.map((obra) => (
                                <CommandItem
                                  key={obra.id}
                                  value={obra.nome}
                                  onSelect={() => {
                                    form.setValue("obra_id", obra.id);
                                    setOpenObraPopover(false);
                                    setSearchTermObra("");
                                  }}
                                >
                                  <Check className={cn("mr-2 h-4 w-4", field.value === obra.id ? "opacity-100" : "opacity-0")}/>
                                  {obra.nome}
                                </CommandItem>
                              ))}
                              {field.value && (
                                <CommandItem
                                  key="clear"
                                  value="limpar"
                                  className="text-destructive hover:text-destructive/90"
                                  onSelect={() => {
                                    limparObra();
                                    setOpenObraPopover(false);
                                    setSearchTermObra("");
                                  }}
                                >
                                  <span className="mr-2">✕</span>
                                  Limpar seleção
                                </CommandItem>
                              )}
                              {searchTermObra && !filteredObras.some(o => o.nome.toLowerCase() === searchTermObra.toLowerCase()) && (
                                 <CommandItem
                                   key="create"
                                   value={searchTermObra}
                                   onSelect={() => handleCreateObra(searchTermObra)}
                                   disabled={creatingObra}
                                   className="text-muted-foreground italic"
                                 >
                                   <PlusCircle className={cn("mr-2 h-4 w-4", creatingObra && "animate-spin")} />
                                   {creatingObra ? `Criando "${searchTermObra}"...` : `Criar "${searchTermObra}"` }
                                 </CommandItem>
                              )}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>

                  {/* Dropdown menu com ações para o projeto/obra */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-10 w-10 flex-shrink-0"
                        disabled={loadingLists}
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-background text-foreground z-[100]">
                      <DropdownMenuItem
                        onClick={() => limparObra()}
                        disabled={!field.value}
                        className={!field.value ? "text-muted-foreground" : "text-destructive"}
                      >
                        <X className="mr-2 h-4 w-4" />
                        Limpar seleção
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="forma_pagamento"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Forma de Pagamento</FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione..." />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="bg-background text-foreground z-[100]">
                    <SelectItem value="dinheiro">Dinheiro</SelectItem>
                    <SelectItem value="pix">PIX</SelectItem>
                    <SelectItem value="cartao_credito">Cartão de Crédito</SelectItem>
                    <SelectItem value="cartao_debito">Cartão de Débito</SelectItem>
                    <SelectItem value="cheque">Cheque</SelectItem>
                    <SelectItem value="transferencia">Transferência</SelectItem>
                    <SelectItem value="boleto">Boleto</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="observacoes"
            render={({ field }) => (
              <FormItem className="md:col-span-2">
                <FormLabel>Observações</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Digite observações adicionais (opcional)"
                    className="resize-none"
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {!lancamento?.id && (
            <FormField
              control={form.control}
              name="numero_parcelas"
              render={({ field }) => (
                <FormItem className="md:col-span-2">
                  <FormLabel>Número de Parcelas</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      step="1"
                      placeholder="1"
                      {...field}
                      onChange={e => field.onChange(parseInt(e.target.value, 10) || 1)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

        </form>
      </Form>

      {/* Adiciona o drawer de contato */}
      <DrawerContato
        open={contatoDrawerOpen}
        onOpenChange={handleDrawerOpenChange}
        contatoId={editContatoId}
        onSuccess={handleContatoSuccess}
      />
    </>
  );
}