import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const { path: pathArray } = await params;
  const path = pathArray.join('/');
  const searchParams = request.nextUrl.searchParams;
  
  // Construir a URL para o Supabase
  const url = new URL(`https://ivmnkctopksvvzmbulah.supabase.co/rest/v1/${path}`);
  
  // Adicionar todos os parâmetros de consulta
  searchParams.forEach((value, key) => {
    url.searchParams.append(key, value);
  });
  
  // Copiar os cabeçalhos da requisição original
  const headers = new Headers();
  request.headers.forEach((value, key) => {
    headers.append(key, value);
  });
  
  // Adicionar cabeçalhos específicos do Supabase
  headers.set('apikey', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '');
  headers.set('Authorization', `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || ''}`);
  
  try {
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers,
    });
    
    const data = await response.json();
    
    return NextResponse.json(data, {
      status: response.status,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error('Erro ao fazer proxy para o Supabase:', error);
    return NextResponse.json(
      { error: 'Erro ao acessar o Supabase' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const { path: pathArray } = await params;
  const path = pathArray.join('/');
  
  // Construir a URL para o Supabase
  const url = new URL(`https://ivmnkctopksvvzmbulah.supabase.co/rest/v1/${path}`);
  
  // Copiar os cabeçalhos da requisição original
  const headers = new Headers();
  request.headers.forEach((value, key) => {
    headers.append(key, value);
  });
  
  // Adicionar cabeçalhos específicos do Supabase
  headers.set('apikey', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '');
  headers.set('Authorization', `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || ''}`);
  headers.set('Content-Type', 'application/json');
  
  try {
    // Obter o corpo da requisição
    const body = await request.json();
    
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    });
    
    const data = await response.json();
    
    return NextResponse.json(data, {
      status: response.status,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error('Erro ao fazer proxy para o Supabase:', error);
    return NextResponse.json(
      { error: 'Erro ao acessar o Supabase' },
      { status: 500 }
    );
  }
}

// Lidar com requisições OPTIONS para CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
