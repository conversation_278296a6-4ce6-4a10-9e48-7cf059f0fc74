---
description:
globs:
alwaysApply: false
---
# Componentes de Formulário - Guia de Implementação

## Padrões para Formulários React

Este guia contém as melhores práticas para implementação de formulários no projeto, focando em resolver problemas comuns e garantir consistência.

### Estrutura Básica com React Hook Form

O projeto utiliza React Hook Form para gerenciamento de estado de formulários. Sempre estruture seus formulários assim:

```tsx
// Definição do schema de validação
const formSchema = z.object({
  campo1: z.string().min(1, "Campo obrigatório"),
  campo2: z.number().optional(),
  // ... outros campos
});

// Dentro do componente
const form = useForm<z.infer<typeof formSchema>>({
  resolver: zodResolver(formSchema),
  defaultValues: {
    campo1: "",
    campo2: undefined,
    // ... valores iniciais para outros campos
  },
});

// Função de submit
function onSubmit(values: z.infer<typeof formSchema>) {
  // Lógica de processamento do formulário
}

// No JSX
<Form {...form}>
  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
    {/* Campos do formulário */}
  </form>
</Form>
```

## Problemas Comuns e Soluções

### 1. Estruturas HTML Aninhadas Inválidas

❌ **Problema**: Aninhar elementos proibidos, como `<button>` dentro de `<button>`.
✅ **Solução**: Use componentes alternativos para ações clicáveis aninhadas:

```tsx
// Correto
<Button>
  <span 
    role="button" 
    tabIndex={0}
    onClick={e => {
      e.stopPropagation();
      handleAction();
    }}
    onKeyDown={e => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.stopPropagation();
        handleAction();
      }
    }}
  >
    Ação Secundária
  </span>
</Button>
```

### 2. Limpeza de Campos em Formulários Controlados

❌ **Problema**: Usar `undefined` para limpar campos, causando comportamento inconsistente.
✅ **Solução**: Use `null` e atualização manual do estado:

```tsx
// Correto
const limparCampo = () => {
  form.setValue("campo", null);
  form.trigger("campo"); // Garante atualização de UI/validação
};
```

### 3. Componentes Select com Funcionalidade de Limpar

❌ **Problema**: Campos Select sem opção de limpar levam a UX frustrante.
✅ **Solução**: Implemente sempre opção de limpar seleções:

```tsx
// Exemplo com Popover + Command
<CommandGroup>
  {/* Itens normais... */}
  
  {field.value && (
    <CommandItem
      key="clear"
      value="limpar"
      className="text-destructive hover:text-destructive/90"
      onSelect={() => {
        form.setValue("campo_id", null);
        form.trigger("campo_id");
        setOpen(false);
      }}
    >
      <span className="mr-2">✕</span>
      Limpar seleção
    </CommandItem>
  )}
</CommandGroup>
```

### 4. Valores Vazios em Componentes Radix UI

❌ **Problema**: Usar `value=""` em `<SelectItem>` do Radix UI causa erros.
✅ **Solução**: Nunca use valor vazio:

```tsx
// Incorreto
<SelectItem value="">Selecione...</SelectItem>

// Correto
<SelectItem value="placeholder">Selecione...</SelectItem>
```

## Exemplos de Implementação

Para ver exemplos funcionais, consulte:

- [Formulário de Lançamento](mdc:src/app/(auth)/lancamentos/components/form-lancamento.tsx) - Exemplo completo com selects, datepicker e validações
- [DatePicker](mdc:src/components/ui/date-picker.tsx) - Componente de data com estado controlado

## Componentes Customizados para Formulários

### DatePicker

Use sempre a adaptação para não receber undefined:

```tsx
<DatePicker 
  date={value} 
  setDate={(date) => date !== undefined ? setValue(date) : null}
  placeholder="DD/MM/AAAA" 
/>
```

### Campos de Seleção + Criação

Para campos que permitem selecionar ou criar novos itens:

```tsx
<Popover open={open} onOpenChange={setOpen}>
  <PopoverTrigger asChild>
    <FormControl>
      <Button
        variant="outline"
        role="combobox"
        aria-expanded={open}
        className={cn("w-full justify-between", !field.value && "text-muted-foreground")}
      >
        {field.value ? itemsMap[field.value]?.nome : "Selecione..."}
        <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50 ml-1" />
      </Button>
    </FormControl>
  </PopoverTrigger>
  <PopoverContent className="p-0 z-[100] bg-background">
    <Command>
      <CommandInput placeholder="Buscar ou criar..." value={searchTerm} onValueChange={setSearchTerm} />
      <CommandList>
        <CommandEmpty>
          {searchTerm && (
            <CommandItem onSelect={() => handleCreate(searchTerm)}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Criar "{searchTerm}"
            </CommandItem>
          )}
        </CommandEmpty>
        <CommandGroup>
          {items.map((item) => (
            <CommandItem
              key={item.id}
              onSelect={() => {
                form.setValue("item_id", item.id);
                form.trigger("item_id");
                setOpen(false);
              }}
            >
              <Check className={cn("mr-2 h-4 w-4", field.value === item.id ? "opacity-100" : "opacity-0")} />
              {item.nome}
            </CommandItem>
          ))}
        </CommandGroup>
      </CommandList>
    </Command>
  </PopoverContent>
</Popover>
```

## Regras de Acessibilidade

- Adicione sempre `aria-label` em campos com significado não-óbvio
- Mantenha foco visível para navegação por teclado
- Garanta que mensagens de erro são lidas por leitores de tela
- Evite depender apenas de cores para feedback (use ícones e textos)
- Forneça feedback para ações longas (como salvamento ou carregamento)
