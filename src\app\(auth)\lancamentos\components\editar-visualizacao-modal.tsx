import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { Visualizacao } from './visualizacoes-dropdown';
import { useVisualizacoesService } from './filtros/visualizacoes-service';

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  visualizacao: Visualizacao | null;
  onVisualizacaoAtualizada: (atualizada: Visualizacao) => void;
}

export default function EditarVisualizacaoModal({ open, onOpenChange, visualizacao, onVisualizacaoAtualizada }: Props) {
  const [nome, setNome] = useState('');
  const [descricao, setDescricao] = useState('');
  const [loading, setLoading] = useState(false);
  const [erro, setErro] = useState<string | null>(null);
  const { atualizarVisualizacao } = useVisualizacoesService();

  // Carregar dados da visualização quando o modal abrir
  useEffect(() => {
    if (visualizacao && open) {
      setNome(visualizacao.nome);
      setDescricao(visualizacao.descricao || '');
      setErro(null);
    }
  }, [visualizacao, open]);

  async function handleAtualizar() {
    setErro(null);
    if (!nome.trim()) {
      setErro('O nome é obrigatório');
      return;
    }
    if (!visualizacao) {
      setErro('Visualização não encontrada');
      return;
    }

    setLoading(true);

    try {
      // Usar o serviço de visualizações para atualizar
      const visualizacaoAtualizada = await atualizarVisualizacao(
        visualizacao.id,
        nome,
        descricao || undefined,
        visualizacao.filtros_json // Manter os filtros existentes
      );

      if (!visualizacaoAtualizada) {
        setErro('Erro ao atualizar visualização');
        return;
      }

      onVisualizacaoAtualizada(visualizacaoAtualizada);
      toast.success('Visualização atualizada com sucesso');
      handleClose();

    } catch (e: any) {
      
      setErro(e.message || 'Erro inesperado');
    } finally {
      setLoading(false);
    }
  }

  function handleClose() {
    setNome('');
    setDescricao('');
    setErro(null);
    onOpenChange(false);
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[480px]">
        <DialogHeader>
          <DialogTitle>Editar visualização</DialogTitle>
          <DialogDescription>Atualize os detalhes da visualização salva.</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Nome <span className="text-destructive">*</span></label>
            <Input value={nome} onChange={e => setNome(e.target.value)} placeholder="Ex: Pendentes do Sete Lagos" maxLength={60} required />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Descrição <span className="text-muted-foreground">(opcional)</span></label>
            <Textarea
              value={descricao}
              onChange={e => setDescricao(e.target.value)}
              placeholder="Descrição opcional para esta visualização"
              className="resize-none h-[80px]"
              maxLength={200}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Filtros aplicados</label>
            <div className="bg-muted p-3 rounded-md text-sm">
              {visualizacao && Object.keys(visualizacao.filtros_json || {}).length > 0 ? (
                <pre className="whitespace-pre-wrap break-all">
                  {JSON.stringify(visualizacao?.filtros_json, null, 2)}
                </pre>
              ) : (
                <div className="text-muted-foreground italic">
                  Nenhum filtro selecionado. Esta visualização mostrará todos os lançamentos.
                </div>
              )}
            </div>
          </div>
          {erro && <div className="text-destructive text-sm">{erro}</div>}
          <div className="flex justify-end gap-2 pt-2">
            <Button variant="ghost" onClick={handleClose} disabled={loading}>Cancelar</Button>
            <Button onClick={handleAtualizar} disabled={loading || !nome.trim()}>{loading ? 'Salvando...' : 'Salvar alterações'}</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}