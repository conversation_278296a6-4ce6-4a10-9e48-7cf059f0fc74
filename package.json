{"name": "automacao-financeira", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.39.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.503.0", "next": "^15.3.1", "pdfjs-dist": "^5.2.133", "pix-utils": "^2.6.0", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.0", "react-pdf": "^9.2.1", "react-qr-code": "^2.0.15", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/jspdf": "^1.3.3", "@types/node": "^20.11.19", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.17", "eslint": "^9.28.0", "eslint-config-next": "^15.3.3", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}