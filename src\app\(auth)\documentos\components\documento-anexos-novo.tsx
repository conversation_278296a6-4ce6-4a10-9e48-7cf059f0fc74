'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { DocumentoAnexo, Documento } from '@/types/documentos';
import { getDocumentoAnexos, uploadDocumentoAnexo, excluirDocumentoAnexo } from '@/services/documento-anexos';
import { FileText, Eye, Plus, Download, Loader2, RefreshCw, ChevronDown, ChevronRight } from 'lucide-react';
import { toast } from 'sonner';
import { FilePreview } from '@/components/FilePreview';
import { useAuth } from '@/hooks/useAuth';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { cn } from '@/lib/utils';

interface DocumentoAnexosNovoProps {
  documento: Documento;
  disabled?: boolean;
  noTopBorder?: boolean;
}

export function DocumentoAnexosNovo({ documento, disabled = false, noTopBorder = false }: DocumentoAnexosNovoProps) {
  const [anexos, setAnexos] = useState<DocumentoAnexo[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewFile, setPreviewFile] = useState<{ url?: string; name?: string; type?: string }>({});
  const [previewMainDoc, setPreviewMainDoc] = useState(false);
  const [isOpen, setIsOpen] = useState(true);

  // Usar o hook de autenticação
  const { isAuthenticated, isLoading: authLoading, refreshAuth, checkAuth } = useAuth();

  // Estado para controlar erros de autenticação
  const [authError, setAuthError] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Carregar anexos ao montar o componente e quando a autenticação estiver pronta
  useEffect(() => {
    if (documento?.id && isAuthenticated && !authLoading) {
      loadAnexos();
    }
  }, [documento?.id, isAuthenticated, authLoading]);

  const loadAnexos = async () => {
    try {
      setLoading(true);
      setAuthError(false);
      setErrorMessage('');

      // Verificar se o usuário está autenticado antes de carregar os anexos
      if (!isAuthenticated) {
        const refreshed = await refreshAuth();
        if (!refreshed) {
          setAuthError(true);
          setErrorMessage('Você precisa estar autenticado para visualizar os anexos.');
          return;
        }
      }

      // Buscar os anexos diretamente do Supabase
      const data = await getDocumentoAnexos(documento.id);
      setAnexos(data);
    } catch (error: any) {
      
      setAuthError(true);
      setErrorMessage(error.message || 'Erro ao carregar anexos');
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    try {
      setUploading(true);
      setAuthError(false);
      setErrorMessage('');

      // Verificar se o usuário está autenticado antes de fazer upload
      if (!isAuthenticated) {
        setAuthError(true);
        setErrorMessage('Você precisa estar autenticado para adicionar anexos.');
        return;
      }

      // Upload de cada arquivo
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        await uploadDocumentoAnexo(file, documento.id);
      }

      // Recarregar a lista de anexos
      await loadAnexos();
      toast.success('Anexo(s) adicionado(s) com sucesso');
    } catch (error: any) {
      
      setAuthError(true);
      setErrorMessage(error.message || 'Erro ao adicionar anexo');
      toast.error(error.message || 'Erro ao adicionar anexo');
    } finally {
      setUploading(false);
      // Limpar o input de arquivo
      e.target.value = '';
    }
  };

  const handlePreviewMainDocument = () => {
    setPreviewFile({
      url: documento.arquivo_url,
      name: documento.nome,
      type: documento.tipo_arquivo
    });
    setPreviewMainDoc(true);
  };

  const handlePreviewAnexo = (anexo: DocumentoAnexo) => {
    setPreviewFile({
      url: anexo.arquivo_url,
      name: anexo.nome,
      type: anexo.tipo_arquivo
    });
    setPreviewOpen(true);
  };

  // Função para tentar novamente após erro de autenticação
  const handleRetryAfterAuthError = async () => {
    setAuthError(false);
    setErrorMessage('');
    const refreshed = await refreshAuth();
    if (refreshed) {
      loadAnexos();
    } else {
      await checkAuth();
      if (isAuthenticated) {
        loadAnexos();
      } else {
        setAuthError(true);
        setErrorMessage('Não foi possível autenticar. Por favor, recarregue a página.');
      }
    }
  };

  return (
    <div className="space-y-4">
      <Collapsible
        open={isOpen}
        onOpenChange={setIsOpen}
        className="rounded-md overflow-hidden border"
      >
        <div className="flex items-center justify-between p-3 bg-muted/30">
          <CollapsibleTrigger asChild>
            <div className="flex items-center gap-2 cursor-pointer">
              {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              <h3 className="text-base font-medium">Documentos e Anexos</h3>
            </div>
          </CollapsibleTrigger>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2 rounded-full"
              onClick={handlePreviewMainDocument}
            >
              <Eye className="h-4 w-4" />
              <span>Visualizar</span>
            </Button>

            {!disabled && !authError && isAuthenticated && !authLoading && (
              <div className="relative">
                <input
                  type="file"
                  id="file-upload"
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  onChange={handleFileChange}
                  multiple
                  disabled={uploading}
                  aria-label="Selecionar arquivos para upload"
                  title="Selecionar arquivos para upload"
                />
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2 rounded-full"
                  disabled={uploading}
                >
                  {uploading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Enviando...</span>
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4" />
                      <span>Anexo</span>
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>
        </div>

        <CollapsibleContent>
          <div className="p-3 space-y-4">
            {/* Documento principal */}
            <div className={cn(
              "border",
              noTopBorder && "border-t-0"
            )}>
              <div className="flex items-center justify-between p-3">
                <div className="flex items-center space-x-3">
                  <FileText className="h-5 w-5 text-blue-500" />
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="text-sm font-medium truncate max-w-[200px] md:max-w-[300px]">
                        {documento.nome}
                      </p>
                      <span className="text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded-full">Principal</span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Enviado em {new Date(documento.created_at).toLocaleDateString('pt-BR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric'
                      })} {new Date(documento.created_at).toLocaleTimeString('pt-BR', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })} • {documento.tipo_arquivo.includes('pdf') ? 'PDF' : documento.tipo_arquivo.split('/')[1]?.toUpperCase() || 'Arquivo'}
                    </p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handlePreviewMainDocument}
                    title="Visualizar documento"
                    className="h-8 w-8"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = documento.arquivo_url;
                      link.download = documento.nome;
                      link.click();
                    }}
                    title="Baixar documento"
                    className="h-8 w-8"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Anexos adicionais */}
            {loading || authLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-5 w-5 animate-spin mr-2" />
                <span>{authLoading ? 'Verificando autenticação...' : 'Carregando anexos...'}</span>
              </div>
            ) : authError ? (
              <div className="text-center p-4 border rounded-md bg-muted/50">
                <p className="text-sm text-destructive mb-2">{errorMessage || 'Erro ao carregar anexos'}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRetryAfterAuthError}
                  className="flex items-center gap-2 mx-auto"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span>Tentar novamente</span>
                </Button>
              </div>
            ) : anexos.length === 0 ? (
              <div className="flex items-center gap-3 p-4 border rounded-md">
                <div className="flex items-center justify-center w-6 h-6">
                  <Plus className="h-5 w-5 text-muted-foreground" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Nenhum anexo adicional</p>
                  <p className="text-xs text-muted-foreground">
                    Adicione documentos complementares relacionados a este lançamento
                  </p>
                </div>
              </div>
            ) : (
              <div className="border">
                {anexos.map((anexo) => (
                  <div key={anexo.id} className="flex items-center justify-between p-3 border-b last:border-b-0">
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-blue-500" />
                      <div>
                        <p className="text-sm font-medium truncate max-w-[200px] md:max-w-[300px]">
                          {anexo.nome}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Enviado em {new Date(anexo.created_at).toLocaleDateString('pt-BR', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric'
                          })} {new Date(anexo.created_at).toLocaleTimeString('pt-BR', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })} • {anexo.tipo_arquivo.includes('pdf') ? 'PDF' : anexo.tipo_arquivo.split('/')[1]?.toUpperCase() || 'Arquivo'}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handlePreviewAnexo(anexo)}
                        title="Visualizar anexo"
                        className="h-8 w-8"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          const link = document.createElement('a');
                          link.href = anexo.arquivo_url;
                          link.download = anexo.nome;
                          link.click();
                        }}
                        title="Baixar anexo"
                        className="h-8 w-8"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Componentes de preview */}
      <FilePreview
        file={previewFile}
        open={previewOpen}
        onOpenChange={setPreviewOpen}
      />
      <FilePreview
        file={previewFile}
        open={previewMainDoc}
        onOpenChange={setPreviewMainDoc}
        title={documento.nome}
      />
    </div>
  );
}