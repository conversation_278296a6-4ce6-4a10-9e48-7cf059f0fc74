'use client';

import React, { useEffect, useState } from 'react';
import { LancamentoComParcelas } from '@/types/lancamentos';
import { useLancamentosService } from '@/services/lancamentos';
import { useParcelasService } from '@/services/parcelas';
import { Parcela } from '@/types/lancamentos';
import { useSearchParams } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { getSignedUrlAnexo, extrairPathDeUrl } from '@/lib/supabase';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2, ChevronDown, ChevronUp, Loader2, Search, MoreVertical, MoreHorizontal, Paperclip, FileText } from 'lucide-react';
import { format, isToday, isFuture, parseISO, differenceInDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { cn, overlayStyles } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { DrawerParcela } from '@/app/(auth)/parcelas/components/drawer-parcela';
import { Checkbox } from '@/components/ui/checkbox';
import { AcoesEmMassa } from './acoes-em-massa';
import { FilePreview } from '@/components/FilePreview';

interface ListaLancamentosProps {
  onEdit: (lancamento: LancamentoComParcelas) => void;
  version?: number;
  filtroProjetoGlobal?: string;
}

interface BadgeStatus {
  text: string;
  className: string;
}

function calcularStatusParcela(parcela: Parcela): BadgeStatus | null {
  if (parcela.status === 'pago' || parcela.status === 'cancelado') {
    // Se estiver pago ou cancelado, não exibe badge
    return null;
  }

  if (!parcela.vencimento) {
    // Se não tiver vencimento, não exibe badge (Em aberto)
    return null;
  }

  const hoje = new Date();
  const vencimento = parseISO(parcela.vencimento + 'T00:00:00');
  const deltaDays = differenceInDays(vencimento, hoje);

  if (deltaDays < 0) {
    return {
      text: 'Atrasado',
      className: 'bg-red-600 text-white hover:bg-red-700'
    };
  }

  if (deltaDays === 0) {
    return {
      text: 'Vence hoje',
      className: 'bg-orange-500 text-white hover:bg-orange-600'
    };
  }

  if (deltaDays <= 7) {
    return {
      text: `${deltaDays} ${deltaDays === 1 ? 'dia' : 'dias'}`,
      className: 'bg-orange-300 text-orange-950 hover:bg-orange-400'
    };
  }

  // Se for > 7 dias, não exibe badge (Em aberto)
  return null;
}

function calcularStatusLancamento(lancamento: LancamentoComParcelas): BadgeStatus | null {
  if (lancamento.status === 'Pago') {
    // Se estiver pago, não exibe badge
    return null;
  }

  if (lancamento.status === 'Cancelado') {
    return {
      text: 'Cancelado',
      className: 'bg-gray-900 text-gray-100 hover:bg-black'
    };
  }

  // Encontrar a próxima parcela não paga
  const proximaParcela = lancamento.parcelas
    .filter((p: Parcela) => p.status !== 'pago' && p.status !== 'cancelado')
    .sort((a: Parcela, b: Parcela) => {
      if (!a.vencimento || !b.vencimento) return 0;
      return new Date(a.vencimento).getTime() - new Date(b.vencimento).getTime();
    })[0];

  if (!proximaParcela) {
    // Se não tiver próxima parcela, não exibe badge (Em aberto)
    return null;
  }

  return calcularStatusParcela(proximaParcela);
}

// Função para obter o badge apropriado conforme o status do lançamento
function getStatusBadgeClass(status: string): string {
  switch (status) {
    case 'Pago':
      return 'bg-green-600 text-white hover:bg-green-700';
    case 'Cancelado':
      return 'bg-gray-900 text-gray-100 hover:bg-black';
    case 'Em aberto':
    default:
      return 'bg-blue-500 text-white hover:bg-blue-600';
  }
}

export function ListaLancamentos({ onEdit, version = 0, filtroProjetoGlobal = 'todos' }: ListaLancamentosProps) {
  const [lancamentos, setLancamentos] = useState<LancamentoComParcelas[]>([]);
  const [loading, setLoading] = useState(true);
  const [lancamentoParaExcluir, setLancamentoParaExcluir] = useState<LancamentoComParcelas | null>(null);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const [updatingParcelaId, setUpdatingParcelaId] = useState<string | null>(null);
  const [drawerParcelaOpen, setDrawerParcelaOpen] = useState(false);
  const [parcelaSelecionada, setParcelaSelecionada] = useState<Parcela | null>(null);
  const [parcelaIndex, setParcelaIndex] = useState<number>(0);
  const [parcelaTotal, setParcelaTotal] = useState<number>(0);

  // Estados para seleção múltipla
  const [selectedLancamentos, setSelectedLancamentos] = useState<Record<string, boolean>>({});
  const [selectAll, setSelectAll] = useState(false);

  // Estados para preview de documentos
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewType, setPreviewType] = useState<'image' | 'pdf' | null>(null);
  const [previewFile, setPreviewFile] = useState<{ url?: string; name?: string; type?: string }>({});
  const [previewOpen, setPreviewOpen] = useState(false);

  // Estados para controlar a coluna fixa inteligente
  const [shouldStick, setShouldStick] = useState(false);
  const [tableContainerRef, setTableContainerRef] = useState<HTMLDivElement | null>(null);

  const searchParams = useSearchParams();
  const lancamentosService = useLancamentosService();
  const { updateParcelaStatus } = useParcelasService();
  const supabase = createClientComponentClient();

  // Obter filtros da URL
  const getFiltrosFromUrl = () => {
    const filtros: Record<string, any> = {};

    for (const [key, value] of searchParams.entries()) {
      if (key.startsWith('filtro_')) {
        const filtroKey = key.replace('filtro_', '');

        if (key.includes('data')) {
          filtros[filtroKey] = new Date(value);
        } else if (searchParams.getAll(key).length > 1) {
          // Decodificar cada valor em arrays
          filtros[filtroKey] = searchParams.getAll(key).map(v => decodeURIComponent(v));
        } else {
          filtros[filtroKey] = decodeURIComponent(value);
        }
      }
    }

    return filtros;
  };

  useEffect(() => {
    // Forçar atualização completa quando a versão mudar
    carregarLancamentos(true);
  }, [version, searchParams]);

  // Limpar seleções quando os filtros mudarem
  useEffect(() => {
    setSelectedLancamentos({});
    setSelectAll(false);
  }, [searchParams]);

  // Effect para controlar a coluna fixa inteligente
  useEffect(() => {
    if (!tableContainerRef) return;

    const handleScroll = () => {
      const container = tableContainerRef;
      const scrollWidth = container.scrollWidth;
      const clientWidth = container.clientWidth;
      const scrollLeft = container.scrollLeft;

      // Verifica se há overflow horizontal
      const hasHorizontalOverflow = scrollWidth > clientWidth;
      
      // Verifica se chegou no final do scroll (com margem de 5px)
      const isAtEnd = scrollLeft >= (scrollWidth - clientWidth - 5);

      // Só aplica sticky se há overflow e não está no final
      setShouldStick(hasHorizontalOverflow && !isAtEnd);
    };

    const checkOverflow = () => {
      handleScroll(); // Executa a verificação inicial
    };

    // Verificar quando o conteúdo muda
    checkOverflow();

    // Adicionar listeners
    tableContainerRef.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', checkOverflow);

    // Cleanup
    return () => {
      if (tableContainerRef) {
        tableContainerRef.removeEventListener('scroll', handleScroll);
      }
      window.removeEventListener('resize', checkOverflow);
    };
  }, [tableContainerRef, lancamentos]);

  async function carregarLancamentos(forceRefresh = false) {
    try {
      setLoading(true);

      // Se forceRefresh for true, limpar a lista atual antes de carregar novamente
      if (forceRefresh) {
        setLancamentos([]);

        // Limpar seleções ao recarregar
        setSelectedLancamentos({});
        setSelectAll(false);

        // Fechar todas as linhas expandidas
        setExpandedRows({});

        // Pequeno atraso para garantir que o estado anterior seja processado
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Adicionar timestamp para evitar cache
      const cacheBust = Date.now();

      // Tentar carregar os lançamentos com até 3 tentativas
      let data;
      let attempt = 0;
      const maxAttempts = 3;

      while (attempt < maxAttempts) {
        attempt++;
        try {
          data = await lancamentosService.getLancamentos({ cacheBust: cacheBust + attempt });

          if (data && Array.isArray(data)) {
            break; // Dados carregados com sucesso, sair do loop
          } else {
            if (attempt < maxAttempts) {
              // Aguardar antes da próxima tentativa
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }
        } catch (loadError) {
          if (attempt < maxAttempts) {
            // Aguardar antes da próxima tentativa
            await new Promise(resolve => setTimeout(resolve, 500));
          } else {
            throw loadError; // Repassar o erro após todas as tentativas
          }
        }
      }

      // Verificar se os dados foram carregados após todas as tentativas
      if (!data || !Array.isArray(data)) {
        throw new Error('Formato de dados inválido recebido do servidor');
      }

      // Verificar se há diferença entre a lista atual e a nova lista
      const currentIds = new Set(lancamentos.map(l => l.id));
      const newIds = new Set(data.map(l => l.id));
      const removedIds = [...currentIds].filter(id => !newIds.has(id));
      const addedIds = [...newIds].filter(id => !currentIds.has(id));

      if (removedIds.length > 0) {
        // Remover os IDs removidos das seleções
        const newSelectedLancamentos = { ...selectedLancamentos };
        removedIds.forEach(id => {
          delete newSelectedLancamentos[id];
        });
        setSelectedLancamentos(newSelectedLancamentos);

        // Remover os IDs removidos das linhas expandidas
        const newExpandedRows = { ...expandedRows };
        removedIds.forEach(id => {
          delete newExpandedRows[id];
        });
        setExpandedRows(newExpandedRows);
      }

      // Atualizar a lista de lançamentos
      setLancamentos(data);

      // Se forceRefresh for true, garantir que todas as seleções sejam limpas
      if (forceRefresh) {
        setSelectedLancamentos({});
        setSelectAll(false);
      }
    } catch (error) {
      toast.error('Erro ao carregar lançamentos');
    } finally {
      setLoading(false);
    }
  }

  // Função para selecionar/deselecionar todos os lançamentos
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);

    const newSelected: Record<string, boolean> = {};
    lancamentosFiltrados.forEach(lancamento => {
      newSelected[lancamento.id] = checked;
    });

    setSelectedLancamentos(newSelected);
  };

  // Função para limpar todas as seleções
  const handleClearSelection = () => {
    setSelectedLancamentos({});
    setSelectAll(false);
  };

  async function handleExcluir(e: React.MouseEvent, lancamento: LancamentoComParcelas) {
    e.stopPropagation();
    setLancamentoParaExcluir(lancamento);
  }

  async function confirmarExclusao() {
    if (!lancamentoParaExcluir) return;

    // Mostrar toast de carregamento
    const loadingToast = toast.loading('Excluindo lançamento...');

    try {
      // Excluir o lançamento usando o serviço melhorado
      const result = await lancamentosService.deleteLancamento(lancamentoParaExcluir.id);

      if (result) {
        // Remover toast de carregamento e mostrar sucesso
        toast.dismiss(loadingToast);
        toast.success('Lançamento excluído com sucesso');

        // Recarregar a lista de lançamentos com atualização forçada
        await carregarLancamentos(true);
      } else {
        throw new Error("Falha ao excluir lançamento: operação retornou falso");
      }
    } catch (error: any) {
      // Remover toast de carregamento
      toast.dismiss(loadingToast);

      // Mostrar mensagem de erro mais detalhada
      const errorMessage = error?.message || 'Erro desconhecido';
      toast.error(`Erro ao excluir lançamento: ${errorMessage}`);

      // Tentar recarregar a lista mesmo com erro, para garantir que a UI esteja atualizada
      try {
        await carregarLancamentos(true);
      } catch (refreshError) {
        // Ignorar erro de recarga
      }
    } finally {
      setLancamentoParaExcluir(null);
    }
  }

  const toggleRow = (id: string) => {
    setExpandedRows(prev => ({ ...prev, [id]: !prev[id] }));
  };

  async function handleMarcarPaga(e: React.MouseEvent, parcelaId: string) {
    e.stopPropagation();
    setUpdatingParcelaId(parcelaId);
    try {
      await updateParcelaStatus(parcelaId, 'pago');
      toast.success('Parcela marcada como paga!');
      await carregarLancamentos(true); // Forçar atualização completa
    } catch (error) {
      toast.error('Erro ao marcar parcela como paga.');
    } finally {
      setUpdatingParcelaId(null);
    }
  }

  async function handleMarcarPendente(e: React.MouseEvent, parcelaId: string) {
    e.stopPropagation();
    setUpdatingParcelaId(parcelaId);
    try {
      await updateParcelaStatus(parcelaId, 'pendente');
      toast.success('Parcela marcada como pendente!');
      await carregarLancamentos(true); // Forçar atualização completa
    } catch (error) {
      toast.error('Erro ao marcar parcela como pendente.');
    } finally {
      setUpdatingParcelaId(null);
    }
  }

  const formatCurrency = (value: number | null) => {
    if (value === null || value === undefined) return '-';
    return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
  };
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    try {
       const date = parseISO(dateString + 'T00:00:00');
       if (isNaN(date.getTime())) throw new Error('Data inválida');
       return format(date, 'dd/MM/yy', { locale: ptBR });
    } catch { return 'Data inválida'; }
  };

  // Função para lidar com o preview de documentos
  const handleDocumentPreview = async (e: React.MouseEvent, doc: any) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      // Extrair o path do arquivo da URL
      const path = extrairPathDeUrl(doc.arquivo_url);
      if (!path) {
        toast.error('Não foi possível obter o caminho do arquivo');
        return;
      }

      // Obter URL assinada para o arquivo
      const url = await getSignedUrlAnexo(path);

      // Determinar o tipo de arquivo para preview
      if (doc.tipo_arquivo?.startsWith('image/')) {
        setPreviewType('image');
        setPreviewUrl(url);
      } else if (doc.tipo_arquivo === 'application/pdf') {
        setPreviewType('pdf');
        setPreviewUrl(url);
      } else {
        // Se não for imagem ou PDF, abrir em nova aba
        window.open(url, '_blank');
      }
    } catch (error) {
      toast.error('Erro ao gerar link para visualização do documento');
    }
  };

  const calcularValores = (lancamento: LancamentoComParcelas) => {
    let valorPago = 0;
    lancamento.parcelas.forEach((p: Parcela) => {
      if (p.status === 'pago' && p.valor) {
        valorPago += p.valor;
      }
    });
    const valorEmAberto = (lancamento.valor_total ?? 0) - valorPago;
    return { valorPago, valorEmAberto };
  };

  // Função para verificar se um lançamento tem anexos
  const temAnexos = (lancamento: LancamentoComParcelas): boolean => {
    // Verificar se o lançamento tem documentos anexados
    if (lancamento.documentos && lancamento.documentos.length > 0) {
      return true;
    }

    // Verificar se alguma parcela tem anexos
    if (lancamento.parcelas && lancamento.parcelas.length > 0) {
      for (const parcela of lancamento.parcelas) {
        if (parcela.anexos && Array.isArray(parcela.anexos) && parcela.anexos.length > 0) {
          return true;
        }
      }
    }

    return false;
  };

  // Função para visualizar anexos de um lançamento
  const handleViewAttachments = async (e: React.MouseEvent, lancamento: LancamentoComParcelas) => {
    e.stopPropagation();

    // Verificar se tem documentos anexados ao lançamento
    if (lancamento.documentos && lancamento.documentos.length > 0) {
      try {
        const doc = lancamento.documentos[0]; // Pegar o primeiro documento
        const path = extrairPathDeUrl(doc.arquivo_url);
        if (!path) {
          toast.error('Não foi possível obter o caminho do arquivo');
          return;
        }

        // Obter URL assinada para o arquivo
        const url = await getSignedUrlAnexo(path);

        setPreviewFile({
          url,
          name: doc.nome,
          type: doc.tipo_arquivo
        });
        setPreviewOpen(true);
      } catch (error) {
        toast.error('Erro ao gerar link para visualização do documento');
      }
      return;
    }

    // Se não tem documentos, verificar se alguma parcela tem anexos
    if (lancamento.parcelas && lancamento.parcelas.length > 0) {
      for (const parcela of lancamento.parcelas) {
        if (parcela.anexos && Array.isArray(parcela.anexos) && parcela.anexos.length > 0) {
          try {
            const anexo = parcela.anexos[0]; // Pegar o primeiro anexo
            const path = anexo.path || (anexo.url ? extrairPathDeUrl(anexo.url) : undefined);
            if (!path) {
              toast.error('Não foi possível obter o caminho do arquivo');
              continue;
            }

            // Obter URL assinada para o arquivo
            const url = await getSignedUrlAnexo(path);

            setPreviewFile({
              url,
              name: anexo.nome || `Anexo da parcela ${parcela.numero}`,
              type: anexo.tipo || 'application/pdf'
            });
            setPreviewOpen(true);
            return;
          } catch (error) {
            toast.error('Erro ao gerar link para visualização do anexo');
          }
        }
      }
    }

    toast.info('Este lançamento não possui anexos');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-black"></div>
      </div>
    );
  }

  // Obter filtros da URL
  const filtros = getFiltrosFromUrl();

  // Filtrar lançamentos com base nos filtros da URL
  let lancamentosFiltrados = [...lancamentos];

  // Filtro de projeto (compatibilidade com o filtro antigo)
  if (filtroProjetoGlobal !== 'todos') {
    lancamentosFiltrados = lancamentosFiltrados.filter(l => l.obras?.nome === filtroProjetoGlobal);
  } else if (filtros.projeto) {
    lancamentosFiltrados = lancamentosFiltrados.filter(l => l.obras?.nome === filtros.projeto);
  }

  // Filtro de fornecedor
  if (filtros.fornecedor) {
    lancamentosFiltrados = lancamentosFiltrados.filter(l => {
      if (!l.contatos) return false;

      // Verificar todos os campos de nome do contato
      return (
        l.contatos.nome_empresa === filtros.fornecedor ||
        l.contatos.nome_razao === filtros.fornecedor ||
        l.contatos.nome_contato === filtros.fornecedor
      );
    });
  }

  // Filtro de data
  if (filtros.dataInicio || filtros.dataFim) {
    lancamentosFiltrados = lancamentosFiltrados.filter(l => {
      if (!l.data_competencia) return false;

      const dataLancamento = new Date(l.data_competencia);

      if (filtros.dataInicio && filtros.dataFim) {
        return dataLancamento >= filtros.dataInicio && dataLancamento <= filtros.dataFim;
      } else if (filtros.dataInicio) {
        return dataLancamento >= filtros.dataInicio;
      } else if (filtros.dataFim) {
        return dataLancamento <= filtros.dataFim;
      }

      return true;
    });
  }

  // Filtro de status
  if (filtros.status && filtros.status.length > 0) {
    lancamentosFiltrados = lancamentosFiltrados.filter(l =>
      Array.isArray(filtros.status)
        ? filtros.status.includes(l.status)
        : l.status === filtros.status
    );
  }

  return (
    <>
      {/* Removendo o filtro redundante, pois agora está na página principal */}
      {/* <div className="flex flex-wrap gap-4 mb-4">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Projeto:</span>
          <Select value={filtroProjetoGlobal} onValueChange={(value) => {
            // Removendo o estado local de filtroProjeto e usando o que foi passado como prop
            // setFiltroProjeto(value);
          }}>
            <SelectTrigger className="h-9 w-[180px]">
              <SelectValue placeholder="Todos os projetos" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="todos">Todos os projetos</SelectItem>
              {projetosUnicos.map(projeto => (
                <SelectItem key={projeto} value={projeto}>{projeto}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div> */}

      <div 
        ref={setTableContainerRef}
        className="rounded-md border w-full overflow-x-auto max-h-[70vh] overflow-y-auto"
        style={{ scrollbarWidth: 'thin' }}
      >
        <Table className="w-full table-fixed relative" key={`lancamentos-table-${lancamentos.length}-${version}`}>
          <TableHeader className="bg-muted sticky top-0 z-20">
            <TableRow className="bg-muted">
              <TableHead className="w-[40px] bg-muted">
                <Checkbox
                  checked={selectAll}
                  onCheckedChange={handleSelectAll}
                  aria-label="Selecionar todos"
                />
              </TableHead>
              <TableHead className="w-[50px] bg-muted"></TableHead>
              <TableHead className="w-[200px] bg-muted">Cliente/Fornecedor</TableHead>
              <TableHead className="w-[400px] bg-muted">Nome do Pagamento</TableHead>
              <TableHead className="w-[100px] bg-muted">Data</TableHead>
              <TableHead className="w-[150px] bg-muted">Projeto</TableHead>
              <TableHead className="w-[120px] bg-muted">Status</TableHead>
              <TableHead className="w-[120px] text-right bg-muted">Valor Total</TableHead>
              <TableHead className="w-[120px] text-right bg-muted">Valor Pago</TableHead>
              <TableHead className="w-[120px] text-right bg-muted">Em aberto</TableHead>
              <TableHead className={`w-[60px] text-center bg-muted border-l ${shouldStick ? 'sticky right-0 z-10' : ''}`}>
                Ações
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {lancamentosFiltrados.length === 0 ? (
              <TableRow>
                <TableCell colSpan={11} className="h-48 text-center">
                  <div className="flex flex-col items-center justify-center gap-2 py-8 text-muted-foreground">
                    <Search className="w-8 h-8 opacity-60 mb-1" />
                    <span className="font-medium text-base">Nenhum resultado para esta visualização</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              lancamentosFiltrados.map((lancamento) => {
                const isExpanded = expandedRows[lancamento.id];
                const statusBadge = calcularStatusLancamento(lancamento);
                const { valorPago, valorEmAberto } = calcularValores(lancamento);

                return (
                  <React.Fragment key={lancamento.id}>
                    <TableRow
                      onClick={() => toggleRow(lancamento.id)}
                      className="cursor-pointer hover:bg-muted/50"
                      data-state={isExpanded ? 'selected' : undefined}
                    >
                      <TableCell onClick={(e) => e.stopPropagation()}>
                        <Checkbox
                          checked={!!selectedLancamentos[lancamento.id]}
                          onCheckedChange={(checked) => {
                            setSelectedLancamentos(prev => ({
                              ...prev,
                              [lancamento.id]: !!checked
                            }));
                          }}
                          aria-label={`Selecionar lançamento ${lancamento.descricao}`}
                        />
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                        </Button>
                      </TableCell>
                      <TableCell className="w-[200px]">
                        <div className="truncate">
                          {lancamento.contatos?.nome_empresa || lancamento.contatos?.nome_razao || lancamento.contatos?.nome_contato || '-'}
                          {!lancamento.contatos && lancamento.contato_id && (
                            <span className="text-red-500">
                              Erro: Contato não carregado (ID: {lancamento.contato_id})
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium w-[400px]">
                        <div className="flex items-center gap-2 overflow-hidden">
                          <span className="truncate">{lancamento.descricao}</span>
                          {statusBadge && (
                            <Badge className={cn("text-xs font-medium border-transparent whitespace-nowrap", statusBadge.className)}>
                              {statusBadge.text}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="w-[100px]">{formatDate(lancamento.data_competencia)}</TableCell>
                      <TableCell className="w-[150px]">
                        <div className="truncate">
                          {lancamento.obras?.nome || '-'}
                        </div>
                      </TableCell>
                      <TableCell className="w-[120px]">
                        <Badge className={cn("text-xs font-medium min-w-[100px] justify-center", getStatusBadgeClass(lancamento.status))}>
                          {lancamento.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right w-[120px]">{formatCurrency(lancamento.valor_total)}</TableCell>
                      <TableCell className="text-right w-[120px]">{formatCurrency(valorPago)}</TableCell>
                      <TableCell className="text-right w-[120px]">{formatCurrency(valorEmAberto)}</TableCell>
                      <TableCell className={`border-l ${shouldStick ? 'sticky right-0 z-10 bg-background' : ''}`}>
                        <div className="flex justify-center">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className={cn("w-[160px]", overlayStyles())}>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onEdit(lancamento);
                                }}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                <span>Editar</span>
                              </DropdownMenuItem>
                              {temAnexos(lancamento) && (
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleViewAttachments(e, lancamento);
                                  }}
                                >
                                  <Paperclip className="mr-2 h-4 w-4" />
                                  <span>Ver Anexos</span>
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem
                                className="text-destructive focus:text-destructive"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleExcluir(e, lancamento);
                                }}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                <span>Excluir</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>

                    {isExpanded && (
                      <React.Fragment>
                        {/* Cabeçalho oculto para manter a estrutura da tabela */}
                        <TableRow className="hidden">
                          <TableHead className="w-[40px]"></TableHead>
                          <TableHead className="w-[50px]"></TableHead>
                          <TableHead className="w-[200px]"></TableHead>
                          <TableHead className="w-[400px]"></TableHead>
                          <TableHead className="w-[100px]"></TableHead>
                          <TableHead className="w-[150px]"></TableHead>
                          <TableHead className="w-[120px]"></TableHead>
                          <TableHead className="w-[120px]"></TableHead>
                          <TableHead className="w-[120px]"></TableHead>
                          <TableHead className="w-[120px]"></TableHead>
                          <TableHead className="w-[60px]"></TableHead>
                        </TableRow>

                        {lancamento.parcelas.length > 0 ? (
                          lancamento.parcelas.map((parcela: Parcela, index: number) => {
                            const parcelaStatus = calcularStatusParcela(parcela);

                            return (
                              <TableRow
                                key={parcela.id}
                                className="hover:bg-muted/20 text-sm w-full border-t border-muted/30">
                                <TableCell className="bg-muted/5 py-2">
                                  <Checkbox disabled checked={false} />
                                </TableCell>
                                <TableCell className="bg-muted/5 py-2"></TableCell>
                                <TableCell className="bg-muted/5 py-2"></TableCell>
                                <TableCell className="bg-muted/5 py-2 font-medium">
                                  <div className="flex items-center gap-2 overflow-hidden pl-4">
                                    <span className="truncate text-muted-foreground">Parcela {parcela.numero}</span>
                                  </div>
                                </TableCell>
                                <TableCell className="bg-muted/5 py-2 text-muted-foreground">
                                  {formatDate(parcela.vencimento)}
                                </TableCell>
                                <TableCell className="bg-muted/5 py-2"></TableCell>
                                <TableCell className="bg-muted/5 py-2 text-center">
                                  <div className="flex flex-col gap-1 items-center">
                                    {parcelaStatus ? (
                                      <Badge className={cn("min-w-[90px] justify-center px-2 py-0.5", parcelaStatus.className)}>
                                        {parcelaStatus.text}
                                      </Badge>
                                    ) : parcela.status !== undefined && (
                                      <Badge
                                        variant={parcela.status === 'pago' ? 'default' : 'outline'}
                                        className={`min-w-[90px] justify-center px-2 py-0.5 ${
                                          parcela.status === 'pago'
                                            ? 'bg-green-600 hover:bg-green-600 text-white'
                                            : parcela.status === 'cancelado'
                                              ? 'bg-slate-500 hover:bg-slate-500 text-white'
                                              : 'bg-yellow-500 hover:bg-yellow-500 text-yellow-950'
                                        }`}
                                      >
                                        {parcela.status === 'pago'
                                          ? 'Pago'
                                          : parcela.status === 'cancelado'
                                            ? 'Cancelado'
                                            : 'Pendente'}
                                      </Badge>
                                    )}
                                  </div>
                                </TableCell>
                                <TableCell className="bg-muted/5 py-2 text-right text-muted-foreground">
                                  {formatCurrency(parcela.valor)}
                                </TableCell>
                                <TableCell className="bg-muted/5 py-2 text-right"></TableCell>
                                <TableCell className="bg-muted/5 py-2 text-right"></TableCell>
                                <TableCell className={`bg-muted/5 py-2 border-l ${shouldStick ? 'sticky right-0 z-10' : ''}`}>
                                  <div className="flex justify-center">
                                    <DropdownMenu>
                                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                                        <Button
                                          variant="ghost"
                                          size="icon"
                                          className="h-8 w-8"
                                        >
                                          <MoreHorizontal className="h-4 w-4" />
                                        </Button>
                                      </DropdownMenuTrigger>
                                      <DropdownMenuContent align="end" className={cn("w-[180px]", overlayStyles())}>
                                        {(parcela.status !== 'pago' && parcela.status !== 'cancelado') ? (
                                          <DropdownMenuItem
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleMarcarPaga(e, parcela.id);
                                            }}
                                            disabled={updatingParcelaId === parcela.id}
                                          >
                                            {updatingParcelaId === parcela.id ? (
                                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            ) : null}
                                            <span>Marcar como Pago</span>
                                          </DropdownMenuItem>
                                        ) : parcela.status === 'pago' ? (
                                          <DropdownMenuItem
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleMarcarPendente(e, parcela.id);
                                            }}
                                            disabled={updatingParcelaId === parcela.id}
                                          >
                                            {updatingParcelaId === parcela.id ? (
                                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                            ) : null}
                                            <span>Marcar como Pendente</span>
                                          </DropdownMenuItem>
                                        ) : null}

                                        <DropdownMenuItem
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            const parcelaCompleta = {
                                              ...parcela,
                                              lancamentos: lancamento
                                            };
                                            setParcelaSelecionada(parcelaCompleta);
                                            setParcelaIndex(index + 1);
                                            setParcelaTotal(lancamento.parcelas.length);
                                            setDrawerParcelaOpen(true);
                                          }}
                                        >
                                          <Edit className="mr-2 h-4 w-4" />
                                          <span>Editar Parcela</span>
                                        </DropdownMenuItem>
                                        {parcela.anexos && Array.isArray(parcela.anexos) && parcela.anexos.length > 0 && (
                                          <DropdownMenuItem
                                            onClick={async (e) => {
                                              e.stopPropagation();
                                              try {
                                                const anexo = parcela.anexos[0];
                                                const path = anexo.path || (anexo.url ? extrairPathDeUrl(anexo.url) : undefined);
                                                if (!path) {
                                                  toast.error('Não foi possível obter o caminho do arquivo');
                                                  return;
                                                }

                                                // Obter URL assinada para o arquivo
                                                const url = await getSignedUrlAnexo(path);

                                                setPreviewFile({
                                                  url,
                                                  name: anexo.nome || `Anexo da parcela ${parcela.numero}`,
                                                  type: anexo.tipo || 'application/pdf'
                                                });
                                                setPreviewOpen(true);
                                              } catch (error) {
                                                toast.error('Erro ao gerar link para visualização do anexo');
                                              }
                                            }}
                                          >
                                            <Paperclip className="mr-2 h-4 w-4" />
                                            <span>Ver Anexos</span>
                                          </DropdownMenuItem>
                                        )}
                                      </DropdownMenuContent>
                                    </DropdownMenu>
                                  </div>
                                </TableCell>
                              </TableRow>
                            );
                          })
                        ) : (
                          <TableRow>
                            <TableCell colSpan={11} className="h-24 text-center text-muted-foreground">
                              Nenhuma parcela encontrada
                            </TableCell>
                          </TableRow>
                        )}

                        {/* Removida a exibição de documentos na tabela conforme solicitado */}
                      </React.Fragment>
                    )}
                  </React.Fragment>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>

      <Dialog open={!!lancamentoParaExcluir} onOpenChange={(open) => !open && setLancamentoParaExcluir(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir o lançamento "{lancamentoParaExcluir?.descricao}"? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setLancamentoParaExcluir(null)}>Cancelar</Button>
            <Button variant="destructive" onClick={confirmarExclusao}>Excluir</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <DrawerParcela
        open={drawerParcelaOpen}
        onOpenChange={setDrawerParcelaOpen}
        parcela={parcelaSelecionada}
        index={parcelaIndex}
        total={parcelaTotal}
        onSubmit={carregarLancamentos}
      />

      {/* Componente de ações em massa - só aparece quando há itens selecionados */}
      {Object.values(selectedLancamentos).some(Boolean) && (
        <AcoesEmMassa
          selectedIds={Object.entries(selectedLancamentos)
            .filter(([_, isSelected]) => isSelected)
            .map(([id]) => id)}
          onClearSelection={handleClearSelection}
          onActionComplete={(forceRefresh) => carregarLancamentos(forceRefresh)}
        />
      )}

      {/* Modal de preview de documentos (antigo) */}
      <Dialog open={!!previewUrl} onOpenChange={(open) => !open && setPreviewUrl(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Visualização do Documento</DialogTitle>
          </DialogHeader>
          <div className="flex justify-center items-center min-h-[300px] max-h-[70vh] overflow-auto">
            {previewType === 'image' && previewUrl && (
              <img
                src={previewUrl}
                alt="Documento"
                className="max-w-full max-h-[70vh] object-contain"
              />
            )}
            {previewType === 'pdf' && previewUrl && (
              <iframe
                src={previewUrl}
                className="w-full h-[70vh]"
                title="Visualização do PDF"
              />
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setPreviewUrl(null)}>Fechar</Button>
            <Button onClick={() => window.open(previewUrl!, '_blank')}>Abrir em Nova Aba</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Componente de preview de arquivo (novo) */}
      <FilePreview
        file={previewFile}
        open={previewOpen}
        onOpenChange={setPreviewOpen}
      />
    </>
  );
}