import React, { useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Download, ExternalLink, ZoomIn, ZoomOut } from 'lucide-react';

// Configurar o worker do PDF.js (usando worker local)
// Importante: react-pdf usa internamente pdfjs-dist@4.8.69
// Definir o worker diretamente
pdfjs.GlobalWorkerOptions.workerSrc = `/pdf-worker/pdf.worker.4.8.69.min.js`;

// Verificar se estamos no navegador e definir uma variável global para o worker
if (typeof window !== 'undefined') {
  // Isso pode ajudar outras instâncias do PDF.js a encontrar o worker
  window.pdfjsWorkerSrc = `/pdf-worker/pdf.worker.4.8.69.min.js`;
}

interface PdfViewerProps {
  fileUrl: string;
  fileName?: string;
  onDownload: () => void;
  onOpenExternal: () => void;
}

export function PdfViewer({ fileUrl, fileName, onDownload, onOpenExternal }: PdfViewerProps) {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
    setLoading(false);
  }

  function onDocumentLoadError(error: Error) {
    
    setError(error);
    setLoading(false);
  }

  function changePage(offset: number) {
    if (numPages) {
      setPageNumber(prevPageNumber => {
        const newPageNumber = prevPageNumber + offset;
        return Math.max(1, Math.min(newPageNumber, numPages));
      });
    }
  }

  function previousPage() {
    changePage(-1);
  }

  function nextPage() {
    changePage(1);
  }

  function zoomIn() {
    setScale(prevScale => Math.min(prevScale + 0.2, 3));
  }

  function zoomOut() {
    setScale(prevScale => Math.max(prevScale - 0.2, 0.5));
  }

  if (error) {
    return (
      <div className="w-full h-full flex flex-col items-center justify-center p-4">
        <div className="bg-white p-8 rounded-lg shadow-md text-center max-w-md">
          <div className="mb-6 text-6xl text-red-500">
            <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M14 3v4a1 1 0 0 0 1 1h4" />
              <path d="M17 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v11a2 2 0 0 1-2 2z" />
              <path d="M9 9h1" />
              <path d="M9 13h6" />
              <path d="M9 17h6" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-3">Não foi possível visualizar o PDF</h3>
          <p className="text-sm text-muted-foreground mb-6">
            Ocorreu um erro ao tentar carregar o PDF.
            Escolha uma das opções abaixo para visualizar o documento.
          </p>
          <div className="flex flex-col gap-3">
            <Button
              variant="default"
              onClick={onOpenExternal}
              className="w-full"
              size="lg"
            >
              <ExternalLink className="h-5 w-5 mr-2" />
              Abrir em nova aba
            </Button>
            <Button
              variant="outline"
              onClick={onDownload}
              className="w-full"
              size="lg"
            >
              <Download className="h-5 w-5 mr-2" />
              Baixar arquivo
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex justify-between items-center py-1 px-2 bg-muted/10 border-b">
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={previousPage}
            disabled={pageNumber <= 1}
            title="Página anterior"
            className="h-7 w-7"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-xs px-1">
            {pageNumber}/{numPages || '?'}
          </span>
          <Button
            variant="ghost"
            size="icon"
            onClick={nextPage}
            disabled={numPages === null || pageNumber >= numPages}
            title="Próxima página"
            className="h-7 w-7"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="icon" onClick={zoomOut} title="Diminuir zoom" className="h-7 w-7">
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={zoomIn} title="Aumentar zoom" className="h-7 w-7">
            <ZoomIn className="h-4 w-4" />
          </Button>
        </div>
      </div>
      <div className="flex-1 overflow-auto bg-white flex items-center justify-center">
        <Document
          file={fileUrl}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          loading={
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
            </div>
          }
        >
          {loading ? null : (
            <Page
              pageNumber={pageNumber}
              scale={scale}
              renderTextLayer={false}
              renderAnnotationLayer={false}
              className="pdf-page"
            />
          )}
        </Document>
      </div>
    </div>
  );
}