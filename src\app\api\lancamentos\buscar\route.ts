import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

// Função para criar um cliente Supabase com a chave de serviço
function createServiceClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    return null;
  }

  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

export async function GET(request: Request) {
  try {
    // Obter parâmetros da URL
    const url = new URL(request.url);
    const cacheBust = url.searchParams.get('cacheBust') || Date.now().toString();

    // Criar cliente de serviço
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      return NextResponse.json(
        { error: 'Não foi possível criar o cliente de serviço' },
        { status: 500 }
      );
    }

    // Buscar lançamentos diretamente da tabela com parcelas em uma única consulta
    const { data: lancamentosTabela, error: tabelaError } = await serviceClient
      .from("lancamentos")
      .select(`
        *,
        contatos:contato_id(*),
        obras:obra_id(*),
        categorias:categoria_id(*),
        documentos(id, nome, arquivo_url, arquivo_path, tipo_arquivo),
        parcelas(*)
      `)
      .order('created_at', { ascending: false });

    if (tabelaError) {
      return NextResponse.json(
        { error: `Erro ao buscar lançamentos: ${tabelaError.message}` },
        { status: 500 }
      );
    }

    if (!lancamentosTabela || !Array.isArray(lancamentosTabela)) {
      return NextResponse.json({ data: [] });
    }

    // Processar lançamentos com suas parcelas já carregadas
    const lancamentosComParcelas = lancamentosTabela.map(lancamento => {
      // Ordenar parcelas por número
      const parcelasOrdenadas = Array.isArray(lancamento.parcelas) 
        ? lancamento.parcelas.sort((a: any, b: any) => a.numero - b.numero)
        : [];

      return {
        ...lancamento,
        // Mapear tipo_lancamento para tipo para compatibilidade com o cliente
        tipo: lancamento.tipo_lancamento,
        parcelas: parcelasOrdenadas,
        contatos: lancamento.contatos,
        obras: lancamento.obras,
        categorias: lancamento.categorias,
        documentos: lancamento.documentos || []
      };
    });

    return NextResponse.json({ data: lancamentosComParcelas });
  } catch (error: any) {
    return NextResponse.json(
      { error: `Erro ao buscar lançamentos: ${error.message}` },
      { status: 500 }
    );
  }
}