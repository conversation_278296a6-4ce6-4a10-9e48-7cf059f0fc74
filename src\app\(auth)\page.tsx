'use client'

import {
  Building,
  DollarSign,
  FileClock,
  FileText,
  Plus,
  MoreHorizontal,
  ArrowDownToLine,
  RefreshCw,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ObraCard } from '@/components/ObraCard'
import { PageHeader } from '@/components/Layout/PageHeader'

// Mock data for summary cards (replace with actual data later)
const summaryCards = [
  { title: 'Obras Ativas', value: '12', icon: Building },
  { title: 'Orçamento Total', value: 'R$ 2.4M', icon: DollarSign },
  { title: 'Documentos Pendentes', value: '7', icon: FileClock },
  { title: 'Contratos Ativos', value: '18', icon: FileText },
]

// Mock data for Obras (replace with actual data later)
const obrasEmAndamento = [
  {
    id: '1',
    name: 'Residencial Vista Verde',
    location: 'São Paulo, SP',
    status: 'Em Progresso',
    statusColor: 'bg-green-100 text-green-800',
    progress: 65,
    progressColor: 'bg-green-500',
    avatars: [
      'https://picsum.photos/seed/vista1/24',
      'https://picsum.photos/seed/vista2/24',
      'https://picsum.photos/seed/vista3/24',
    ],
    deadline: 'Ago 2025',
  },
  {
    id: '2',
    name: 'Comercial Centro Empresarial',
    location: 'Rio de Janeiro, RJ',
    status: 'Em Análise',
    statusColor: 'bg-yellow-100 text-yellow-800',
    progress: 25,
    progressColor: 'bg-yellow-500',
    avatars: [
      'https://picsum.photos/seed/comercial1/24',
      'https://picsum.photos/seed/comercial2/24',
    ],
    deadline: 'Out 2025',
  },
  {
    id: '3',
    name: 'Residencial Park Avenue',
    location: 'Curitiba, PR',
    status: 'Iniciando',
    statusColor: 'bg-blue-100 text-blue-800',
    progress: 5,
    progressColor: 'bg-blue-500',
    avatars: [
      'https://picsum.photos/seed/park1/24',
      'https://picsum.photos/seed/park2/24',
      'https://picsum.photos/seed/park3/24',
    ],
    deadline: 'Dez 2025',
  },
]

// Mock data for Documentos Recentes (replace with actual data later)
const documentosRecentes = [
  {
    id: 'doc1',
    name: 'Contrato_Fornecedor_123.pdf',
    obra: 'Residencial Vista Verde',
    status: 'Pendente',
    statusColor: 'bg-yellow-100 text-yellow-800',
    data: '22/04/2025',
  },
  // Add more documents as needed
]

export default function HomePage() {
  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <div className="space-y-6">
      <PageHeader 
        title="Dashboard"
        description="Visão geral do sistema"
        actions={
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Atualizar
          </Button>
        }
      />

      <div className="space-y-8">
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {summaryCards.map((card) => (
            <div key={card.title} className="bg-white p-5 rounded-lg shadow">
              <div className="flex justify-between items-start">
                <div className="space-y-1">
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {card.title}
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {card.value}
                  </p>
                </div>
                <div className="p-2 bg-gray-100 rounded-lg">
                   <card.icon className="h-5 w-5 text-gray-600" />
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-foreground">Obras em Andamento</h2>
            <Button size="sm" variant="default">
              <Plus className="mr-1 h-4 w-4" />
              Nova Obra
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {obrasEmAndamento.map((obra) => (
              <ObraCard key={obra.id} obra={obra} />
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-foreground">Documentos Recentes</h2>
            <Button variant="link" size="sm" className="px-0">
              Ver todos
            </Button>
          </div>
          <div className="bg-card shadow rounded-lg border border-border overflow-hidden">
            <div className="divide-y divide-border">
              {documentosRecentes.map((doc) => (
                <div key={doc.id} className="px-4 py-3 grid grid-cols-10 gap-4 items-center hover:bg-accent/50">
                  <div className="col-span-4 flex items-center gap-3">
                    <FileText className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                    <span className="text-sm font-medium text-foreground truncate">{doc.name}</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-sm text-muted-foreground truncate">{doc.obra}</span>
                  </div>
                  <div className="col-span-1 flex justify-center">
                     <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${doc.statusColor}`}>
                       {doc.status}
                     </span>
                  </div>
                  <div className="col-span-2 text-right">
                     <span className="text-sm text-muted-foreground">{doc.data}</span>
                  </div>
                  <div className="col-span-1 flex justify-end gap-2">
                     <Button variant="ghost" size="icon" className="h-7 w-7" aria-label="Baixar documento">
                        <ArrowDownToLine className="h-4 w-4" />
                     </Button>
                     <Button variant="ghost" size="icon" className="h-7 w-7" aria-label="Mais opções">
                        <MoreHorizontal className="h-4 w-4" />
                     </Button>
                  </div>
                </div>
              ))}
            </div>
            {documentosRecentes.length === 0 && (
               <div className="px-4 py-4 text-center text-sm text-muted-foreground">
                  Nenhum documento recente encontrado.
               </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}