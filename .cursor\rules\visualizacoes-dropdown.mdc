---
description: 
globs: 
alwaysApply: true
---
# Padrão e Uso do Componente VisualizacoesDropdown

O componente `VisualizacoesDropdown` é responsável por exibir, selecionar, editar, excluir e salvar visualizações de filtros na tela de lançamentos.

- **Localização:** [`src/app/(auth)/lancamentos/components/filtros/visualizacoes-dropdown.tsx`](mdc:src/app/(auth)/lancamentos/components/filtros/visualizacoes-dropdown.tsx)

## Padrão Visual e Comportamento
- Os itens do dropdown usam fonte `text-sm font-normal` para consistência visual com o sistema.
- Os botões de editar (`Edit`) e excluir (`Trash`) aparecem diretamente ao lado do nome da visualização, mas **só ficam visíveis no hover** do item (`opacity-0 group-hover:opacity-100`).
- O botão de salvar visualização (`SalvarVisualizacaoButton`) aparece no final do dropdown, apenas se houver filtros ativos.
- O dropdown utiliza `Popover` e `ScrollArea` do shadcn/ui para responsividade e acessibilidade.

## Integração com Filtros
- O componente deve ser utilizado junto ao componente de filtros avançados, compondo o topo da tela de lançamentos.
- O padrão recomendado é:
  - Esquerda: `VisualizacoesDropdown` seguido dos filtros específicos (`FiltrosAvancados`)
  - Direita: Botão de ação (ex: "Novo Lançamento")
- Exemplo de uso integrado: ver [`FiltrosContainer`](mdc:src/app/(auth)/lancamentos/components/filtros/filtros-container.tsx)

## Observações de UX
- Os botões de ação só aparecem no hover para manter o visual clean.
- O botão de salvar visualização só é habilitado se houver filtros ativos.
- O componente é responsivo e mantém alinhamento limpo em diferentes tamanhos de tela.

## Arquivos Relacionados
- [`salvar-visualizacao-button.tsx`](mdc:src/app/(auth)/lancamentos/components/filtros/salvar-visualizacao-button.tsx)
- [`filtros-container.tsx`](mdc:src/app/(auth)/lancamentos/components/filtros/filtros-container.tsx)

---
**Manter este padrão para garantir consistência visual, usabilidade e integração entre filtros e visualizações.**

