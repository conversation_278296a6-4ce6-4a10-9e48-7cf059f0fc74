// Função SQL para executar SQL diretamente

export const executeSqlFunction = `
CREATE OR REPLACE FUNCTION execute_sql(sql_query TEXT, params JSONB DEFAULT '{}'::jsonb)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
BEGIN
  EXECUTE sql_query INTO result;
  RETURN result;
EXCEPTION WHEN OTHERS THEN
  RETURN jsonb_build_object(
    'error', SQLERRM,
    'detail', SQLSTATE,
    'query', sql_query
  );
END;
$$;
`;

// Função para verificar se a função existe
export const checkExecuteSqlFunctionExists = `
SELECT COUNT(*) AS count
FROM information_schema.routines 
WHERE routine_type = 'FUNCTION' 
AND routine_name = 'execute_sql';
`;