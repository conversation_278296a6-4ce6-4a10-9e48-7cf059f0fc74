import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import { uploadAnexo, extrairPathDeUrl } from '@/lib/supabase';
import { verificarAutenticacao } from '@/lib/auth-helpers';
import { DocumentosService } from '@/services/documentos-service';
import { createServiceClient } from '@/lib/supabase/service-client';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';

export const dynamic = 'force-dynamic';

// GET /api/documentos - Lista documentos
export async function GET(request: NextRequest) {
  try {
    // Obter status da query string, se disponível
    const url = new URL(request.url);
    const status = url.searchParams.get('status') as any;

    // Criar cliente Supabase diretamente com as variáveis de ambiente
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      return NextResponse.json(
        { error: 'Configuração do servidor incompleta' },
        { status: 500 }
      );
    }

    // Criar cliente Supabase
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // Verificar se devemos forçar o uso do cliente de serviço
    const forceServiceRole = url.searchParams.get('forceServiceRole') === 'true';

    // Usar o cliente de serviço se solicitado
    let clientToUse = supabase;

    if (forceServiceRole) {
      const serviceClient = createServiceClient();

      if (!serviceClient) {
        return NextResponse.json({ error: 'Erro ao criar cliente de serviço' }, { status: 500 });
      }

      clientToUse = serviceClient;
    }

    // Consulta com contagem de anexos
    const { data: documentos, error: queryError } = await clientToUse
      .from('documentos')
      .select(`
        *,
        documento_anexos (
          id
        )
      `)
      .order('created_at', { ascending: false });

    if (queryError) {
      return NextResponse.json(
        { error: 'Erro ao consultar documentos: ' + queryError.message },
        { status: 500 }
      );
    }

    // Processar documentos para incluir contagem de anexos
    let result = (documentos || []).map(documento => {
      const { documento_anexos, ...documentoLimpo } = documento;
      return {
        ...documentoLimpo,
        quantidade_anexos: documento_anexos ? documento_anexos.length : 0
      };
    });

    // Filtrar por status se necessário
    if (status && result.length > 0) {
      result = result.filter(doc => doc.status === status);
    }

    return NextResponse.json(result);
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Erro ao listar documentos: ' + error.message },
      { status: 500 }
    );
  }
}

// POST /api/documentos - Cria um novo documento
export async function POST(request: NextRequest) {
  try {
    // Verificar se é um FormData ou JSON
    const contentType = request.headers.get('content-type') || '';
    let userId: string | null = null;
    let formData: FormData | null = null;
    let jsonData: any = null;

    // Inicializar o cliente do Supabase para obter o usuário atual
    const supabase = await getSupabaseRouteClient();

    // Verificar a autenticação usando o helper
    const auth = await verificarAutenticacao(supabase);

    // Se não estiver autenticado, tentar com cliente de serviço como fallback
    if (!auth.autenticado) {
      // Se for FormData, extrair e processar
      if (contentType.includes('multipart/form-data')) {
        formData = await request.formData();
        userId = formData.get('userId') as string;
      } else {
        // Assumir que é JSON
        try {
          const clonedRequest = request.clone();
          jsonData = await clonedRequest.json();
          userId = jsonData.user_id;
        } catch (e) {
          // Erro ao extrair userId do body
        }
      }

      // Se não conseguiu obter userId do body, retornar erro
      if (!userId) {
        return NextResponse.json(
          { error: 'Não foi possível autenticar o usuário e nenhum userId foi fornecido no body' },
          { status: 401 }
        );
      }
    } else {
      // Se autenticado, usar o ID do usuário da sessão
      userId = auth.userId;
    }

    // Processar o FormData para obter o arquivo
    if (!formData) {
      formData = await request.formData();
    }
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'Arquivo não fornecido' }, { status: 400 });
    }

    // Upload do arquivo para o Supabase Storage
    const fileUrl = await uploadAnexo(file, 'documentos');
    const filePath = extrairPathDeUrl(fileUrl);

    // Usar o cliente de serviço para garantir que a inserção funcione
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      return NextResponse.json({ error: 'Erro ao criar cliente de serviço' }, { status: 500 });
    }

    // Criar registro do documento no banco usando o cliente de serviço
    const { data, error } = await serviceClient
      .from('documentos')
      .insert({
        nome: file.name,
        arquivo_url: fileUrl,
        arquivo_path: filePath,
        tipo_arquivo: file.type,
        status: 'pendente',
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data, { status: 201 });
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Erro ao criar documento: ' + error.message },
      { status: 500 }
    );
  }
}