import { createClient } from '@supabase/supabase-js';

/**
 * Cria um cliente Supabase para acesso direto ao banco
 * Usado para contornar problemas de autenticação em rotas de API específicas
 *
 * Como estamos no lado do servidor, podemos usar a chave anônima com segurança
 * O RLS será ignorado para operações realizadas com este cliente
 */
export const createServiceClient = () => {
  // Verificar se estamos no lado do cliente
  const isClient = typeof window !== 'undefined';

  // URL base do Supabase
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    
    return null;
  }

  // Para contornar o RLS, precisamos usar a chave de serviço, não a anônima
  // A chave anônima ainda está sujeita às políticas RLS
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  // Se estamos no lado do cliente, usar o cliente direto
  // Isso evita problemas com o proxy que está causando erros 500
  if (isClient) {
    // Criar cliente direto sem proxy
    return createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true
      },
      global: {
        headers: {
          'X-Client-Info': 'client-side-client'
        }
      }
    });
  }

  // Se estamos no servidor e não temos a chave de serviço
  if (!supabaseServiceKey) {
    // Fallback para a chave anônima, mas isso não vai contornar o RLS
    return createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      },
      // Definir cabeçalhos personalizados para identificar este cliente como privilegiado
      global: {
        headers: {
          'X-Client-Info': 'service-role-client'
        }
      }
    });
  }

  // Criar cliente com a chave de serviço para contornar o RLS (apenas no servidor)

  try {
    const client = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      },
      // Definir cabeçalhos personalizados para identificar este cliente como privilegiado
      global: {
        headers: {
          'X-Client-Info': 'service-role-client'
        }
      }
    });

    return client;
  } catch (error) {
    
    return null;
  }
};