'use client'

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { ListaObras } from './components/lista-obras';
import { DrawerObra } from './components/drawer-obra';
import { Obra } from '@/types/obras';

// Colunas padrão para a tabela
const defaultColumns = [
  { id: 'nome', label: 'Nome', isVisible: true },
  { id: 'descricao', label: 'Descrição', isVisible: true },
  { id: 'data_inicio', label: 'Data Início', isVisible: true },
  { id: 'status', label: 'Status', isVisible: true },
  { id: 'acoes', label: 'Ações', isVisible: true },
];

export default function ObrasPage() {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [obraSelecionada, setObraSelecionada] = useState<Obra | undefined>(undefined);
  const [version, setVersion] = useState(0);
  const [columns, setColumns] = useState(defaultColumns);

  const handleNovaObra = () => {
    setObraSelecionada(undefined);
    setDrawerOpen(true);
  };

  const handleEditarObra = (obra: Obra) => {
    setObraSelecionada(obra);
    setDrawerOpen(true);
  };

  const handleSalvarObra = async () => {
    setDrawerOpen(false);
    setObraSelecionada(undefined);
    // Trigger refresh da lista
    setVersion(prev => prev + 1);
  };

  return (
    <div className="page-container">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Obras</h1>
          <p className="text-muted-foreground">Gerencie suas obras e projetos</p>
        </div>
        <Button onClick={handleNovaObra} className="gap-2">
          <Plus className="h-4 w-4" />
          Nova Obra
        </Button>
      </div>

      <ListaObras 
        onEdit={handleEditarObra}
        version={version}
        columns={columns}
        onColumnsChange={setColumns}
      />

      <DrawerObra
        open={drawerOpen}
        onOpenChange={setDrawerOpen}
        obra={obraSelecionada}
        onSubmit={handleSalvarObra}
      />
    </div>
  );
}