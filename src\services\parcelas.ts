'use client';

import { Database } from "@/types/supabase";
// import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"; // Remover import antigo
import { supabase } from '@/lib/supabase/client'; // Importar cliente compartilhado

// const supabase = createClientComponentClient<Database>(); // Remover criação local

// Tipo para o status da parcela, baseado na documentação
type StatusParcela = Database["public"]["Enums"]["status_parcela"];

// Tipo para o anexo da parcela
export type ParcelaAnexo = {
  url: string;
  tipo: string;
  nome: string;
};

// Tipo para a Parcela com dados do Lançamento e Contato associados
export type ParcelaComDetalhes = Database["public"]["Tables"]["parcelas"]["Row"] & {
  anexos?: ParcelaAnexo[];
  lancamentos: {
    id: string;
    descricao: string;
    categoria_id?: string | null;
    contato_id?: string | null;
    obra_id?: string | null;
    data_competencia?: string | null;
    forma_pagamento?: Database["public"]["Enums"]["forma_pagamento"];
    tipo_lancamento?: Database["public"]["Enums"]["tipo_lancamento"];
    status?: Database["public"]["Enums"]["status_lancamento"];
    valor_total?: number;
    observacoes?: string | null;
    created_at?: string | null;
    updated_at?: string | null;
    contatos: {
      nome_empresa: string | null;
      nome_razao: string | null;
      nome_contato: string | null;
      chave_pix: string | null;
    } | null; // Contato pode ser nulo no lançamento
    obras: {
      nome: string | null;
    } | null; // Obra pode ser nula no lançamento
  } | null; // Lançamento pode ser nulo se a relação não for encontrada
};

// Variáveis para controle de throttle mais agressivo
let lastFetchTime = 0;
let pendingPromise: Promise<ParcelaComDetalhes[]> | null = null;
let cachedResult: ParcelaComDetalhes[] | null = null;
const THROTTLE_TIME = 5000; // 5 segundos para reduzir requisições
const CACHE_TIME = 10000; // 10 segundos de cache

// Função para buscar parcelas com detalhes
export async function getParcelas(filters?: { status?: StatusParcela }) {
  const now = Date.now();

  // Se existe cache válido, retornar do cache
  if (cachedResult && now - lastFetchTime < CACHE_TIME) {
    return cachedResult;
  }

  // Se já tiver uma requisição pendente, retornar ela
  if (pendingPromise) {
    return pendingPromise;
  }

  // Se última requisição foi muito recente, retornar cache se existir
  if (now - lastFetchTime < THROTTLE_TIME && cachedResult) {
    return cachedResult;
  }

  lastFetchTime = now;

  try {
    pendingPromise = fetchParcelasData(filters);
    const result = await pendingPromise;
    cachedResult = result;
    return result;
  } finally {
    // Limpar a promise pendente após conclusão
    setTimeout(() => {
      pendingPromise = null;
    }, 1000);
  }
}

// Função interna que realmente faz a busca
async function fetchParcelasData(filters?: { status?: StatusParcela }): Promise<ParcelaComDetalhes[]> {
  try {
    let query = supabase
      .from("parcelas")
      .select(`
        *,
        lancamentos:lancamento_id (
          *,
          contatos (*),
          obras (*)
        )
      `)
      .order("vencimento", { ascending: true }); // Ordenar por vencimento

    // Aplicar filtros
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    const { data, error } = await query;

    if (error) {
      // Melhor tratamento do erro
      throw new Error(`Erro ao buscar parcelas: ${error.message}`);
    }

    if (!data) {
      return [];
    }

    // Verificar se os dados possuem as estruturas aninhadas esperadas
    if (data.length > 0) {
      const parcelasInvalidas = data.filter(p => !p.lancamentos).length;
    }

    // Tipagem explícita para garantir que o TS entenda a estrutura do JOIN
    return data as ParcelaComDetalhes[];
  } catch (error) {
    // Melhoria no relançamento do erro
    if (error instanceof Error) {
      throw error; // Manter o erro original se for uma instância de Error
    } else {
      // Se não for um Error padrão, transformar em um com informações úteis
      throw new Error(`Erro ao processar parcelas: ${JSON.stringify(error)}`);
    }
  }
}

// Função para atualizar o status de uma parcela
export async function updateParcelaStatus(parcelaId: string, newStatus: StatusParcela, dataPagamento?: string | null) {
  // Define o tipo do updateData para corresponder à tabela parcelas
  const updateData: Database['public']['Tables']['parcelas']['Update'] = { status: newStatus };

  if (newStatus === 'pago') {
    updateData.data_pagamento = dataPagamento || new Date().toISOString();
  } else {
    updateData.data_pagamento = null;
  }

  const { data, error } = await supabase
    .from("parcelas")
    .update(updateData) // updateData agora tem o tipo correto
    .eq("id", parcelaId)
    .select()
    .single();

  if (error) {

    throw new Error(`Erro ao atualizar status da parcela: ${error.message}`);
  }

  return data;
}

// Função para atualizar todos os campos de uma parcela
export async function updateParcela(parcelaId: string, data: Partial<Database['public']['Tables']['parcelas']['Update']>) {
  // Processar os dados antes de enviar para a API
  const processedData: Partial<Database['public']['Tables']['parcelas']['Update']> = { ...data };

  // Garantir que o valor seja um número válido
  if (processedData.valor !== undefined) {
    // Converter para número se for string
    if (typeof processedData.valor === 'string') {
      const valorLimpo = processedData.valor.replace(/[^\d,.]/g, '').replace(',', '.');
      processedData.valor = parseFloat(valorLimpo);

      // Se for NaN ou inválido, usar um valor mínimo
      if (isNaN(processedData.valor) || processedData.valor <= 0) {
        processedData.valor = 0.01;
      }
    } else if (typeof processedData.valor === 'number') {
      // Garantir que o valor seja positivo
      if (processedData.valor <= 0) {
        processedData.valor = 0.01;
      }
    }
  }

  // Garantir que a data de vencimento esteja no formato correto
  if (processedData.vencimento) {
    // Se for uma data, converter para string no formato ISO
    if (processedData.vencimento instanceof Date) {
      const ano = processedData.vencimento.getFullYear();
      const mes = String(processedData.vencimento.getMonth() + 1).padStart(2, '0');
      const dia = String(processedData.vencimento.getDate()).padStart(2, '0');
      processedData.vencimento = `${ano}-${mes}-${dia}`;
    }
  }

  // Usar fetch para chamar a API diretamente em vez do cliente Supabase
  try {
    const response = await fetch(`/api/parcelas/${parcelaId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(processedData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Erro desconhecido' }));
      throw new Error(errorData.error || `Erro ao atualizar parcela: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    throw error instanceof Error ? error : new Error('Erro desconhecido ao atualizar parcela');
  }
}

// Função para atualizar múltiplas parcelas de uma vez
export async function updateMultiplasParcelas(
  lancamentoId: string,
  parcelas: Array<Partial<Database['public']['Tables']['parcelas']['Update']> & { id: string }>
) {
  // Verificar se temos parcelas para atualizar
  if (!parcelas || parcelas.length === 0) {
    throw new Error('Nenhuma parcela fornecida para atualização');
  }

  // Função para garantir que valores sejam números com 2 casas decimais
  const normalizarValor = (valor: any): number => {
    if (valor === null || valor === undefined) return 0;

    if (typeof valor === 'number') {
      return isNaN(valor) ? 0 : parseFloat(valor.toFixed(2));
    }

    if (typeof valor === 'string') {
      // Remover formatação e converter para número
      try {
        const valorLimpo = valor.replace(/[^\d,.]/g, '').replace(',', '.');
        const numero = parseFloat(valorLimpo);
        return isNaN(numero) ? 0 : parseFloat(numero.toFixed(2));
      } catch (e) {
        return 0;
      }
    }

    return 0;
  };

  // Normalizar valores das parcelas
  const parcelasNormalizadas = parcelas.map(parcela => ({
    ...parcela,
    valor: normalizarValor(parcela.valor)
  }));

  // Chamar o endpoint para atualização em lote
  const response = await fetch(`/api/lancamentos/${lancamentoId}/parcelas`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ parcelas: parcelasNormalizadas }),
  });

  // Verificar resposta
  if (!response.ok) {
    const errorData = await response.json();

    // Tratamento específico para erro de validação de valor total
    if (errorData.error && errorData.error.includes('valor total das parcelas')) {
      throw new Error(`${errorData.error}. Valor das parcelas: ${errorData.valorTotalParcelas}, Valor do lançamento: ${errorData.valorTotalLancamento}`);
    }

    throw new Error(errorData.error || 'Erro ao atualizar parcelas');
  }

  return response.json();
}

// Hook para facilitar o uso (opcional, mas boa prática)
export function useParcelasService() {
  return {
    getParcelas,
    updateParcelaStatus,
    updateParcela,
    updateMultiplasParcelas,
  };
}