// Função para criar as funções SQL diretamente no banco de dados

import { directGetAllVisualizacoes, directGetVisualizacaoById, directUpdateVisualizacao, directDeleteVisualizacao } from './sql-functions';
import { insertVisualizacaoBypassRls } from './insert-function';
import { directInsertVisualizacao } from './direct-insert-function';
import { executeSqlFunction } from './execute-sql';

// Criar tabela temporária para executar SQL
const createTempTable = `
CREATE TABLE IF NOT EXISTS _direct_api_temp (
  id SERIAL PRIMARY KEY,
  sql TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
`;

export const createAllFunctions = async (supabase: any): Promise<boolean> => {
  try {
    // Criar tabela temporária
    try {
      const { error: tableError } = await supabase
        .from('_direct_api_temp')
        .select('id')
        .limit(1);

      if (tableError && tableError.code === 'PGRST301') {
        // Tabela não existe, criar
        const { error: createError } = await supabase.rpc('execute_sql', {
          sql_query: createTempTable
        });

        if (createError) {
          // Tentar criar diretamente com SQL
          try {
            await supabase.sql(createTempTable);
          } catch (sqlError) {
            // Silently handle error
          }
        }
      }
    } catch (e) {
      // Silently handle error
    }

    // Criar função para executar SQL
    try {
      // Tentar criar a função diretamente
      const { error: execError } = await supabase.rpc('execute_sql', {
        sql_query: executeSqlFunction
      });

      if (execError) {
        // Tentar criar usando a tabela temporária
        try {
          const { error: insertError } = await supabase
            .from('_direct_api_temp')
            .insert({ sql: executeSqlFunction })
            .select();
        } catch (insertError) {
          // Silently handle error
        }
      }
    } catch (e) {
      // Silently handle error
    }

    // Criar funções de acesso
    const functions = [
      { name: 'direct_get_all_visualizacoes', sql: directGetAllVisualizacoes },
      { name: 'direct_get_visualizacao_by_id', sql: directGetVisualizacaoById },
      { name: 'direct_update_visualizacao', sql: directUpdateVisualizacao },
      { name: 'direct_delete_visualizacao', sql: directDeleteVisualizacao },
      { name: 'insert_visualizacao_bypass_rls', sql: insertVisualizacaoBypassRls },
      { name: 'direct_insert_visualizacao', sql: directInsertVisualizacao }
    ];

    for (const func of functions) {
      try {
        const { error } = await supabase.rpc('execute_sql', {
          sql_query: func.sql
        });
      } catch (e) {
        // Silently handle error
      }
    }

    return true;
  } catch (error) {
    return false;
  }
};