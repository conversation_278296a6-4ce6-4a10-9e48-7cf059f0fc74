import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

// Criar cliente Supabase com a chave de serviço para contornar o RLS
const createServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    return null;
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

export async function POST(request: NextRequest) {
  try {
    // Obter os IDs dos lançamentos a serem excluídos
    const { ids } = await request.json();

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'Lista de IDs não fornecida ou inválida' },
        { status: 400 }
      );
    }

    // Criar cliente de serviço
    const supabase = createServiceClient();
    if (!supabase) {
      return NextResponse.json(
        { error: 'Não foi possível criar o cliente de serviço' },
        { status: 500 }
      );
    }

    // Resultados para cada ID
    const results = [];
    const errors = [];

    // Processar cada ID individualmente
    for (const id of ids) {
      try {
        // Atualizar documentos para remover referência ao lançamento
        await supabase
          .from('documentos')
          .update({ lancamento_id: null })
          .eq('lancamento_id', id);

        // Excluir parcelas primeiro
        await supabase
          .from('parcelas')
          .delete()
          .eq('lancamento_id', id);

        // Excluir o lançamento
        const { error: deleteLancamentoError } = await supabase
          .from('lancamentos')
          .delete()
          .eq('id', id);

        if (deleteLancamentoError) {
          errors.push(`Erro ao excluir lançamento ${id}: ${deleteLancamentoError.message}`);
          results.push({ id, success: false, error: deleteLancamentoError.message });
        } else {
          results.push({ id, success: true });
        }
      } catch (error: any) {
        errors.push(`Erro ao processar lançamento ${id}: ${error.message}`);
        results.push({ id, success: false, error: error.message });
      }
    }

    // Verificar se todos os lançamentos foram excluídos
    const { data: remainingLancamentos } = await supabase
      .from('lancamentos')
      .select('id')
      .in('id', ids);

    // Adicionar erros para lançamentos que ainda existem
    if (remainingLancamentos && remainingLancamentos.length > 0) {
      for (const lancamento of remainingLancamentos) {
        if (!errors.some(e => e.includes(lancamento.id))) {
          errors.push(`Lançamento ${lancamento.id} não foi excluído após a operação`);
        }
      }
    }

    return NextResponse.json({
      success: errors.length === 0,
      results,
      errors
    });
  } catch (error: any) {
    return NextResponse.json(
      {
        success: false,
        error: `Erro interno: ${error.message}`
      },
      { status: 500 }
    );
  }
}