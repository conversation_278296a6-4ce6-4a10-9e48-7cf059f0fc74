import { supabase } from '@/lib/supabase/client';

/**
 * Tenta atualizar a sessão do usuário
 * @returns Promise<boolean> - true se a sessão foi atualizada com sucesso, false caso contrário
 */
export async function refreshSession(): Promise<boolean> {
  try {
    // Verificar se há uma sessão atual
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      
      return false;
    }
    
    if (!session) {
  
      return false;
    }
    
    // Tentar atualizar a sessão
    const { error: refreshError } = await supabase.auth.refreshSession();
    
    if (refreshError) {
      
      return false;
    }
    
    return true;
  } catch (error) {
    
    return false;
  }
}

/**
 * Verifica se o usuário está autenticado
 * @returns Promise<boolean> - true se o usuário estiver autenticado, false caso contrário
 */
export async function isAuthenticated(): Promise<boolean> {
  try {
    // Verificar se há uma sessão atual
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      
      return false;
    }
    
    if (session) {
      return true;
    }
    
    // Tentar obter o usuário como alternativa
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      
      return false;
    }
    
    return !!user;
  } catch (error) {
    
    return false;
  }
}