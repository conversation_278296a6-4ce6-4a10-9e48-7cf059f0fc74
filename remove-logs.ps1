# Script para remover todos os logs de console da aplicação
Write-Host "Iniciando remoção de logs de console..." -ForegroundColor Green

# Obter todos os arquivos TypeScript e JavaScript
$files = Get-ChildItem -Recurse -Path src -Include "*.ts","*.tsx","*.js","*.jsx" | Where-Object { $_.Name -notlike "*.test.*" -and $_.Name -notlike "*.spec.*" }

$totalFiles = 0
$modifiedFiles = 0

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Remover console.log
    $content = $content -replace '^\s*console\.log\([^;]*\);\s*$', ''
    $content = $content -replace '^\s*console\.log\([^;]*\);\s*\r?\n', ''
    $content = $content -replace '\s*console\.log\([^;]*\);\s*', ''
    
    # Remover console.error (manter apenas os críticos)
    $content = $content -replace '^\s*console\.error\([^;]*\);\s*$', ''
    $content = $content -replace '^\s*console\.error\([^;]*\);\s*\r?\n', ''
    
    # Remover console.warn
    $content = $content -replace '^\s*console\.warn\([^;]*\);\s*$', ''
    $content = $content -replace '^\s*console\.warn\([^;]*\);\s*\r?\n', ''
    
    # Remover console.info
    $content = $content -replace '^\s*console\.info\([^;]*\);\s*$', ''
    $content = $content -replace '^\s*console\.info\([^;]*\);\s*\r?\n', ''
    
    # Remover console.debug
    $content = $content -replace '^\s*console\.debug\([^;]*\);\s*$', ''
    $content = $content -replace '^\s*console\.debug\([^;]*\);\s*\r?\n', ''
    
    # Limpar linhas vazias excessivas
    $content = $content -replace '\r?\n\s*\r?\n\s*\r?\n', "`n`n"
    
    $totalFiles++
    
    if ($content -ne $originalContent) {
        Set-Content $file.FullName -Value $content -NoNewline
        $modifiedFiles++
        Write-Host "Modificado: $($file.FullName)" -ForegroundColor Yellow
    }
}

Write-Host "`nResumo:" -ForegroundColor Green
Write-Host "Arquivos verificados: $totalFiles" -ForegroundColor White
Write-Host "Arquivos modificados: $modifiedFiles" -ForegroundColor White

# Verificar quantos logs restam
$remainingLogs = Get-ChildItem -Recurse -Path src -Include "*.ts","*.tsx","*.js","*.jsx" | Select-String -Pattern "console\." | Measure-Object | Select-Object -ExpandProperty Count

Write-Host "Logs restantes: $remainingLogs" -ForegroundColor $(if ($remainingLogs -eq 0) { "Green" } else { "Red" })

Write-Host "`nRemoção de logs concluída!" -ForegroundColor Green 