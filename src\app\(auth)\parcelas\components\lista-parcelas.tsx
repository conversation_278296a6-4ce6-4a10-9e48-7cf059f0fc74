'use client';

import React, { useEffect, useState } from 'react';
import { LancamentoComParcelas, Parcela } from '@/types/lancamentos';
import { useParcelasService } from '@/services/parcelas';
import { ParcelaComDetalhes } from '@/services/parcelas';
import { useRouter } from 'next/navigation';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Edit, Loader2, ArrowDownAZ, ArrowUpAZ, ArrowDownUp, CalendarDays, ArrowDown10, ArrowUp10, CreditCard } from 'lucide-react';
import { format, parseISO, differenceInDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getSignedUrlAnexo } from '@/lib/supabase';

// Interface para controle interno de parcelas com valores relacionados
interface ParcelaEstendida {
  id: string;
  lancamento_id: string;
  numero: number;
  valor: number;
  vencimento: string | null;
  status: StatusParcela;
  data_pagamento: string | null;
  created_at: string | null;
  updated_at: string | null;
  total_parcelas?: number;
  valorPago: number;
  valorAberto: number;
  lancamentoDescricao: string;
  lancamentoId: string;
  lancamentoCompleto: LancamentoComParcelas;
  contatoNome: string;
  obraNome: string;
  numeroParcela: string; // ex: "2/5"
  lancamentos: LancamentoComParcelas;
}

type StatusParcela = string;

interface ListaParcelasProps {
  onEdit: (parcela: ParcelaEstendida, index: number, total: number) => void;
  version?: number;
  onAbrirPagamentosPendentes?: () => void;
}

interface BadgeStatus {
  text: string;
  className: string;
}

function calcularStatusParcela(parcela: ParcelaEstendida): BadgeStatus | null {
  const statusParcela = parcela.status as string;

  if (statusParcela === 'pago' || statusParcela === 'cancelado') {
    // Se estiver pago ou cancelado, não exibe badge
    return null;
  }

  if (!parcela.vencimento) {
    // Se não tiver vencimento, não exibe badge (Em aberto)
    return null;
  }

  const hoje = new Date();
  const vencimento = parseISO(parcela.vencimento + 'T00:00:00');
  const deltaDays = differenceInDays(vencimento, hoje);

  if (deltaDays < 0) {
    return {
      text: 'Atrasado',
      className: 'bg-red-600 text-white hover:bg-red-700'
    };
  }

  if (deltaDays === 0) {
    return {
      text: 'Vence hoje',
      className: 'bg-orange-500 text-white hover:bg-orange-600'
    };
  }

  if (deltaDays <= 7) {
    return {
      text: `${deltaDays} ${deltaDays === 1 ? 'dia' : 'dias'}`,
      className: 'bg-orange-300 text-orange-950 hover:bg-orange-400'
    };
  }

  // Se for > 7 dias, não exibe badge (Em aberto)
  return null;
}

// Função para formatar data
function formatDate(dateString?: string | null): string {
  if (!dateString) return '-';
  try {
    return format(parseISO(dateString), 'dd/MM/yy', { locale: ptBR });
  } catch (error) {
    // Erro silencioso ao formatar data
    return dateString;
  }
}

// Função para formatar valores em reais
function formatCurrency(value?: number | null): string {
  if (value === undefined || value === null) return 'R$ 0,00';
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
}

// Tipo para as colunas de ordenação
type SortColumn = 'fornecedor' | 'nome' | 'parcela' | 'data' | 'projeto' | 'valor' | null;
type SortDirection = 'asc' | 'desc' | null;

export function ListaParcelas({ onEdit, version = 0, onAbrirPagamentosPendentes }: ListaParcelasProps) {
  const [parcelas, setParcelas] = useState<ParcelaEstendida[]>([]);
  const [loading, setLoading] = useState(true);
  const [filtroObra, setFiltroObra] = useState<string>('todos');
  const [filtroStatus, setFiltroStatus] = useState<string>('todos');
  const [sortColumn, setSortColumn] = useState<SortColumn>('data');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [parcelasOriginais, setParcelasOriginais] = useState<ParcelaEstendida[]>([]);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewType, setPreviewType] = useState<string | null>(null);
  const parcelasService = useParcelasService();
  const router = useRouter();

  // Referência estável para o serviço de parcelas
  const parcelasServiceRef = React.useRef(parcelasService);

  // Atualizar a referência apenas quando necessário
  React.useEffect(() => {
    parcelasServiceRef.current = parcelasService;
  }, [parcelasService]);

  // Função para buscar dados de parcelas usando AbortController para cancelamento seguro
  useEffect(() => {
    // Criar controller para permitir cancelar a requisição se o componente for desmontado
    const controller = new AbortController();
    const signal = controller.signal;
    let isMounted = true;

    // Delay para evitar múltiplas requisições imediatas
    const timeoutId = setTimeout(() => {
      if (isMounted) {
        carregarParcelas();
      }
    }, 100);

    async function carregarParcelas() {
      if (!isMounted) return;

      setLoading(true);
      try {
        // Buscar parcelas com detalhes do lançamento e contato usando a referência estável
        const data = await parcelasServiceRef.current.getParcelas();

        // Verificar se componente ainda está montado antes de atualizar estado
        if (!isMounted) return;

        if (!data || data.length === 0) {
          setParcelas([]);
          setParcelasOriginais([]);
          setLoading(false);
          return;
        }

        // Processar os dados para o formato esperado pela tabela
        const parcelasProcessadas: ParcelaEstendida[] = [];

        for (const parcela of data) {
          // Verificar se parcela está definida
          if (!parcela) {
            continue;
          }

          // Verificar se o lançamento está definido
          if (!parcela.lancamentos) {
            continue;
          }

          // Skip parcelas que já foram totalmente pagas
          const statusParcela = parcela.status as string;
          if (statusParcela === 'pago' || statusParcela === 'cancelado') continue;

          // Extrair dados com verificações de nulos para evitar erros de tipo
          const lancamentoId = parcela.lancamento_id;
          const lancamentoDescricao = parcela.lancamentos.descricao || 'Desconhecido';
          // Obter o nome do fornecedor, tentando diferentes campos disponíveis
          let contatoNome = '-';
          if (parcela.lancamentos.contatos) {
            const contato = parcela.lancamentos.contatos;
            contatoNome = contato.nome_empresa || contato.nome_razao || contato.nome_contato || '-';
          }
          const obraNome = parcela.lancamentos.obras?.nome || '-';

          // Calcular valor em aberto
          const valorParcela = parcela.valor || 0;
          const valorPago = valorParcela * (statusParcela === 'pago' ? 1 : 0);
          const valorAberto = valorParcela - valorPago;

          // Só adicionar parcelas com valor em aberto
          if (valorAberto > 0) {
            // Determinar o número total de parcelas para exibir formato "2/5"
            const totalParcelas = data.filter(p => p && p.lancamento_id === lancamentoId).length;

            // Preparar objeto para parcelas
            const parcelas: Parcela[] = data
              .filter(p => p && p.lancamento_id === lancamentoId)
              .map(p => ({
                id: p!.id,
                lancamento_id: p!.lancamento_id,
                numero: p!.numero,
                valor: p!.valor,
                vencimento: p!.vencimento,
                status: p!.status,
                data_pagamento: p!.data_pagamento,
                created_at: p!.created_at,
                updated_at: p!.updated_at,
                anexos: p!.anexos
              }));

            // Objeto de contatos disponível em parcela.lancamentos.contatos

            // Construir objeto de lançamento completo para passar ao onEdit
            const lancamentoCompleto: LancamentoComParcelas = {
              id: lancamentoId,
              descricao: lancamentoDescricao,
              categoria_id: parcela.lancamentos.categoria_id || null,
              contato_id: parcela.lancamentos.contato_id || null,
              obra_id: parcela.lancamentos.obra_id || null,
              data_competencia: parcela.lancamentos.data_competencia || parcela.vencimento || new Date().toISOString().split('T')[0],
              forma_pagamento: parcela.lancamentos.forma_pagamento || 'pix',
              tipo_lancamento: parcela.lancamentos.tipo_lancamento || 'despesa',
              status: parcela.lancamentos.status || 'Em aberto',
              valor_total: parcela.lancamentos.valor_total || valorParcela,
              observacoes: parcela.lancamentos.observacoes || null,
              created_at: parcela.lancamentos.created_at || null,
              updated_at: parcela.lancamentos.updated_at || null,
              parcelas: parcelas,
              contatos: {
                nome_empresa: contatoNome,
                nome_razao: null,
                nome_contato: null,
                chave_pix: parcela.lancamentos.contatos?.chave_pix || null
              },
              obras: {
                nome: obraNome
              }
            };

            // Adicionar parcela processada
            parcelasProcessadas.push({
              id: parcela.id,
              lancamento_id: parcela.lancamento_id,
              numero: parcela.numero,
              valor: parcela.valor,
              vencimento: parcela.vencimento,
              status: parcela.status,
              data_pagamento: parcela.data_pagamento,
              created_at: parcela.created_at,
              updated_at: parcela.updated_at,
              valorPago,
              valorAberto,
              lancamentoDescricao,
              lancamentoId,
              lancamentoCompleto,
              contatoNome,
              obraNome,
              numeroParcela: `${parcela.numero}/${totalParcelas}`,
              lancamentos: lancamentoCompleto
            });
          }
        }

        // Ordenar e definir o estado
        const parcelasOrdenadas = ordenarParcelas(parcelasProcessadas);
        setParcelas(parcelasOrdenadas);
        setParcelasOriginais([...parcelasProcessadas]); // Guardar cópia da ordenação original
      } catch (error) {
        // Melhorar tratamento de erro
        if (!isMounted) return;

        toast.error(error instanceof Error ? error.message : "Ocorreu um erro ao carregar as parcelas");

        // Limpar estado em caso de erro
        setParcelas([]);
        setParcelasOriginais([]);
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    // Cleanup function - será chamada quando o componente for desmontado
    return () => {
      isMounted = false;
      controller.abort();
      clearTimeout(timeoutId);
    };
  }, [version]); // Remover parcelasService das dependências, usar apenas version

  // Filtrar parcelas pelos critérios selecionados
  const parcelasFiltradas = parcelas.filter(parcela => {
    // Aplicar filtro de projeto/obra
    if (filtroObra && filtroObra !== 'todos' && parcela.obraNome !== filtroObra) {
      return false;
    }

    // Aplicar filtro de status
    if (filtroStatus && filtroStatus !== 'todos') {
      const status = calcularStatusParcela(parcela);
      if (filtroStatus === 'atrasado' && (!status || !status.text.includes('Atrasado'))) {
        return false;
      }
      if (filtroStatus === 'hoje' && (!status || !status.text.includes('Vence hoje'))) {
        return false;
      }
      if (filtroStatus === 'proximos' && (!status || !(status.text.match(/^\d+\s+(dia|dias)$/)))) {
        return false;
      }
      if (filtroStatus === 'em_aberto' && status) {
        return false;
      }
    }

    return true;
  });

  // Obter lista de obras únicas para o filtro
  const obrasUnicas = Array.from(new Set(parcelas.map(p => p.obraNome))).filter(Boolean);

  // Ordenar parcelas com base na coluna e direção selecionadas
  const ordenarParcelas = (parcelas: ParcelaEstendida[]) => {
    // Se não houver coluna ou direção de ordenação, retornar as parcelas na ordem original
    if (sortColumn === null || sortDirection === null) {
      return parcelasOriginais.filter(parcela => {
        // Reaplicar os filtros nas parcelas originais
        if (filtroObra && filtroObra !== 'todos' && parcela.obraNome !== filtroObra) {
          return false;
        }

        if (filtroStatus && filtroStatus !== 'todos') {
          const status = calcularStatusParcela(parcela);
          if (filtroStatus === 'atrasado' && (!status || !status.text.includes('Atrasado'))) {
            return false;
          }
          if (filtroStatus === 'hoje' && (!status || !status.text.includes('Vence hoje'))) {
            return false;
          }
          if (filtroStatus === 'proximos' && (!status || !(status.text.match(/^\d+\s+(dia|dias)$/)))) {
            return false;
          }
          if (filtroStatus === 'em_aberto' && status) {
            return false;
          }
        }

        return true;
      });
    }

    return [...parcelas].sort((a, b) => {
      let comparacao = 0;

      switch (sortColumn) {
        case 'fornecedor':
          comparacao = a.contatoNome.localeCompare(b.contatoNome);
          break;
        case 'nome':
          comparacao = a.lancamentoDescricao.localeCompare(b.lancamentoDescricao);
          break;
        case 'parcela':
          // Ordena por número de parcela (extrai o número antes da barra)
          const numA = parseInt(a.numeroParcela.split('/')[0]) || 0;
          const numB = parseInt(b.numeroParcela.split('/')[0]) || 0;
          comparacao = numA - numB;
          break;
        case 'data':
          // Ordenar por data de vencimento
          if (!a.vencimento && !b.vencimento) comparacao = 0;
          else if (!a.vencimento) comparacao = 1;
          else if (!b.vencimento) comparacao = -1;
          else comparacao = new Date(a.vencimento).getTime() - new Date(b.vencimento).getTime();
          break;
        case 'projeto':
          comparacao = a.obraNome.localeCompare(b.obraNome);
          break;
        case 'valor':
          comparacao = (a.valor || 0) - (b.valor || 0);
          break;
        default:
          comparacao = 0;
      }

      // Inverter se a ordem for descendente
      return sortDirection === 'asc' ? comparacao : -comparacao;
    });
  };

  // Função para alterar a ordenação com três estados
  const handleSort = (column: SortColumn) => {
    if (sortColumn === column) {
      // Ciclo: asc -> desc -> null (ordem original)
      if (sortDirection === 'asc') {
        setSortDirection('desc');
      } else if (sortDirection === 'desc') {
        setSortColumn(null);
        setSortDirection(null);
      } else {
        // Se estiver nulo, voltar para ascendente
        setSortDirection('asc');
      }
    } else {
      // Se clicar em coluna diferente, define ela como a nova coluna de ordenação
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  // Renderiza o ícone de ordenação apropriado
  const renderSortIcon = (column: SortColumn) => {
    if (sortColumn !== column) {
      return <ArrowDownUp className="h-4 w-4 ml-1 inline-block text-muted-foreground opacity-50" />;
    }

    if (sortDirection === null) {
      return <ArrowDownUp className="h-4 w-4 ml-1 inline-block text-muted-foreground" />;
    }

    return sortDirection === 'asc'
      ? column === 'valor' || column === 'parcela'
        ? <ArrowDown10 className="h-4 w-4 ml-1 inline-block" />
        : <ArrowDownAZ className="h-4 w-4 ml-1 inline-block" />
      : column === 'valor' || column === 'parcela'
        ? <ArrowUp10 className="h-4 w-4 ml-1 inline-block" />
        : <ArrowUpAZ className="h-4 w-4 ml-1 inline-block" />;
  };

  // Componente para os cabeçalhos ordenáveis
  const SortableHeader = ({ column, children }: { column: SortColumn, children: React.ReactNode }) => (
    <div
      className="flex items-center cursor-pointer hover:text-primary"
      onClick={() => handleSort(column)}
    >
      {children}
      {renderSortIcon(column)}
    </div>
  );

  // Aplicar ordenação após filtragem
  const parcelasOrdenadas = ordenarParcelas(parcelasFiltradas);

  // Renderização do componente
  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Carregando parcelas...</span>
      </div>
    );
  }

  // Verificar se não há parcelas filtradas
  const temParcelas = parcelasOrdenadas && parcelasOrdenadas.length > 0;

  const handlePreview = async (anexo: any) => {
    try {
      const url = await getSignedUrlAnexo(anexo.path); // path salvo no banco
      setPreviewType(anexo.tipo?.startsWith('image/') ? 'image' : anexo.tipo === 'application/pdf' ? 'pdf' : null);
      setPreviewUrl(url);
    } catch (e) {
      // Trate o erro (ex: toast.error)
    }
  };

  return (
    <>
      {/* Filtros acima da tabela */}
      <div className="flex flex-wrap gap-4 mb-4 items-center justify-between">
        <div className="flex gap-4 flex-wrap">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Projeto:</span>
            <Select value={filtroObra} onValueChange={setFiltroObra}>
              <SelectTrigger className="h-9 w-[180px]">
                <SelectValue placeholder="Todos os projetos" />
              </SelectTrigger>
              <SelectContent className="bg-background text-foreground z-50">
                <SelectItem value="todos">Todos os projetos</SelectItem>
                {obrasUnicas.map(obra => (
                  <SelectItem key={obra} value={obra}>{obra}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Status:</span>
            <Select value={filtroStatus} onValueChange={setFiltroStatus}>
              <SelectTrigger className="h-9 w-[180px]">
                <SelectValue placeholder="Todos os status" />
              </SelectTrigger>
              <SelectContent className="bg-background text-foreground z-50">
                <SelectItem value="todos">Todos os status</SelectItem>
                <SelectItem value="atrasado">Atrasado</SelectItem>
                <SelectItem value="hoje">Vence hoje</SelectItem>
                <SelectItem value="proximos">Próximos 7 dias</SelectItem>
                <SelectItem value="em_aberto">Em aberto</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        {onAbrirPagamentosPendentes && (
          <Button
            onClick={onAbrirPagamentosPendentes}
            type="button"
            aria-label="Pagar pendentes"
            variant="outline"
            size="sm"
            className="h-8 gap-1"
          >
            <CreditCard className="h-4 w-4" />
            <span>Pagar pendentes</span>
          </Button>
        )}
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader className="bg-muted">
            <TableRow>
              <TableHead className="hidden md:table-cell">
                <SortableHeader column="fornecedor">
                  <span>Fornecedor/Cliente</span>
                </SortableHeader>
              </TableHead>
              <TableHead className="w-[400px]">
                <SortableHeader column="nome">
                  <span>Nome do Pagamento</span>
                </SortableHeader>
              </TableHead>
              <TableHead>
                <SortableHeader column="parcela">
                  <span>Parcela</span>
                </SortableHeader>
              </TableHead>
              <TableHead>
                <SortableHeader column="data">
                  <span>Data Venc.</span>
                </SortableHeader>
              </TableHead>
              <TableHead className="hidden md:table-cell">
                <SortableHeader column="projeto">
                  <span>Projeto</span>
                </SortableHeader>
              </TableHead>
              <TableHead className="text-right">
                <SortableHeader column="valor">
                  <span className="ml-auto">Valor Parcela</span>
                </SortableHeader>
              </TableHead>
              <TableHead className="w-[100px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!temParcelas ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  Nenhuma parcela encontrada
                </TableCell>
              </TableRow>
            ) : (
              parcelasOrdenadas.map((parcela, idx) => {
                const parcelaStatus = calcularStatusParcela(parcela);
                // Calcular total de parcelas do mesmo lançamento
                const totalParcelas = parcelasOrdenadas.filter(p => p.lancamentoId === parcela.lancamentoId).length;

                return (
                  <TableRow
                    key={parcela.id}
                    className="hover:bg-muted/50 cursor-pointer"
                    onClick={() => onEdit(
                      { ...parcela, lancamentos: parcela.lancamentos || parcela.lancamentoCompleto },
                      idx + 1,
                      totalParcelas
                    )}
                  >
                    <TableCell className="hidden md:table-cell">{parcela.contatoNome}</TableCell>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        {parcela.lancamentoDescricao}
                        {parcelaStatus && (
                          <Badge className={cn("text-xs font-medium border-transparent", parcelaStatus.className)}>
                            {parcelaStatus.text}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{parcela.numeroParcela}</TableCell>
                    <TableCell>{formatDate(parcela.vencimento)}</TableCell>
                    <TableCell className="hidden md:table-cell">{parcela.obraNome}</TableCell>
                    <TableCell className="text-right">{formatCurrency(parcela.valor)}</TableCell>
                    <TableCell>
                      <div className="flex items-center justify-end gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Abrir o drawer de edição da parcela
                            onEdit(
                              { ...parcela, lancamentos: parcela.lancamentos || parcela.lancamentoCompleto },
                              idx + 1,
                              totalParcelas
                            );
                          }}
                          title="Editar parcela"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>
    </>
  );
}