'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  Di<PERSON>Header,
  DialogFooter,
} from '@/components/ui/dialog';
import { CustomDialogContent } from '@/components/ui/custom-dialog';
import { Button } from '@/components/ui/button';
import {
  Loader2, ZoomIn, ZoomOut, RotateCw, Download, X, ExternalLink,
  MoreHorizontal, AlertCircle, FileText
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { getSignedUrlAnexo, extrairPathDeUrl } from '@/lib/supabase';
import { toast } from 'sonner';
import './file-preview.css';
import { PdfViewer } from './PdfViewer';

interface FilePreviewProps {
  /**
   * URL do arquivo ou caminho no Storage do Supabase
   */
  file: {
    url?: string;
    path?: string;
    name?: string;
    type?: string;
  };
  /**
   * Se o modal está aberto
   */
  open: boolean;
  /**
   * Callback para quando o estado de abertura muda
   */
  onOpenChange: (open: boolean) => void;
  /**
   * Título opcional para o modal
   */
  title?: string;
}

export function FilePreview({ file, open, onOpenChange, title }: FilePreviewProps) {
  const [loading, setLoading] = useState(true);
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [fileType, setFileType] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [pdfLoadError, setPdfLoadError] = useState(false);
  const [fallbackMode, setFallbackMode] = useState(false);

  // Determinar o tipo de arquivo com base na extensão ou tipo MIME
  const determineFileType = (url: string, type?: string): string => {
    if (type) {
      if (type.startsWith('image/')) return 'image';
      if (type === 'application/pdf') return 'pdf';
    }

    const lowercaseUrl = url.toLowerCase();
    if (lowercaseUrl.endsWith('.pdf')) return 'pdf';
    if (/\.(jpe?g|png|gif|webp|bmp)$/i.test(lowercaseUrl)) return 'image';

    return 'unknown';
  };

  // Obter URL do arquivo
  useEffect(() => {
    if (!open) return;

    const fetchFileUrl = async () => {
      setLoading(true);
      setError(null);

      try {
        let url: string;

        // Se temos um caminho no Storage, obter URL assinada
        if (file.path) {
          url = await getSignedUrlAnexo(file.path);
        }
        // Se temos uma URL direta
        else if (file.url) {
          // Se a URL parece ser do Supabase Storage, extrair o caminho e obter URL assinada
          if (file.url.includes('/anexos/')) {
            const path = extrairPathDeUrl(file.url);
            if (path) {
              try {
                url = await getSignedUrlAnexo(path);
              } catch (signedUrlError) {
                
                url = file.url;
              }
            } else {
              url = file.url;
            }
          } else {
            url = file.url;
          }
        } else {
          throw new Error('Nenhuma URL ou caminho fornecido');
        }

        // Adicionar timestamp para evitar cache
        url = url.includes('?')
          ? `${url}&t=${Date.now()}`
          : `${url}?t=${Date.now()}`;

        setFileUrl(url);
        setFileType(determineFileType(url, file.type));
      } catch (err) {
        
        setError('Não foi possível carregar o arquivo. Tente novamente.');
        toast.error('Erro ao carregar o arquivo');
      } finally {
        setLoading(false);
      }
    };

    fetchFileUrl();
  }, [open, file]);

  // Resetar estados quando o modal é fechado
  useEffect(() => {
    if (!open) {
      setScale(1);
      setRotation(0);
      setPdfLoadError(false);
      setFallbackMode(false);
    }
  }, [open]);

  // Funções de controle
  const zoomIn = () => setScale(prev => Math.min(prev + 0.25, 3));
  const zoomOut = () => setScale(prev => Math.max(prev - 0.25, 0.5));
  const rotate = () => setRotation(prev => (prev + 90) % 360);

  // Manipulador de eventos para o elemento embed
  const handlePdfLoad = () => {
    setLoading(false);
  };

  // Manipulador de erros para o elemento embed
  const handlePdfError = () => {
    setPdfLoadError(true);
    setLoading(false);
  };

  // Função para gerar a classe de transformação
  const getTransformClasses = () => {
    const scaleClass = `transform-scale-${Math.floor(scale * 100)}`.replace('.', '');
    const rotateClass = `transform-rotate-${rotation}`;
    return `${scaleClass} ${rotateClass}`;
  };

  const downloadFile = () => {
    if (!fileUrl) return;

    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = file.name || 'arquivo';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <CustomDialogContent className="w-[90vw] max-w-5xl h-[90vh] flex flex-col p-0 overflow-hidden">
        <DialogTitle className="sr-only">{title || file.name || 'Visualizar arquivo'}</DialogTitle>
        <div className="flex items-center justify-between px-3 py-1.5 border-b bg-gray-50/50">
          <div className="truncate max-w-[calc(100%-90px)] flex items-center">
            <span className="text-sm font-medium truncate">{title || file.name || 'Visualizar arquivo'}</span>
          </div>
          <div className="flex items-center gap-0.5">
            <Button variant="ghost" size="icon" onClick={downloadFile} title="Baixar arquivo" className="h-7 w-7">
              <Download className="h-3.5 w-3.5" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" title="Mais opções" className="h-7 w-7">
                  <MoreHorizontal className="h-3.5 w-3.5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {fileType === 'image' && (
                  <>
                    <DropdownMenuItem onClick={zoomIn}>
                      <ZoomIn className="mr-2 h-4 w-4" />
                      <span>Aumentar zoom</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={zoomOut}>
                      <ZoomOut className="mr-2 h-4 w-4" />
                      <span>Diminuir zoom</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={rotate}>
                      <RotateCw className="mr-2 h-4 w-4" />
                      <span>Rotacionar</span>
                    </DropdownMenuItem>
                  </>
                )}
                {fileType === 'pdf' && (
                  <DropdownMenuItem onClick={() => setPdfLoadError(!pdfLoadError)}>
                    <AlertCircle className="mr-2 h-4 w-4" />
                    <span>{pdfLoadError ? "Visualizar no navegador" : "Problemas para visualizar?"}</span>
                  </DropdownMenuItem>
                )}
                {fileUrl && (
                  <DropdownMenuItem onClick={() => window.open(fileUrl, '_blank')}>
                    <ExternalLink className="mr-2 h-4 w-4" />
                    <span>Abrir em nova janela</span>
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="flex-1 overflow-auto flex items-center justify-center bg-white">
          {loading && (
            <div className="flex flex-col items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin mb-2" />
              <span className="text-sm text-muted-foreground">Carregando arquivo...</span>
            </div>
          )}

          {error && (
            <div className="flex flex-col items-center justify-center text-destructive">
              <span>{error}</span>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => window.open(file.url || '', '_blank')}
              >
                Abrir em nova janela
              </Button>
            </div>
          )}

          {!loading && !error && fileUrl && (
            <>
              {fileType === 'image' && (
                <div className="flex items-center justify-center w-full h-full">
                  <div
                    className={`file-preview-image-container ${getTransformClasses()}`}
                  >
                    <img
                      src={fileUrl}
                      alt={file.name || 'Imagem'}
                      className="file-preview-image"
                    />
                  </div>
                </div>
              )}

              {fileType === 'pdf' && fileUrl && !pdfLoadError && (
                <div className="w-full h-full flex flex-col items-center justify-center p-4">
                  <PdfViewer
                    fileUrl={fileUrl}
                    fileName={file.name}
                    onDownload={downloadFile}
                    onOpenExternal={() => window.open(fileUrl, '_blank')}
                  />
                </div>
              )}

              {fileType === 'pdf' && fileUrl && pdfLoadError && (
                <div className="w-full h-full flex flex-col items-center justify-center p-4">
                  <div className="bg-white p-8 rounded-lg shadow-md text-center max-w-md">
                    <div className="mb-6 text-6xl text-red-500">
                      <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M14 3v4a1 1 0 0 0 1 1h4" />
                        <path d="M17 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v11a2 2 0 0 1-2 2z" />
                        <path d="M9 9h1" />
                        <path d="M9 13h6" />
                        <path d="M9 17h6" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold mb-3">Não foi possível visualizar o PDF</h3>
                    <p className="text-sm text-muted-foreground mb-6">
                      Ocorreu um erro ao carregar o visualizador de PDF.
                      Escolha uma das opções abaixo para visualizar o documento.
                    </p>
                    <div className="flex flex-col gap-3">
                      <Button
                        variant="default"
                        onClick={() => {
                          // Tentar usar o iframe como fallback
                          setPdfLoadError(false);
                          setFallbackMode(true);
                        }}
                        className="w-full"
                        size="lg"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                          <path d="M14 3v4a1 1 0 0 0 1 1h4" />
                          <path d="M17 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v11a2 2 0 0 1-2 2z" />
                        </svg>
                        Tentar visualizar no navegador
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => window.open(fileUrl, '_blank')}
                        className="w-full"
                        size="lg"
                      >
                        <ExternalLink className="h-5 w-5 mr-2" />
                        Abrir em nova aba
                      </Button>
                      <Button
                        variant="outline"
                        onClick={downloadFile}
                        className="w-full"
                        size="lg"
                      >
                        <Download className="h-5 w-5 mr-2" />
                        Baixar arquivo
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {fileType === 'pdf' && fileUrl && !pdfLoadError && fallbackMode && (
                <div className="w-full h-full flex flex-col items-center justify-center">
                  <iframe
                    src={fileUrl}
                    title="PDF Preview"
                    className="w-full h-full border-0"
                    onError={() => {
                      setPdfLoadError(true);
                      setFallbackMode(false);
                    }}
                  />
                </div>
              )}

              {fileType === 'unknown' && (
                <div className="flex flex-col items-center justify-center">
                  <span className="text-sm text-muted-foreground mb-2">
                    Não é possível visualizar este tipo de arquivo.
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadFile}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Baixar arquivo
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </CustomDialogContent>
    </Dialog>
  );
}