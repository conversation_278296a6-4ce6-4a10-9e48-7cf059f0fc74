-- Habilitar RLS para a tabela documentos
ALTER TABLE documentos ENABLE ROW LEVEL SECURITY;

-- Política para permitir leitura de documentos para usuários autenticados
CREATE POLICY "Permitir leitura de documentos para usuários autenticados" ON documentos
  FOR SELECT
  TO authenticated
  USING (true);

-- Política para permitir inserção de documentos para usuários autenticados
CREATE POLICY "Permitir inserção de documentos para usuários autenticados" ON documentos
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Política para permitir atualização de documentos para usuários autenticados
CREATE POLICY "Permitir atualização de documentos para usuários autenticados" ON documentos
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Política para permitir exclusão de documentos para usuários autenticados
CREATE POLICY "Permitir exclusão de documentos para usuários autenticados" ON documentos
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Adicionar índices para melhorar a performance
CREATE INDEX IF NOT EXISTS idx_documentos_user_id ON documentos(user_id);
CREATE INDEX IF NOT EXISTS idx_documentos_status ON documentos(status);
CREATE INDEX IF NOT EXISTS idx_documentos_lancamento_id ON documentos(lancamento_id);

-- Adicionar comentários nas colunas
COMMENT ON TABLE documentos IS 'Armazena documentos enviados pelos usuários para análise e processamento';
COMMENT ON COLUMN documentos.id IS 'ID único do documento';
COMMENT ON COLUMN documentos.nome IS 'Nome original do arquivo';
COMMENT ON COLUMN documentos.arquivo_url IS 'URL pública do arquivo';
COMMENT ON COLUMN documentos.arquivo_path IS 'Caminho do arquivo no storage';
COMMENT ON COLUMN documentos.tipo_arquivo IS 'Tipo MIME do arquivo';
COMMENT ON COLUMN documentos.status IS 'Status do documento: pendente, aprovado ou rejeitado';
COMMENT ON COLUMN documentos.fornecedor IS 'Nome do fornecedor extraído do documento';
COMMENT ON COLUMN documentos.valor_total IS 'Valor total extraído do documento';
COMMENT ON COLUMN documentos.data_vencimento IS 'Data de vencimento extraída do documento';
COMMENT ON COLUMN documentos.motivo_rejeicao IS 'Motivo da rejeição do documento';
COMMENT ON COLUMN documentos.dados_extraidos IS 'Dados extraídos do documento pela IA em formato JSON';
COMMENT ON COLUMN documentos.user_id IS 'ID do usuário que enviou o documento';
COMMENT ON COLUMN documentos.lancamento_id IS 'ID do lançamento associado ao documento (quando aprovado)';
COMMENT ON COLUMN documentos.created_at IS 'Data de criação do registro';
COMMENT ON COLUMN documentos.updated_at IS 'Data da última atualização do registro';

-- Criar trigger para atualizar o campo updated_at automaticamente
CREATE OR REPLACE FUNCTION set_documentos_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS set_documentos_updated_at ON documentos;
CREATE TRIGGER set_documentos_updated_at
BEFORE UPDATE ON documentos
FOR EACH ROW
EXECUTE FUNCTION set_documentos_updated_at();
