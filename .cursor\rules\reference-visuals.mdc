---
description:
globs:
alwaysApply: false
---
# Trabalhando com Referências Visuais

## Processo de Implementação Visual

Este documento define o processo para implementar interfaces baseadas em referências visuais (mockups, protótipos, screenshots de produção).

### Etapas para Replicação de Interfaces

1. **Análise da Referência Visual**
   - Identifique os principais componentes e sua hierarquia
   - Observe padrões de cores, espaçamentos e tipografia
   - Note elementos interativos e seus diferentes estados

2. **Mapeamento dos Componentes**
   - Identifique componentes existentes no projeto que podem ser reutilizados
   - Documente quaisquer novos componentes que precisem ser criados
   - Priorize os elementos por importância visual e funcional

3. **Implementação Sistemática**
   - Comece com o layout de container e estrutura básica
   - Implemente os componentes principais primeiro
   - Adicione detalhes e refinamentos depois de ter a estrutura básica

4. **Verificação e Ajustes Finos**
   - Compare lado a lado com a referência visual
   - Ajuste tamanhos, espaçamentos e cores para aproximar ao máximo
   - Verifique em diferentes tamanhos de tela (responsividade)

## Formato de Solicitação Eficiente

Para solicitar implementações visuais de forma eficiente, use este formato:

```
Referência visual: [imagem/link]
Alvo: [arquivo específico a ser modificado]
Prioridade: [aspectos mais importantes da implementação]
Detalhes críticos: [comportamentos que devem ser preservados]
Nível de fidelidade: [percentual desejado de semelhança]
Padrões a seguir: [design system/convenções do projeto]
```

## Exemplo de Implementação Bem-Sucedida

O [Drawer de Pagamentos Pendentes](mdc:src/app/(auth)/parcelas/components/drawer-pagamentos-pendentes.tsx) é um exemplo de implementação visual bem-sucedida, com:

- Layout de duas colunas seguindo a referência visual
- Componentes customizados para área de upload e exibição de QR code
- Tipografia e espaçamentos alinhados com o design desejado
- Adaptações funcionais que mantêm a aparência visual consistente

## Ferramentas Úteis para Comparação Visual

Durante o processo de desenvolvimento, utilize estas técnicas:

1. **Comparação em Janelas Lado a Lado**
   - Coloque a referência visual e a implementação em janelas lado a lado
   - Use a mesma escala para comparação justa

2. **Overlay com Transparência (para casos críticos)**
   - Para interfaces que exigem precisão pixel-perfect, considere usar overlays
   - Ferramenta recomendada: PerfectPixel ou similar

3. **Extração de Cores e Medidas**
   - Use ferramentas de color picker para obter cores exatas
   - Meça distâncias em pixels na referência para replicar proporções

## Princípios de Design a Manter

1. **Consistência Visual** - Mantenha padrões consistentes em todo o aplicativo
2. **Hierarquia Clara** - Preserve a hierarquia visual dos elementos
3. **Feedback Visual** - Mantenha indicadores visuais claros para ações
4. **Espaçamento Adequado** - Respeite margens e paddings da referência
5. **Responsividade** - Adapte o design para diferentes tamanhos de tela
