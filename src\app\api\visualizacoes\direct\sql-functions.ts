// Funções SQL para acesso direto às visualizações
// Estas funções devem ser executadas no banco de dados Supabase

export const directGetAllVisualizacoes = `
CREATE OR REPLACE FUNCTION direct_get_all_visualizacoes()
RETURNS SETOF visualizacoes
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT * FROM visualizacoes
  ORDER BY updated_at DESC;
END;
$$;
`;

export const directGetVisualizacaoById = `
CREATE OR REPLACE FUNCTION direct_get_visualizacao_by_id(p_id UUID)
RETURNS SETOF visualizacoes
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT * FROM visualizacoes
  WHERE id = p_id;
END;
$$;
`;

export const directUpdateVisualizacao = `
CREATE OR REPLACE FUNCTION direct_update_visualizacao(
  p_id UUID,
  p_nome TEXT,
  p_descricao TEXT,
  p_filtros_json JSONB
)
RETURNS visualizacoes
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_result visualizacoes;
BEGIN
  UPDATE visualizacoes
  SET 
    nome = p_nome,
    descricao = p_descricao,
    filtros_json = p_filtros_json,
    updated_at = NOW()
  WHERE id = p_id
  RETURNING * INTO v_result;
  
  RETURN v_result;
END;
$$;
`;

export const directDeleteVisualizacao = `
CREATE OR REPLACE FUNCTION direct_delete_visualizacao(p_id UUID)
RETURNS visualizacoes
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_result visualizacoes;
BEGIN
  DELETE FROM visualizacoes
  WHERE id = p_id
  RETURNING * INTO v_result;
  
  RETURN v_result;
END;
$$;
`;

// Função para verificar se as funções existem
export const checkFunctionsExist = `
SELECT COUNT(*) AS count
FROM information_schema.routines 
WHERE routine_type = 'FUNCTION' 
AND routine_name IN (
  'direct_get_all_visualizacoes',
  'direct_get_visualizacao_by_id',
  'direct_update_visualizacao',
  'direct_delete_visualizacao'
);
`;