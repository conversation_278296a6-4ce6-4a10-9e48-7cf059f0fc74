'use client';

import { Database } from "@/types/supabase"
// import { createClientComponentClient } from "@supabase/auth-helpers-nextjs" // Remover import antigo
import { supabase } from '@/lib/supabase/client'; // Importar cliente compartilhado
import { createServiceClient } from '@/lib/supabase/service-client'; // Importar cliente de serviço
import { LancamentoComParcelas } from "@/types/lancamentos"; // Adicionar esta importação

// const supabase = createClientComponentClient<Database>() // Remover criação local

// Copiando tipos do formulário
type FormaPagamento = Database['public']['Enums']['forma_pagamento'];
type TipoLancamento = Database['public']['Enums']['tipo_lancamento'];
type StatusLancamento = Database['public']['Enums']['status_lancamento'];

// InsertLancamento e UpdateLancamento não são mais usados diretamente para criação
// type InsertLancamento = Database["public"]["Tables"]["lancamentos"]["Insert"]
type UpdateLancamento = Database["public"]["Tables"]["lancamentos"]["Update"]

// Tipo para os argumentos da nossa função RPC
// Nota: Os nomes dos parâmetros devem corresponder aos da função SQL (prefixo 'p_' removido para convenção JS/TS)
export interface CreateLancamentoArgs {
  descricao: string;
  valor_total: number; // Já em centavos
  data_competencia: string; // Formato ISO string
  forma_pagamento: FormaPagamento;
  tipo_lancamento: TipoLancamento;
  status: StatusLancamento;
  observacoes?: string | null;
  obra_id?: string | null;
  contato_id?: string | null;
  categoria_id?: string | null;
  numero_parcelas: number;
}

export async function createLancamento(args: CreateLancamentoArgs) {
  // Mapeia status conforme o formato esperado pela tabela de lançamentos
  // Sempre use exatamente 'Em aberto', 'Pago' ou 'Cancelado'
  let statusParaLancamento: Database["public"]["Enums"]["status_lancamento"] = 'Em aberto'; // valor padrão

  // Criar cliente de serviço para contornar problemas de CORS e RLS
  const serviceClient = createServiceClient();

  if (!serviceClient) {
    throw new Error("Não foi possível criar o cliente de serviço");
  }

  // Obter o usuário atual
  const { data: { user } } = await supabase.auth.getUser();

  // Se o status for no formato RPC (pendente, pago, cancelado), converte para formato UI
  if (typeof args.status === 'string') {
    const statusValue = args.status as string;

    // Conversão de formato antigo para novo
    if (statusValue === 'pendente') {
      statusParaLancamento = 'Em aberto';
    } else if (statusValue === 'pago') {
      statusParaLancamento = 'Pago';
    } else if (statusValue === 'cancelado') {
      statusParaLancamento = 'Cancelado';
    }
    // Se já estiver no formato correto, usa diretamente
    else if (statusValue === 'Em aberto' || statusValue === 'Pago' || statusValue === 'Cancelado') {
      statusParaLancamento = statusValue as Database["public"]["Enums"]["status_lancamento"];
    }
  }

  // Mapeia os argumentos para o formato esperado pela RPC (com prefixo p_)
  const rpcArgs = {
    p_descricao: args.descricao,
    p_valor_total: args.valor_total,
    p_data_competencia: args.data_competencia,
    p_forma_pagamento: args.forma_pagamento,
    p_tipo_lancamento: args.tipo_lancamento,
    p_status: statusParaLancamento, // Garantido que está no formato 'Em aberto', 'Pago' ou 'Cancelado'
    p_observacoes: args.observacoes || '',
    p_obra_id: args.obra_id || null,
    p_contato_id: args.contato_id || null,
    p_categoria_id: args.categoria_id || null,
    p_numero_parcelas: args.numero_parcelas || 1,
    p_user_id: user?.id || null
  };

  // Chama a função RPC simplificada com tipos corretos usando o cliente de serviço
  const { data: lancamentoId, error } = await serviceClient.rpc(
    "criar_lancamento_com_parcelas_simples",
    rpcArgs
  );

  if (error) {
    throw new Error(error.message || "Erro ao criar lançamento e parcelas via RPC");
  }

  // A RPC retorna o ID do lançamento criado.
  return { id: lancamentoId }; // Retorna um objeto simulando um lançamento parcial
}

export async function updateLancamento(id: string, data: UpdateLancamento) {
  try {
    // Usar a API sem parâmetros dinâmicos na URL
    const response = await fetch('/api/lancamentos-atualizar', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id,
        ...data
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Erro desconhecido' }));
      throw new Error(errorData.error || `Erro ao atualizar lançamento: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    throw error instanceof Error ? error : new Error('Erro desconhecido ao atualizar lançamento');
  }
}

export async function deleteLancamento(id: string) {
  try {
    // Usar a API específica para excluir lançamentos
    try {
      const response = await fetch('/api/lancamentos/excluir', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Erro ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        return true;
      } else {
        throw new Error(result.error || 'Erro desconhecido na API');
      }
    } catch (apiError: any) {
      // Tentar método alternativo com cliente direto
      const serviceClient = createServiceClient();

      if (!serviceClient) {
        throw new Error("Não foi possível criar o cliente de serviço");
      }

      // Verificar se o lançamento existe antes de tentar excluí-lo
      const { data: lancamento, error: checkError } = await serviceClient
        .from("lancamentos")
        .select("id")
        .eq("id", id)
        .single();

      if (checkError) {
        // Se o erro for "not found", significa que o lançamento já não existe
        if (checkError.code === 'PGRST116') {
          return true; // Retorna sucesso, pois o lançamento já não existe
        }
        throw new Error(`Erro ao verificar lançamento: ${checkError.message}`);
      }

      if (!lancamento) {
        return true; // Retorna sucesso, pois o lançamento já não existe
      }

      // Primeiro, atualizar documentos para remover a referência ao lançamento
      await serviceClient
        .from("documentos")
        .update({ lancamento_id: null })
        .eq("lancamento_id", id);

      // Excluir parcelas primeiro
      await serviceClient
        .from("parcelas")
        .delete()
        .eq("lancamento_id", id);

      // Finalmente, excluir o lançamento
      const { error: deleteLancamentoError } = await serviceClient
        .from("lancamentos")
        .delete()
        .eq("id", id);

      if (deleteLancamentoError) {
        throw new Error(`Erro ao excluir lançamento: ${deleteLancamentoError.message}`);
      }

      // Verificar se o lançamento foi realmente excluído
      const { data: checkAfterDelete } = await serviceClient
        .from("lancamentos")
        .select("id")
        .eq("id", id);

      if (checkAfterDelete && checkAfterDelete.length > 0) {
        throw new Error(`Falha ao excluir lançamento: o registro ainda existe após exclusão`);
      }

      return true;
    }
  } catch (error: any) {
    throw error;
  }
}

// getLancamentoById atualizado para usar a função RPC
export async function getLancamentoById(id: string): Promise<LancamentoComParcelas | null> {
  try {
    // Criar cliente de serviço para contornar problemas de CORS e RLS
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      return null;
    }

    // Usar a função RPC para buscar o lançamento com suas parcelas
    const { data, error } = await serviceClient.rpc(
      'buscar_lancamento_por_id',
      { p_id: id }
    );

    if (error) {
      // Fallback para o método antigo se a RPC falhar

      // Buscar o lançamento diretamente da tabela
      const { data: lancamento, error: lancamentoError } = await serviceClient
        .from("lancamentos")
        .select(`
          *,
          contatos:contato_id(*),
          obras:obra_id(*),
          categorias:categoria_id(*),
          documentos(id, nome, arquivo_url, arquivo_path, tipo_arquivo)
        `)
        .eq("id", id)
        .single();

      if (lancamentoError) {
        throw new Error(`Erro ao buscar lançamento: ${lancamentoError.message}`);
      }

      if (!lancamento) {
        return null;
      }

      // Buscar as parcelas do lançamento
      const { data: parcelas, error: parcelasError } = await serviceClient
        .from("parcelas")
        .select("*")
        .eq("lancamento_id", id)
        .order("numero", { ascending: true });

      // Garantir que os campos relacionados existam, mesmo que vazios
      const result: LancamentoComParcelas = {
        ...lancamento,
        parcelas: parcelas || [],
        contatos: lancamento.contatos,
        obras: lancamento.obras,
        categorias: lancamento.categorias,
        documentos: lancamento.documentos || []
      };

      return result;
    }

    // Se a RPC funcionou, verificar se os dados são válidos
    if (!data) {
      return null;
    }

    // Converter os dados da RPC para o formato esperado
    const lancamentoComParcelas: LancamentoComParcelas = {
      ...data,
      parcelas: data.parcelas || [],
      contatos: data.contatos || null,
      obras: data.obras || null,
      categorias: data.categorias || null,
      documentos: data.documentos || []
    };

    return lancamentoComParcelas;
  } catch (error) {
    throw error;
  }
}

// Função principal de busca atualizada
export async function getLancamentos(filters: {
  startDate?: string;
  endDate?: string;
  status?: string;
  tipo?: string;
  contatoId?: string;
  obraId?: string;
  cacheBust?: number; // Adicionar parâmetro para evitar cache
} = {}): Promise<LancamentoComParcelas[]> {
  try {
    // Adicionar timestamp para evitar cache
    const cacheBust = filters.cacheBust || Date.now();

    // Usar a API dedicada para buscar lançamentos
    try {
      const response = await fetch(`/api/lancamentos/buscar?cacheBust=${cacheBust}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Erro ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.data || !Array.isArray(result.data)) {
        throw new Error("API retornou formato inválido");
      }

      // Converter os dados da API para o formato esperado
      const lancamentosComParcelas: LancamentoComParcelas[] = result.data
        .filter(item => item && typeof item === 'object')
        .map(item => {
          return {
            ...item,
            parcelas: Array.isArray(item.parcelas) ? item.parcelas : [],
            contatos: item.contatos || null,
            obras: item.obras || null,
            categorias: item.categorias || null,
            documentos: item.documentos || []
          } as LancamentoComParcelas;
        });

      // Aplicar filtros
      let lancamentosFiltrados = lancamentosComParcelas;

      // Filtrar por data de início
      if (filters.startDate) {
        lancamentosFiltrados = lancamentosFiltrados.filter(lancamento => {
          if (!lancamento.data) return false;
          return new Date(lancamento.data) >= new Date(filters.startDate!);
        });
      }

      // Filtrar por data de fim
      if (filters.endDate) {
        lancamentosFiltrados = lancamentosFiltrados.filter(lancamento => {
          if (!lancamento.data) return false;
          return new Date(lancamento.data) <= new Date(filters.endDate!);
        });
      }

      // Filtrar por status
      if (filters.status) {
        lancamentosFiltrados = lancamentosFiltrados.filter(lancamento =>
          lancamento.status === filters.status
        );
      }

      // Filtrar por tipo
      if (filters.tipo) {
        lancamentosFiltrados = lancamentosFiltrados.filter(lancamento =>
          lancamento.tipo === filters.tipo
        );
      }

      // Filtrar por contato
      if (filters.contatoId) {
        lancamentosFiltrados = lancamentosFiltrados.filter(lancamento =>
          lancamento.contato_id === filters.contatoId
        );
      }

      // Filtrar por obra
      if (filters.obraId) {
        lancamentosFiltrados = lancamentosFiltrados.filter(lancamento =>
          lancamento.obra_id === filters.obraId
        );
      }

      return lancamentosFiltrados;
    } catch (apiError: any) {
      // Fallback: Usar cliente direto como alternativa
      const serviceClient = createServiceClient();

      if (!serviceClient) {
        throw new Error("Não foi possível criar o cliente de serviço");
      }

      // Modificando a consulta para especificar a relação correta para categoria_id
      const { data: lancamentosTabela, error: tabelaError } = await serviceClient
        .from("lancamentos")
        .select(`
          *,
          contatos:contato_id(*),
          obras:obra_id(*),
          categorias:categoria_id(*),
          documentos(id, nome, arquivo_url, arquivo_path, tipo_arquivo)
        `)
        .order('created_at', { ascending: false });

      if (tabelaError) {
        return []; // Retornar array vazio em caso de erro
      }

      if (!lancamentosTabela || !Array.isArray(lancamentosTabela)) {
        return [];
      }

      // Aplicar filtros
      let lancamentosFiltrados = lancamentosTabela;

      // Filtrar por data de início
      if (filters.startDate) {
        lancamentosFiltrados = lancamentosFiltrados.filter(lancamento => {
          if (!lancamento.data) return false;
          return new Date(lancamento.data) >= new Date(filters.startDate!);
        });
      }

      // Filtrar por data de fim
      if (filters.endDate) {
        lancamentosFiltrados = lancamentosFiltrados.filter(lancamento => {
          if (!lancamento.data) return false;
          return new Date(lancamento.data) <= new Date(filters.endDate!);
        });
      }

      // Filtrar por status
      if (filters.status) {
        lancamentosFiltrados = lancamentosFiltrados.filter(lancamento =>
          lancamento.status === filters.status
        );
      }

      // Filtrar por tipo
      if (filters.tipo) {
        lancamentosFiltrados = lancamentosFiltrados.filter(lancamento =>
          lancamento.tipo_lancamento === filters.tipo
        );
      }

      // Filtrar por contato
      if (filters.contatoId) {
        lancamentosFiltrados = lancamentosFiltrados.filter(lancamento =>
          lancamento.contato_id === filters.contatoId
        );
      }

      // Filtrar por obra
      if (filters.obraId) {
        lancamentosFiltrados = lancamentosFiltrados.filter(lancamento =>
          lancamento.obra_id === filters.obraId
        );
      }

      // Preparar array para armazenar lançamentos com suas parcelas
      const lancamentosComParcelas: LancamentoComParcelas[] = [];

      // Para cada lançamento, buscar suas parcelas
      for (const lancamento of lancamentosFiltrados) {
        try {
          // Buscar parcelas para este lançamento usando o cliente de serviço
          const { data: parcelas } = await serviceClient
            .from("parcelas")
            .select("*")
            .eq("lancamento_id", lancamento.id)
            .order("numero", { ascending: true });

          // Adicionar o lançamento com suas parcelas ao array
          lancamentosComParcelas.push({
            ...lancamento,
            tipo: lancamento.tipo_lancamento, // Mapear tipo_lancamento para tipo
            parcelas: parcelas || [],
            contatos: lancamento.contatos,
            obras: lancamento.obras,
            categorias: lancamento.categorias,
            documentos: lancamento.documentos || []
          });
        } catch (parcelaError) {
          // Adicionar o lançamento mesmo sem parcelas para não perder dados
          lancamentosComParcelas.push({
            ...lancamento,
            tipo: lancamento.tipo_lancamento, // Mapear tipo_lancamento para tipo
            parcelas: [],
            contatos: lancamento.contatos,
            obras: lancamento.obras,
            categorias: lancamento.categorias,
            documentos: lancamento.documentos || []
          });
        }
      }

      return lancamentosComParcelas;
    }
  } catch (error: any) {
    return []; // Retornar array vazio em vez de lançar erro para evitar quebrar a UI
  }
}

// Função para excluir múltiplos lançamentos
export async function deleteManyLancamentos(ids: string[]): Promise<{ success: boolean; errors: string[] }> {
  const errors: string[] = [];
  let success = true;
  const deletedIds: string[] = [];

  try {
    // Usar a API específica para excluir vários lançamentos
    try {
      const response = await fetch('/api/lancamentos/excluir-varios', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ids }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Erro ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        return { success: true, errors: [] };
      } else {
        // Processar erros retornados pela API
        return {
          success: result.results.some((r: any) => r.success),
          errors: result.errors
        };
      }
    } catch (apiError: any) {
      // Tentar método alternativo com cliente direto
      const serviceClient = createServiceClient();

      if (!serviceClient) {
        throw new Error("Não foi possível criar o cliente de serviço");
      }

      // Verificar se os lançamentos existem antes de tentar excluir
      const { data: existingLancamentos, error: checkError } = await serviceClient
        .from("lancamentos")
        .select("id")
        .in("id", ids);

      if (checkError) {
        throw new Error(`Erro ao verificar lançamentos: ${checkError.message}`);
      }

      // Se não houver lançamentos para excluir, retornar sucesso imediatamente
      if (!existingLancamentos || existingLancamentos.length === 0) {
        return { success: true, errors: [] };
      }

      // Usar apenas os IDs que realmente existem
      const validIds = existingLancamentos.map(l => l.id);

      // Processar cada lançamento individualmente
      for (const id of validIds) {
        try {
          // Tentar excluir o lançamento usando a função de exclusão individual
          const result = await deleteLancamento(id);

          if (result) {
            deletedIds.push(id);
          } else {
            errors.push(`Erro ao excluir lançamento ${id}: Falha na exclusão`);
          }
        } catch (error: any) {
          errors.push(`Erro ao excluir lançamento ${id}: ${error.message}`);
        }

        // Pequena pausa entre lançamentos
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // Calcular o número de sucessos com base nos IDs excluídos
      const successCount = deletedIds.length;

      // Determinar o status geral da operação
      if (successCount === ids.length) {
        success = true;
      } else if (successCount > 0) {
        success = true; // Considerar parcialmente bem-sucedido
      } else {
        success = false;
      }

      return { success, errors };
    }
  } catch (error: any) {
    return {
      success: false,
      errors: [`Erro ao excluir lançamentos: ${error.message}`]
    };
  }
}

// Função para atualizar o projeto (obra) de múltiplos lançamentos
export async function updateManyLancamentosObra(ids: string[], obra_id: string): Promise<{ success: boolean; errors: string[] }> {
  const errors: string[] = [];
  let success = true;

  try {
    // Criar cliente de serviço para contornar problemas de CORS e RLS
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      throw new Error("Não foi possível criar o cliente de serviço");
    }

    const { error } = await serviceClient
      .from("lancamentos")
      .update({ obra_id, updated_at: new Date().toISOString() })
      .in("id", ids);

    if (error) {
      success = false;
      errors.push(`Erro ao atualizar obra dos lançamentos: ${error.message}`);
    }
  } catch (error: any) {
    success = false;
    errors.push(`Erro ao atualizar obra dos lançamentos: ${error.message}`);
  }

  return { success, errors };
}

// Função para atualizar a categoria de múltiplos lançamentos
export async function updateManyLancamentosCategoria(ids: string[], categoria_id: string): Promise<{ success: boolean; errors: string[] }> {
  const errors: string[] = [];
  let success = true;

  try {
    // Criar cliente de serviço para contornar problemas de CORS e RLS
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      throw new Error("Não foi possível criar o cliente de serviço");
    }

    const { error } = await serviceClient
      .from("lancamentos")
      .update({ categoria_id, updated_at: new Date().toISOString() })
      .in("id", ids);

    if (error) {
      success = false;
      errors.push(`Erro ao atualizar categoria dos lançamentos: ${error.message}`);
    }
  } catch (error: any) {
    success = false;
    errors.push(`Erro ao atualizar categoria dos lançamentos: ${error.message}`);
  }

  return { success, errors };
}

export function useLancamentosService() {
  return {
    createLancamento,
    updateLancamento,
    deleteLancamento,
    getLancamentos, // Atualizado
    getLancamentoById, // Atualizado
    deleteManyLancamentos,
    updateManyLancamentosObra,
    updateManyLancamentosCategoria,
    createServiceClient // Expor a função para criar o cliente de serviço
  }
}