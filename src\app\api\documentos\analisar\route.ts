import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { formatDateToISOString } from '@/lib/date-utils';
import { ParcelaExtraida } from '@/types/documentos';

// POST /api/documentos/analisar - Analisar um documento com Gemini AI
export async function POST(request: NextRequest) {
  try {
    // Importar o cliente de serviço
    const { createServiceClient } = await import('@/lib/supabase/service-client');

    // Usar o cliente de serviço para ignorar as políticas RLS
    const supabase = createServiceClient();

    if (!supabase) {
      return NextResponse.json({ error: 'Erro ao criar cliente de serviço' }, { status: 500 });
    }

    // Obter o body da requisição com a URL do documento
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json(
        { error: 'URL do documento não fornecida' },
        { status: 400 }
      );
    }

    // Verificar se a API key do Gemini está configurada
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key do Gemini não configurada' },
        { status: 500 }
      );
    }

    // Inicializar o cliente do Gemini
    let genAI, model;
    try {
      genAI = new GoogleGenerativeAI(apiKey);
      model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    } catch (geminiError) {
      return NextResponse.json(
        { error: 'Erro ao inicializar serviço de IA: ' + (geminiError as Error).message },
        { status: 500 }
      );
    }

    // Obter o arquivo do Supabase Storage
    // Extrair o caminho do arquivo da URL
    let filePath = '';

    try {
      // Tenta extrair o caminho usando diferentes métodos
      // Método 1: Usando regex para extrair após /anexos/
      const regexMatch = url.match(/\/anexos\/(.+?)(\?|$)/);
      if (regexMatch) {
        filePath = regexMatch[1];
      }
      // Método 2: Se a URL contém o caminho completo
      else if (url.includes('anexos/')) {
        filePath = url.split('anexos/')[1].split('?')[0];
      }
      // Método 3: Se a URL é apenas o nome do arquivo
      else {
        filePath = url;
      }

      // Caminho extraído com sucesso
    } catch (extractError) {
      return NextResponse.json(
        { error: 'Erro ao processar URL do arquivo: ' + (extractError as Error).message },
        { status: 500 }
      );
    }

    // Tentar baixar o arquivo
    const { data: fileData, error: fileError } = await supabase.storage
      .from('anexos')
      .download(filePath);

    if (fileError) {
      return NextResponse.json(
        { error: 'Erro ao obter arquivo: ' + fileError.message },
        { status: 500 }
      );
    }

    // Converter o arquivo para base64
    const fileBase64 = await fileData.arrayBuffer().then(buffer => {
      const bytes = new Uint8Array(buffer);
      let binary = '';
      for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
      }
      return btoa(binary);
    });

    // Determinar o tipo MIME
    let mimeType = 'application/pdf';
    if (url.endsWith('.jpg') || url.endsWith('.jpeg')) {
      mimeType = 'image/jpeg';
    } else if (url.endsWith('.png')) {
      mimeType = 'image/png';
    }

    // Criar o prompt para o Gemini
    const prompt = `
      Analise este documento financeiro e extraia as seguintes informações:

      1. Nome do fornecedor
      2. Valor total
      3. Data de vencimento
      4. Tipo de documento (boleto, comprovante, nota_fiscal, outros)
      5. Status do pagamento (se for comprovante: pago, se for boleto: pendente)
      6. Data do pagamento (APENAS para comprovantes - quando o pagamento foi realizado)
      7. Forma de pagamento realizada (APENAS para comprovantes - PIX, TED, DOC, Cartão, etc.)
      8. Se houver parcelas, liste cada uma com número, valor e data de vencimento
      9. DADOS DETALHADOS DO FORNECEDOR/CONTATO (máximo de informações possíveis)

      IDENTIFICAÇÃO DO TIPO DE DOCUMENTO:
      - "comprovante": Se contém palavras como "comprovante", "recibo", "pago", "quitado", "transferência realizada", "pagamento efetuado", "débito", "crédito", "transação", "operação realizada", "valor debitado", "valor creditado"
      - "boleto": Se contém código de barras, "vencimento", "valor a pagar", "banco", "agência", "nosso número", "linha digitável"
      - "nota_fiscal": Se contém "nota fiscal", "NF", "CNPJ", "imposto"
      - "outros": Para outros tipos de documento

      IDENTIFICAÇÃO DO STATUS DE PAGAMENTO:
      - "pago": SEMPRE para comprovantes de pagamento, transferências, débitos, créditos
      - "pendente": Para boletos não pagos, faturas em aberto
      - "vencido": Para boletos com data de vencimento já passada

      EXTRAÇÃO DE DADOS DE PAGAMENTO (APENAS PARA COMPROVANTES):
      - Data do pagamento: Procure por "data da operação", "data do débito", "data da transação", "realizado em"
      - Forma de pagamento: Identifique ESPECIFICAMENTE:
        * "PIX" se mencionar PIX, Pix, pix
        * "TED" se mencionar TED, transferência eletrônica disponível
        * "DOC" se mencionar DOC, documento de ordem de crédito
        * "Transferência" para transferências bancárias genéricas
        * "Cartão" para pagamentos com cartão de débito/crédito
        * "Dinheiro" para pagamentos em espécie
        * Use "PIX" como padrão se não conseguir identificar claramente

      EXTRAÇÃO DETALHADA DO FORNECEDOR/CONTATO:
      Procure extrair o MÁXIMO de informações sobre o fornecedor:
      - Nome da empresa (razão social)
      - Nome fantasia (se diferente da razão social)
      - Nome do contato/pessoa responsável
      - CNPJ ou CPF (sempre incluir se encontrado)
      - Email (qualquer email encontrado)
      - Telefone (qualquer telefone encontrado)
      - Chave PIX (se mencionada)
      - Endereço completo (rua, número, bairro)
      - Cidade
      - Estado/UF
      - CEP
      - Tipo de pessoa (juridica se tiver CNPJ, fisica se tiver CPF)
      
      IMPORTANTE: Se for boleto bancário, procure especialmente no "beneficiário" ou "cedente".
      Se for nota fiscal, procure nos dados do emitente.
      Se for comprovante, procure nos dados do favorecido/destinatário.

      IMPORTANTE SOBRE VALORES MONETÁRIOS:
      - Os valores devem ser extraídos como números decimais (ex: 39839.00 para R$ 39.839,00)
      - NÃO use separadores de milhar (pontos) nos valores
      - Use ponto como separador decimal, não vírgula
      - Certifique-se de que os valores das parcelas somam exatamente o valor total
      - NÃO multiplique por 100 para representar centavos

      IMPORTANTE SOBRE DATAS:
      - Todas as datas DEVEM estar no formato ISO "YYYY-MM-DD" (ex: 2024-05-15)
      - Não use formatos como DD/MM/YYYY ou MM/DD/YYYY
      - Certifique-se de que o ano tem 4 dígitos (2024, não apenas 24)
      - Certifique-se de que o mês e o dia têm 2 dígitos (05, não apenas 5)
      - Preste muita atenção para não inverter dia e mês

      Retorne apenas um objeto JSON com os seguintes campos:
      {
        "fornecedor": "Nome do fornecedor",
        "valor_total": número (apenas o valor numérico, sem formatação, ex: 39839.00),
        "data_vencimento": "YYYY-MM-DD",
        "tipo_documento": "boleto|comprovante|nota_fiscal|outros",
        "status_pagamento": "pago|pendente|vencido",
        "data_pagamento": "YYYY-MM-DD" (APENAS para comprovantes),
        "forma_pagamento_realizada": "PIX|TED|DOC|Transferência|Cartão|Dinheiro" (APENAS para comprovantes),
        "parcelas": [
          {
            "numero": número da parcela,
            "valor": valor da parcela (número, ex: 13279.67),
            "vencimento": "YYYY-MM-DD"
          }
        ],
        "confianca": número de 0 a 1 indicando sua confiança na extração,
        "contato_detectado": {
          "nome_empresa": "Nome da empresa/razão social",
          "nome_razao": "Razão social (se diferente do nome fantasia)",
          "nome_contato": "Nome da pessoa de contato",
          "email": "<EMAIL>",
          "telefone": "(11) 99999-9999",
          "cpf_cnpj": "00.000.000/0000-00 ou 000.000.000-00",
          "tipo_pessoa": "juridica|fisica",
          "chave_pix": "chave PIX se encontrada",
          "endereco": "Rua, número, bairro",
          "cidade": "Cidade",
          "uf": "SP",
          "cep": "00000-000",
          "confianca": número de 0 a 1 indicando confiança nos dados do contato
        }
      }

      Se alguma informação não estiver disponível, omita o campo correspondente.
      Para dados do contato, inclua apenas os campos que conseguir identificar com certeza.
    `;

    // Fazer a chamada para o Gemini
    let result, response, text;
    try {
      result = await model.generateContent([
        prompt,
        {
          inlineData: {
            mimeType,
            data: fileBase64
          }
        }
      ]);

      response = result.response;
      text = response.text();

    } catch (geminiApiError) {
      return NextResponse.json(
        { error: 'Erro na análise com IA: ' + (geminiApiError as Error).message },
        { status: 500 }
      );
    }

    // Extrair o JSON da resposta
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      return NextResponse.json(
        { error: 'Não foi possível extrair dados do documento' },
        { status: 500 }
      );
    }

    // Armazenar o texto do JSON para evitar tentar ler o stream novamente
    const jsonText = jsonMatch[0];

    // Parsear o JSON e garantir que os valores estejam no formato correto
    let jsonData;
    try {
      jsonData = JSON.parse(jsonText);
      

      

      
          } catch (parseError) {
      return NextResponse.json(
        { error: 'Erro ao processar dados extraídos do documento' },
        { status: 500 }
      );
    }

    // Função para extrair valor numérico de uma string
    const extrairValorNumerico = (valor: string | number): number => {
      if (typeof valor === 'number') return valor;

      // Remover todos os caracteres não numéricos, exceto vírgula e ponto
      const valorLimpo = valor.replace(/[^\d,.-]/g, '');

      // Substituir vírgula por ponto para conversão correta
      const valorComPonto = valorLimpo.replace(',', '.');

      // Converter para número
      const valorNumerico = parseFloat(valorComPonto);

      // Retornar 0 se não for um número válido
      return isNaN(valorNumerico) ? 0 : valorNumerico;
    };

    // Garantir que o valor total seja um número
    if (jsonData.valor_total) {
      // Converter para número
      jsonData.valor_total = extrairValorNumerico(jsonData.valor_total);
    }

    // Garantir que a data de vencimento esteja no formato correto
    if (jsonData.data_vencimento) {
      try {
        // Verificar se a data está no formato YYYY-MM-DD
        if (!/^\d{4}-\d{2}-\d{2}$/.test(jsonData.data_vencimento)) {
          // Tentar converter de outros formatos comuns (DD/MM/YYYY)
          if (/^\d{2}\/\d{2}\/\d{4}$/.test(jsonData.data_vencimento)) {
            const [day, month, year] = jsonData.data_vencimento.split('/');
            jsonData.data_vencimento = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
          }
          // Formato DD/MM/YY
          else if (/^\d{2}\/\d{2}\/\d{2}$/.test(jsonData.data_vencimento)) {
            const [day, month, shortYear] = jsonData.data_vencimento.split('/');
            const year = `20${shortYear}`; // Assumir anos 2000+
            jsonData.data_vencimento = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
          }
        }

        // Validar a data resultante
        const date = new Date(jsonData.data_vencimento);
        if (isNaN(date.getTime())) {
          // Se a data for inválida, usar a data atual
          const today = new Date();
          jsonData.data_vencimento = formatDateToISOString(today) || today.toISOString().split('T')[0];
        }
      } catch (error) {
        
        // Em caso de erro, usar a data atual
        const today = new Date();
        jsonData.data_vencimento = formatDateToISOString(today) || today.toISOString().split('T')[0];
      }
    }

    // Garantir que os valores das parcelas sejam números e datas estejam corretas
    if (jsonData.parcelas && Array.isArray(jsonData.parcelas)) {
      jsonData.parcelas = jsonData.parcelas.map((parcela: ParcelaExtraida) => {
        const valorParcela = extrairValorNumerico(parcela.valor);

        // Processar data de vencimento da parcela
        let vencimento = parcela.vencimento;
        try {
          // Verificar se a data está no formato YYYY-MM-DD
          if (vencimento && !/^\d{4}-\d{2}-\d{2}$/.test(vencimento)) {
            // Tentar converter de outros formatos comuns (DD/MM/YYYY)
            if (/^\d{2}\/\d{2}\/\d{4}$/.test(vencimento)) {
              const [day, month, year] = vencimento.split('/');
              vencimento = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
            }
            // Formato DD/MM/YY
            else if (/^\d{2}\/\d{2}\/\d{2}$/.test(vencimento)) {
              const [day, month, shortYear] = vencimento.split('/');
              const year = `20${shortYear}`; // Assumir anos 2000+
              vencimento = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
            }
          }

          // Validar a data resultante
          const date = new Date(vencimento);
          if (isNaN(date.getTime())) {
            // Se a data for inválida, usar a data de vencimento do documento ou a data atual
            vencimento = jsonData.data_vencimento || formatDateToISOString(new Date()) || new Date().toISOString().split('T')[0];
          }
        } catch (error) {
          
          // Em caso de erro, usar a data de vencimento do documento ou a data atual
          vencimento = jsonData.data_vencimento || formatDateToISOString(new Date()) || new Date().toISOString().split('T')[0];
        }

        return {
          ...parcela,
          valor: valorParcela,
          vencimento: vencimento
        };
      });
    }

    // Encontrar o documento pela URL
    const { data: documentos, error: findError } = await supabase
      .from('documentos')
      .select('id')
      .eq('arquivo_url', url)
      .limit(1);

    if (findError || !documentos || documentos.length === 0) {
      return NextResponse.json(
        { error: 'Documento não encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json(jsonData);
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Erro interno do servidor: ' + error.message },
      { status: 500 }
    );
  }
}