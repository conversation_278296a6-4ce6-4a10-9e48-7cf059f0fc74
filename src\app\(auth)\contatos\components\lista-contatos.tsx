'use client';

import { useEffect, useState, useCallback } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Edit2, Trash2, Eye } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { supabase } from '@/lib/supabase';
import { Spinner } from '@/components/ui/spinner';
import { toast } from 'sonner';
import { Contato } from '@/types/contatos';

interface ListaContatosProps {
  onEdit: (id: string) => void;
}

export function ListaContatos({ onEdit }: ListaContatosProps) {
  const [contatos, setContatos] = useState<Contato[]>([]);
  const [loading, setLoading] = useState(true);
  const [contatoParaExcluir, setContatoParaExcluir] = useState<string | null>(null);
  const [excluindo, setExcluindo] = useState(false);

  const loadContatos = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('contatos')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setContatos(data || []);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      toast.error(`Erro ao carregar contatos: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadContatos();
  }, [loadContatos]);

  async function handleExcluir() {
    if (!contatoParaExcluir) return;

    try {
      setExcluindo(true);
      const { error } = await supabase
        .from('contatos')
        .delete()
        .eq('id', contatoParaExcluir);

      if (error) throw error;

      toast.success('Contato excluído com sucesso');
      loadContatos();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      toast.error(`Erro ao excluir contato: ${errorMessage}`);
    } finally {
      setExcluindo(false);
      setContatoParaExcluir(null);
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <Spinner className="w-6 h-6" />
      </div>
    );
  }

  return (
    <>
      <div className="border rounded-md">
        <Table>
          <TableHeader className="bg-muted">
            <TableRow>
              <TableHead>Nome/Empresa</TableHead>
              <TableHead>Tipo</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Telefone</TableHead>
              <TableHead className="w-[100px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {contatos.map((contato) => (
              <TableRow key={contato.id}>
                <TableCell className="font-medium">{contato.nome_empresa}</TableCell>
                <TableCell>{contato.tipo}</TableCell>
                <TableCell>{contato.email}</TableCell>
                <TableCell>{contato.telefone}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onEdit(contato.id)}
                      className="h-7 w-7"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onEdit(contato.id)}
                      className="h-7 w-7"
                    >
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setContatoParaExcluir(contato.id)}
                      className="h-7 w-7"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <Dialog open={!!contatoParaExcluir} onOpenChange={() => setContatoParaExcluir(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Excluir contato</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir este contato? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setContatoParaExcluir(null)}
              disabled={excluindo}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleExcluir}
              disabled={excluindo}
            >
              {excluindo ? (
                <Spinner className="mr-2 h-4 w-4" />
              ) : (
                <Trash2 className="mr-2 h-4 w-4" />
              )}
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}