'use client';

import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTitle,
  SheetDescription,
} from '@/components/ui/sheet';
import { FormContrato } from './form-contrato';
import { Contrato } from '@/types/contratos';

interface DrawerContratoProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contrato?: Contrato;
  onSubmit: () => Promise<void>;
}

export function DrawerContrato({ open, onOpenChange, contrato, onSubmit }: DrawerContratoProps) {
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-[600px]">
        <SheetHeader>
          <SheetTitle>{contrato ? 'Editar Contrato' : 'Novo Contrato'}</SheetTitle>
          <SheetDescription>
            {contrato 
              ? 'Faça as alterações necessárias nos dados do contrato.' 
              : 'Preencha os dados para criar um novo contrato.'}
          </SheetDescription>
        </SheetHeader>
        <div className="py-6">
          <FormContrato 
            contrato={contrato} 
            onSuccess={onSubmit} 
            onCancel={() => onOpenChange(false)} 
          />
        </div>
      </SheetContent>
    </Sheet>
  );
}