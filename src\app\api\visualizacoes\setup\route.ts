import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Criar cliente Supabase com service_role para acesso direto ao banco
const createServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    
    return null;
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

// Função SQL para inserir visualizações
const serviceInsertVisualizacaoFunction = `
CREATE OR REPLACE FUNCTION public.service_insert_visualizacao(
  p_user_id UUID,
  p_contexto TEXT,
  p_nome TEXT,
  p_descricao TEXT,
  p_filtros_json JSONB
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_id UUID;
BEGIN
  -- Gerar um novo UUID para a visualização
  v_id := gen_random_uuid();

  -- Inserir a visualização com o UUID gerado
  INSERT INTO public.visualizacoes (
    id,
    user_id,
    contexto,
    nome,
    descricao,
    filtros_json,
    created_at,
    updated_at
  ) VALUES (
    v_id,
    p_user_id,
    p_contexto,
    p_nome,
    p_descricao,
    p_filtros_json,
    NOW(),
    NOW()
  );

  -- Retornar o ID da visualização criada
  RETURN v_id;
END;
$$;
`;

// GET /api/visualizacoes/setup - Configura as funções SQL necessárias
export async function GET(request: NextRequest) {
  try {
    // Criar cliente Supabase com service role
    const supabase = createServiceClient();
    if (!supabase) {
      return NextResponse.json({ error: 'Erro ao criar cliente Supabase' }, { status: 500 });
    }

    // Verificar se a função já existe
    let checkData = null;
    let checkError = null;
    try {
      const result = await supabase.rpc(
        'check_function_exists',
        { function_name: 'service_insert_visualizacao' }
      );
      checkData = result.data;
      checkError = result.error;
    } catch (e) {
      checkError = { message: 'Função de verificação não existe' };
    }

    if (checkError) {
    } else if (checkData && checkData.exists) {
      return NextResponse.json({ success: true, message: 'Função já existe' });
    }

    // Criar a função SQL

    // Executar SQL diretamente
    let sqlError = null;
    try {
      const result = await supabase.rpc(
        'execute_sql',
        { sql_query: serviceInsertVisualizacaoFunction }
      );
      sqlError = result.error;
    } catch (e) {
      // Se a função execute_sql não existir, tentar executar diretamente
      try {
        await supabase.from('_setup_temp')
          .insert({ sql: serviceInsertVisualizacaoFunction })
          .select();
      } catch (insertError) {
        sqlError = insertError;
      }
    }

    if (sqlError) {

      // Tentar criar a tabela temporária e inserir o SQL
      try {
        // Criar tabela temporária se não existir
        try {
          await supabase.rpc('execute_sql', {
            sql_query: `
              CREATE TABLE IF NOT EXISTS _setup_temp (
                id SERIAL PRIMARY KEY,
                sql TEXT,
                created_at TIMESTAMPTZ DEFAULT NOW()
              );
            `
          });
        } catch (e) {
          // Ignorar erro se a função não existir
        }

        // Inserir o SQL na tabela temporária
        await supabase.from('_setup_temp')
          .insert({ sql: serviceInsertVisualizacaoFunction });
      } catch (tempError) {
        return NextResponse.json({
          error: 'Erro ao criar função SQL',
          details: (sqlError as any)?.message || 'Erro desconhecido',
          sql: serviceInsertVisualizacaoFunction
        }, { status: 500 });
      }
    }

    return NextResponse.json({ success: true, message: 'Função SQL criada com sucesso' });
  } catch (error: any) {
    return NextResponse.json({ error: 'Erro interno do servidor: ' + (error?.message || 'desconhecido') }, { status: 500 });
  }
}