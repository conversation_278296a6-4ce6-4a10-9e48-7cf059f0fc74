'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Save } from 'lucide-react'
import { toast } from 'sonner'
import { useVisualizacoesService, criarFuncoesSql } from './visualizacoes-service'
import { supabase } from '@/lib/supabase/client'

interface SalvarVisualizacaoButtonProps {
  filtrosAtuais: any
  onVisualizacaoCriada: (visualizacao: any) => void
  disabled?: boolean
  renderAsForm?: boolean
}

export default function SalvarVisualizacaoButton({
  filtrosAtuais,
  onVisualizacaoCriada,
  disabled = false,
  renderAsForm = false
}: SalvarVisualizacaoButtonProps) {
  const [open, setOpen] = useState(false)
  const [nome, setNome] = useState('')
  const [descricao, setDescricao] = useState('')
  const [loading, setLoading] = useState(false)
  const [erro, setErro] = useState<string | null>(null)
  const [funcoesCriadas, setFuncoesCriadas] = useState(false)
  const { criarVisualizacao } = useVisualizacoesService()

  // Não precisamos mais verificar se há filtros ativos
  // Agora permitimos salvar visualizações sem filtros
  const temFiltrosAtivos = true

  // Inicializar funções SQL quando o componente for montado
  useEffect(() => {
    async function inicializarFuncoes() {
      if (!funcoesCriadas) {
        try {
          // Verificar se a função criarFuncoesSql existe
          if (typeof criarFuncoesSql === 'function') {
            const resultado = await criarFuncoesSql()
            setFuncoesCriadas(resultado)
          } else {
            setFuncoesCriadas(true)
          }
        } catch (error) {
          // Mesmo com erro, vamos tentar prosseguir
          setFuncoesCriadas(true)
        }
      }
    }

    inicializarFuncoes()
  }, [])

  async function handleSalvar() {
    setErro(null)

    // Validar nome
    if (!nome.trim()) {
      setErro('O nome é obrigatório')
      return
    }

    // Garantir que filtrosAtuais seja um objeto válido, mesmo que vazio
    if (!filtrosAtuais || typeof filtrosAtuais !== 'object') {
      filtrosAtuais = {}
    }

    setLoading(true)

    try {
      // Variável para armazenar o ID do usuário
      let userId;

      try {
        // Tentar obter o usuário do cliente Supabase
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          throw sessionError;
        }

        if (sessionData?.session?.user) {
          userId = sessionData.session.user.id;
        } else {
          // Tentar obter via getUser como fallback
          const { data: userData, error: userError } = await supabase.auth.getUser();

          if (userError) {
            throw userError;
          }

          if (userData?.user) {
            userId = userData.user.id;
          } else {
            setErro('Você precisa estar autenticado para salvar uma visualização. Por favor, faça login novamente.');
            setLoading(false);
            return;
          }
        }
      } catch (authError) {
        setErro('Erro ao verificar sua autenticação. Por favor, faça login novamente.');
        setLoading(false);
        return;
      }

      // Garantir que temos um ID de usuário válido
      if (!userId) {
        setErro('Não foi possível identificar o usuário. Por favor, faça login novamente.');
        setLoading(false);
        return;
      }

      // Usar o serviço para criar a visualização
      const novaVisualizacao = await criarVisualizacao(
        nome,
        filtrosAtuais,
        userId,
        descricao,
        'lancamentos'
      );

      if (!novaVisualizacao) {
        setErro('Erro ao criar visualização. Tente novamente.')
        setLoading(false)
        return;
      }

      // Save the visualization ID in localStorage
      if (typeof window !== 'undefined') {
        window.localStorage.setItem('visualizacao_lancamentos_ativa', novaVisualizacao.id);

        // Check if sidebar is collapsed and save the visualization for that state too
        const isSidebarCollapsed = window.localStorage.getItem('sidebar_collapsed') === 'true';
        if (isSidebarCollapsed) {
          window.localStorage.setItem('visualizacao_lancamentos_ativa_when_collapsed', novaVisualizacao.id);
        }
      }

      // Verificar se a função onVisualizacaoCriada existe antes de chamá-la
      if (typeof onVisualizacaoCriada === 'function') {
        onVisualizacaoCriada(novaVisualizacao);
      }

      setNome('')
      setDescricao('')
      setOpen(false)
      toast.success('Visualização salva com sucesso')
    } catch (e: any) {
      setErro('Erro ao salvar: ' + (e?.message || 'Falha desconhecida'))
    } finally {
      setLoading(false)
    }
  }

  // Renderizar apenas o formulário (sem botão e sem modal)
  if (renderAsForm) {
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Nome <span className="text-destructive">*</span></label>
          <Input
            value={nome}
            onChange={e => setNome(e.target.value)}
            placeholder="Ex: Pendentes do Sete Lagos"
            maxLength={60}
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Descrição <span className="text-muted-foreground">(opcional)</span></label>
          <Textarea
            value={descricao}
            onChange={e => setDescricao(e.target.value)}
            placeholder="Descrição opcional para esta visualização"
            className="resize-none h-[80px]"
            maxLength={200}
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Filtros aplicados</label>
          <div className="bg-muted p-3 rounded-md text-sm">
            {Object.keys(filtrosAtuais || {}).length > 0 ? (
              <pre className="whitespace-pre-wrap break-all">
                {JSON.stringify(filtrosAtuais, null, 2)}
              </pre>
            ) : (
              <div className="text-muted-foreground italic">
                Nenhum filtro selecionado. Esta visualização mostrará todos os lançamentos.
              </div>
            )}
          </div>
        </div>
        {erro && <div className="text-destructive text-sm">{erro}</div>}
        <div className="flex justify-end gap-2 pt-2">
          <Button
            onClick={handleSalvar}
            disabled={loading || !nome.trim()}
            className="w-full"
          >
            {loading ? 'Salvando...' : 'Salvar visualização'}
          </Button>
        </div>
      </div>
    );
  }

  // Renderização padrão com botão e modal
  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className="h-9 gap-1"
        onClick={() => setOpen(true)}
        disabled={disabled}
        title="Salvar filtros como visualização"
      >
        <Save className="h-4 w-4" />
        <span>Salvar visualização</span>
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[480px]">
          <DialogHeader>
            <DialogTitle>Salvar visualização</DialogTitle>
            <DialogDescription>Salve os filtros atuais para reutilizar depois.</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Nome <span className="text-destructive">*</span></label>
              <Input
                value={nome}
                onChange={e => setNome(e.target.value)}
                placeholder="Ex: Pendentes do Sete Lagos"
                maxLength={60}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Descrição <span className="text-muted-foreground">(opcional)</span></label>
              <Textarea
                value={descricao}
                onChange={e => setDescricao(e.target.value)}
                placeholder="Descrição opcional para esta visualização"
                className="resize-none h-[80px]"
                maxLength={200}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Filtros aplicados</label>
              <div className="bg-muted p-3 rounded-md text-sm">
                {Object.keys(filtrosAtuais || {}).length > 0 ? (
                  <pre className="whitespace-pre-wrap break-all">
                    {JSON.stringify(filtrosAtuais, null, 2)}
                  </pre>
                ) : (
                  <div className="text-muted-foreground italic">
                    Nenhum filtro selecionado. Esta visualização mostrará todos os lançamentos.
                  </div>
                )}
              </div>
            </div>
            {erro && <div className="text-destructive text-sm">{erro}</div>}
            <div className="flex justify-end gap-2 pt-2">
              <Button
                variant="ghost"
                onClick={() => setOpen(false)}
                disabled={loading}
              >
                Cancelar
              </Button>
              <Button
                onClick={handleSalvar}
                disabled={loading || !nome.trim()}
              >
                {loading ? 'Salvando...' : 'Salvar visualização'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}