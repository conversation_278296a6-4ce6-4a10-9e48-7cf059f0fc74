'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { PageHeader } from '@/components/Layout/PageHeader';
import { ListaParcelas } from './components';
import { DrawerLancamento } from '../lancamentos/components/drawer-lancamento';
import { LancamentoComParcelas } from '@/types/lancamentos';
import { toast } from 'sonner';
import { DrawerPagamentosPendentes } from './components/drawer-pagamentos-pendentes';
import { CreditCard } from 'lucide-react';
import { DrawerParcela } from './components/drawer-parcela';

export default function ParcelasPage() {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedParcela, setSelectedParcela] = useState<any | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const [totalParcelas, setTotalParcelas] = useState<number>(0);
  const [version, setVersion] = useState(0);
  const [isInitialized, setIsInitialized] = useState(false);
  const [drawerPagamentosOpen, setDrawerPagamentosOpen] = useState(false);

  // Inicializar a página de forma controlada
  useEffect(() => {
    let mounted = true;
    
    // Pequeno atraso para garantir que a montagem não cause problemas de renderização
    const timer = setTimeout(() => {
      if (mounted) {
        setIsInitialized(true);

      }
    }, 100);
    
    // Limpar ao desmontar
    return () => {
      mounted = false;
      clearTimeout(timer);

    };
  }, []);

  // Função para abrir o DrawerParcela ao clicar em uma parcela
  const handleEditarParcela = useCallback((parcela: any, index: number, total: number) => {
    setSelectedParcela(parcela);
    setSelectedIndex(index);
    setTotalParcelas(total);
    setDrawerOpen(true);
  }, []);

  // Função para atualizar a lista quando houver alterações
  const handleSubmitSuccess = useCallback(() => {
    setDrawerOpen(false);
    setSelectedParcela(null);
    setSelectedIndex(0);
    setTotalParcelas(0);
    setVersion(prev => prev + 1);
  }, []);

  return (
    <div>
      <PageHeader title="Parcelas" />

      <div className="page-container">
        {isInitialized && (
          <ListaParcelas 
            onEdit={(parcela, index, total) => handleEditarParcela(parcela, index, total)}
            version={version} 
            onAbrirPagamentosPendentes={() => setDrawerPagamentosOpen(true)}
          />
        )}

        <DrawerParcela
          open={drawerOpen}
          onOpenChange={setDrawerOpen}
          parcela={selectedParcela}
          index={selectedIndex}
          total={totalParcelas}
          onSubmit={handleSubmitSuccess}
        />
        <DrawerPagamentosPendentes
          open={drawerPagamentosOpen}
          onOpenChange={setDrawerPagamentosOpen}
        />
      </div>
    </div>
  );
}