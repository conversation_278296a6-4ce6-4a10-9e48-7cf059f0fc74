-- Enable RLS
ALTER TABLE lancamentos ENABLE ROW LEVEL SECURITY;
ALTER TABLE parcelas ENABLE ROW LEVEL SECURITY;

-- Create policies for lancamentos
CREATE POLICY "Enable read access for authenticated users" ON lancamentos
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Enable insert access for authenticated users" ON lancamentos
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Enable update access for authenticated users" ON lancamentos
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Enable delete access for authenticated users" ON lancamentos
  FOR DELETE
  TO authenticated
  USING (true);

-- Create policies for parcelas
CREATE POLICY "Enable read access for authenticated users" ON parcelas
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Enable insert access for authenticated users" ON parcelas
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Enable update access for authenticated users" ON parcelas
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Enable delete access for authenticated users" ON parcelas
  FOR DELETE
  TO authenticated
  USING (true); 