'use client'

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  Building2,
  Receipt,
  FileText,
  Users,
  Settings,
  DollarSign,
  ChevronDown,
  ChevronRight,
  FileImage
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MenuItem {
  title: string;
  href: string;
  icon: any;
  submenu?: MenuItem[];
  badge?: string | number;
}

const menuItems: MenuItem[] = [
  {
    title: 'Dashboard',
    href: '/',
    icon: LayoutDashboard
  },
  {
    title: 'Obras',
    href: '/obras',
    icon: Building2
  },
  {
    title: 'Lançamentos',
    href: '#lancamentos',
    icon: Receipt,
    submenu: [
      {
        title: 'Geral',
        href: '/lancamentos',
        icon: Receipt
      },
      {
        title: 'Parcelas',
        href: '/parcelas',
        icon: DollarSign
      },

    ]
  },
  {
    title: 'Documentos',
    href: '/documentos',
    icon: FileImage
  },
  {
    title: 'Contratos',
    href: '/contratos',
    icon: FileText
  },
  {
    title: 'Contatos',
    href: '/contatos',
    icon: Users
  },

  {
    title: 'Configurações',
    href: '/configuracoes',
    icon: Settings
  }
];

interface SidebarProps {
  collapsed?: boolean;
}

function SidebarComponent({ collapsed = false }: SidebarProps) {
  const pathname = usePathname();
  // Inicializa todos os menus como fechados por padrão
  const [openMenus, setOpenMenus] = useState<{[key: string]: boolean}>({});
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [hoverPosition, setHoverPosition] = useState<{top: number, left: number}>({top: 0, left: 0});

  // Efeito para abrir automaticamente o menu que contém o item ativo atual
  useEffect(() => {
    // Encontra o menu pai que contém o item ativo
    const findActiveParentMenu = () => {
      for (const item of menuItems) {
        if (item.submenu && item.submenu.some(subItem => subItem.href === pathname)) {
          return item.href;
        }
      }
      return null;
    };

    const activeParentMenu = findActiveParentMenu();
    if (activeParentMenu) {
      setOpenMenus(prev => ({
        ...prev,
        [activeParentMenu]: true
      }));
    }
  }, [pathname]);

  const toggleMenu = (href: string) => {
    setOpenMenus(prev => ({
      ...prev,
      [href]: !prev[href]
    }));
  };

  const handleMouseEnter = (href: string, e: React.MouseEvent) => {
    setHoveredItem(href);
    // Get the position of the menu item
    const rect = e.currentTarget.getBoundingClientRect();
    setHoverPosition({
      top: rect.top,
      left: rect.right + 5 // 5px offset from the menu item
    });
  };

  const renderMenuItems = (items: MenuItem[]) => {
    return items.map((item) => {
      const isActive = pathname === item.href;
      const hasSubmenu = item.submenu && item.submenu.length > 0;
      const isOpen = openMenus[item.href];
      const isHovered = hoveredItem === item.href;
      const isSubmenuActive = hasSubmenu && item.submenu?.some(subItem => pathname === subItem.href);

      return (
        <div
          key={item.href}
          className={cn(
            "relative group",
            collapsed && hasSubmenu && "sidebar-menu-item"
          )}
          onMouseEnter={(e) => collapsed ? handleMouseEnter(item.href, e) : null}
          onMouseLeave={() => collapsed ? setHoveredItem(null) : null}
        >
          {/* Item principal */}
          <Link
            id={item.href.replace('#', 'menu-')}
            href={hasSubmenu ? '#' : item.href}
            className={cn(
              "flex items-center w-full py-2.5 px-4 rounded-md my-0.5",
              isActive
                ? "bg-gray-200/90 text-primary font-medium"
                : isSubmenuActive
                  ? "text-muted-foreground"
                  : "hover:bg-gray-200/60 text-muted-foreground",
              collapsed && "justify-center"
            )}
            onClick={(e) => {
              if (hasSubmenu) {
                e.preventDefault();
                toggleMenu(item.href);
              }
            }}
          >
            <item.icon className={cn(
              "h-4 w-4 shrink-0",
              isActive ? "text-primary" : "text-muted-foreground"
            )} />

            {!collapsed && (
              <>
                <span className={cn(
                  "ml-3 flex-1 text-sm",
                  isActive ? "font-medium" : ""
                )}>
                  {item.title}
                </span>

                {item.badge && (
                  <span className="ml-auto bg-gray-200 text-xs rounded-full py-0.5 px-2 text-muted-foreground">
                    {item.badge}
                  </span>
                )}

                {hasSubmenu && (
                  <ChevronDown
                    className={cn(
                      "h-4 w-4 ml-1 text-muted-foreground transition-transform duration-200",
                      isOpen && "transform rotate-180"
                    )}
                  />
                )}
              </>
            )}
          </Link>

          {/* Submenu - shown inline when not collapsed or as floating when collapsed and hovered */}
          {hasSubmenu && (
            <>
              {/* Inline submenu when sidebar is open and menu is expanded */}
              {!collapsed && isOpen && (
                <div className="sidebar-submenu-inline pl-6">
                  {item.submenu?.map(subItem => {
                    const isSubActive = pathname === subItem.href;
                    return (
                      <Link
                        key={subItem.href}
                        href={subItem.href}
                        className={cn(
                          "flex items-center w-full py-2.5 px-4 rounded-md my-0.5 pl-10",
                          isSubActive
                            ? "bg-gray-200/90 text-primary font-medium"
                            : "hover:bg-gray-200/60 text-muted-foreground"
                        )}
                      >
                        <span className="text-sm">
                          {subItem.title}
                        </span>
                      </Link>
                    );
                  })}
                </div>
              )}

              {/* Floating submenu when sidebar is collapsed and item is hovered */}
              {collapsed && isHovered && (
                <div
                  className="sidebar-submenu-floating"
                  style={{
                    position: 'fixed',
                    top: `${hoverPosition.top}px`,
                    left: `${hoverPosition.left}px`,
                    zIndex: 9999,
                    backgroundColor: 'white',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
                    borderRadius: '8px',
                    padding: '8px',
                    minWidth: '200px',
                    border: '1px solid rgba(0, 0, 0, 0.1)',
                    animation: 'fadeIn 0.2s ease-in-out'
                  }}
                >
                  {item.submenu?.map(subItem => {
                    const isSubActive = pathname === subItem.href;
                    return (
                      <Link
                        key={subItem.href}
                        href={subItem.href}
                        className={cn(
                          "flex items-center w-full py-2.5 px-4 rounded-md my-0.5 pl-4",
                          isSubActive
                            ? "bg-gray-200/90 text-primary font-medium"
                            : "hover:bg-gray-200/60 text-muted-foreground"
                        )}
                      >
                        <subItem.icon className={cn(
                          "h-4 w-4 mr-3 shrink-0",
                          isSubActive ? "text-primary" : "text-muted-foreground"
                        )} />
                        <span className="text-sm">
                          {subItem.title}
                        </span>
                      </Link>
                    );
                  })}
                </div>
              )}
            </>
          )}
        </div>
      );
    });
  };

  return (
    <nav className="bg-transparent">
      {renderMenuItems(menuItems)}
    </nav>
  );
}

export const Sidebar = SidebarComponent;