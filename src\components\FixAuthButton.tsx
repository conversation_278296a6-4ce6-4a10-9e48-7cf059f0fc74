'use client';

import { Button } from '@/components/ui/button';
import { clearSupabaseCookies, resetSession } from '@/lib/fixCookies';
import { toast } from 'sonner';
import { RefreshCw } from 'lucide-react';

interface FixAuthButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
}

export function FixAuthButton({
  variant = 'outline',
  size = 'sm',
  className = ''
}: FixAuthButtonProps) {
  const handleFixAuth = () => {
    try {
      // Limpar cookies do Supabase
      const result = clearSupabaseCookies();

      if (result) {
        toast.success('Cookies de autenticação limpos com sucesso');

        // Recarregar a página após um breve delay
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        toast.error('Erro ao limpar cookies');
      }
    } catch (error) {
      toast.error('Erro ao tentar corrigir autenticação');
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleFixAuth}
      className={className}
    >
      <RefreshCw className="h-4 w-4 mr-2" />
      Corrigir Autenticação
    </Button>
  );
}