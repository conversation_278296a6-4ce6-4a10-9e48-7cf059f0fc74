-- Função para buscar lançamentos com suas parcelas em uma única chamada
CREATE OR REPLACE FUNCTION public.buscar_lancamentos_com_parcelas()
RETURNS SETOF json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    lancamento_record RECORD;
    parcelas_json json;
    contato_json json;
    obra_json json;
    categoria_json json;
    resultado json;
BEGIN
    -- Para cada lançamento
    FOR lancamento_record IN 
        SELECT * FROM lancamentos ORDER BY created_at DESC
    LOOP
        -- Buscar parcelas para este lançamento
        SELECT json_agg(p) INTO parcelas_json
        FROM parcelas p
        WHERE p.lancamento_id = lancamento_record.id
        ORDER BY p.numero ASC;
        
        -- Se não houver parcelas, usar array vazio
        IF parcelas_json IS NULL THEN
            parcelas_json = '[]'::json;
        END IF;
        
        -- Buscar contato relacionado
        SELECT row_to_json(c) INTO contato_json
        FROM contatos c
        WHERE c.id = lancamento_record.contato_id;
        
        -- Buscar obra relacionada
        SELECT row_to_json(o) INTO obra_json
        FROM obras o
        WHERE o.id = lancamento_record.obra_id;
        
        -- Buscar categoria relacionada
        SELECT row_to_json(c) INTO categoria_json
        FROM categorias c
        WHERE c.id = lancamento_record.categoria_id;
        
        -- Construir o objeto de resultado
        SELECT json_build_object(
            'id', lancamento_record.id,
            'descricao', lancamento_record.descricao,
            'valor_total', lancamento_record.valor_total,
            'data_competencia', lancamento_record.data_competencia,
            'forma_pagamento', lancamento_record.forma_pagamento,
            'tipo', lancamento_record.tipo,
            'status', lancamento_record.status,
            'observacoes', lancamento_record.observacoes,
            'obra_id', lancamento_record.obra_id,
            'contato_id', lancamento_record.contato_id,
            'categoria_id', lancamento_record.categoria_id,
            'created_at', lancamento_record.created_at,
            'updated_at', lancamento_record.updated_at,
            'user_id', lancamento_record.user_id,
            'parcelas', parcelas_json,
            'contatos', contato_json,
            'obras', obra_json,
            'categorias', categoria_json
        ) INTO resultado;
        
        -- Retornar o resultado
        RETURN NEXT resultado;
    END LOOP;
    
    RETURN;
END;
$$;

-- Conceder permissões para todos os usuários
GRANT EXECUTE ON FUNCTION public.buscar_lancamentos_com_parcelas() TO authenticated;
GRANT EXECUTE ON FUNCTION public.buscar_lancamentos_com_parcelas() TO anon;
GRANT EXECUTE ON FUNCTION public.buscar_lancamentos_com_parcelas() TO service_role;
