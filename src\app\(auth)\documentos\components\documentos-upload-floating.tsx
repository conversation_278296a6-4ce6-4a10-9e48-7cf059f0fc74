'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Upload, X, FileText, Image, ArrowUp, Eye, Trash2, CheckCircle2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { uploadDocumento, getDocumentoById, atualizarDadosExtraidos, atualizarContatoDocumento } from '@/services/documentos';
import { analisarDocumento } from '@/services/ai-analysis';
import { supabase } from '@/lib/supabase/client';
import { useContatosService } from '@/services/contatos';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

interface DocumentosUploadFloatingProps {
  onUploadSuccess: (file?: File, step?: number, isComplete?: boolean) => void;
}

interface UploadedDocument {
  id: string;
  nome: string;
  arquivo_url: string;
  tipo_arquivo: string;
  dados_extraidos?: any;
  status: 'uploading' | 'analyzing' | 'ready' | 'error';
  error?: string;
}

interface PendingFile {
  file: File;
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress?: string;
  error?: string;
}

export function DocumentosUploadFloating({ onUploadSuccess }: DocumentosUploadFloatingProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [uploadedDocIds, setUploadedDocIds] = useState<string[]>([]);
  const [processingFile, setProcessingFile] = useState<File | null>(null);
  const [processingStep, setProcessingStep] = useState<number>(0);
  const [processingComplete, setProcessingComplete] = useState(false);

  // Estados para a nova funcionalidade
  const [showAnalysisModal, setShowAnalysisModal] = useState(false);
  const [uploadedDocuments, setUploadedDocuments] = useState<UploadedDocument[]>([]);
  const [isAnalysisMode, setIsAnalysisMode] = useState(false);
  const [pendingFiles, setPendingFiles] = useState<PendingFile[]>([]); // Arquivos aguardando processamento
  const [isPasting, setIsPasting] = useState(false); // Estado para indicar quando está colando

  const buttonRef = useRef<HTMLButtonElement>(null);
  const dropzoneRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const processingStepTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Inicializar o serviço de contatos
  const contatosService = useContatosService();

  // Event listener para CTRL+V (colar arquivos do clipboard)
  useEffect(() => {
    const handlePaste = (e: ClipboardEvent) => {
      // Só processar se a modal estiver aberta
      if (!showAnalysisModal) return;

      // Verificar se há arquivos no clipboard
      const clipboardItems = e.clipboardData?.items;
      if (!clipboardItems) return;

      const files: File[] = [];

      // Processar todos os itens do clipboard
      for (let i = 0; i < clipboardItems.length; i++) {
        const item = clipboardItems[i];
        
        // Verificar se é um arquivo
        if (item.kind === 'file') {
          const file = item.getAsFile();
          if (file) {
            // Verificar se é um tipo de arquivo aceito (PDF, PNG, JPEG)
            if (file.type === 'application/pdf' || 
                file.type === 'image/png' || 
                file.type === 'image/jpeg' ||
                file.type === 'image/jpg') {
              files.push(file);
            } else {
              toast.error(`Arquivo não suportado: ${file.name}. Apenas PDF, PNG e JPEG são aceitos.`);
            }
          }
        }
      }

      // Se encontrou arquivos válidos, adicioná-los à lista
      if (files.length > 0) {
        // Prevenir o comportamento padrão do paste
        e.preventDefault();
        
        // Indicar que está processando o paste
        setIsPasting(true);
        
        // Adicionar os arquivos à lista de pendentes
        setPendingFiles(prev => {
          const newFiles: PendingFile[] = [...prev, ...files.map(file => ({
            file,
            id: Date.now().toString() + Math.random().toString(),
            status: 'pending' as const
          }))];
          return newFiles;
        });

        toast.success(`${files.length} arquivo(s) colado(s) com sucesso!`);
        
        // Remover o indicador de pasting após um breve delay
        setTimeout(() => setIsPasting(false), 1000);
      }
    };

    // Adicionar o event listener quando a modal estiver aberta
    if (showAnalysisModal) {
      document.addEventListener('paste', handlePaste);
    }

    // Cleanup do event listener
    return () => {
      document.removeEventListener('paste', handlePaste);
    };
  }, [showAnalysisModal]);

  // Função para lidar com o fechamento
  const handleClose = () => {
    setIsExpanded(false);
    setFiles([]);
  };

  // Função para fechar a modal de análise
  const handleCloseAnalysisModal = () => {
    setShowAnalysisModal(false);
    setUploadedDocuments([]);
    setPendingFiles([]);
    setIsAnalysisMode(false);
    // Limpar o input file
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    // Atualizar a lista de documentos na página principal
    onUploadSuccess(undefined, 0, true);
  };

  // Função para lidar com a seleção de arquivos na modal (apenas adicionar à lista)
  const handleFileSelectModal = (e: React.ChangeEvent<HTMLInputElement>) => {// Log detalhado de cada arquivo
    if (e.target.files) {
      for (let i = 0; i < e.target.files.length; i++) {}
    }
    
    if (e.target.files && e.target.files.length > 0) {
      const selectedFiles = Array.from(e.target.files);// Filtrar apenas arquivos PDF, PNG e JPEG
      const validFiles = selectedFiles.filter(file => {
        const isValid =
          file.type === 'application/pdf' ||
          file.type === 'image/png' ||
          file.type === 'image/jpeg';

        if (!isValid) {
          toast.error(`Arquivo não suportado: ${file.name}. Apenas PDF, PNG e JPEG são aceitos.`);
        }

        return isValid;
      });if (validFiles.length > 0) {
        // Apenas adicionar à lista de arquivos pendentes, sem processar
        setPendingFiles(prev => {
          const newFiles: PendingFile[] = [...prev, ...validFiles.map(file => ({
            file,
            id: Date.now().toString() + Math.random().toString(),
            status: 'pending' as const
          }))];return newFiles;
        });toast.success(`${validFiles.length} arquivo(s) adicionado(s) para análise!`);
        
        // Limpar o input automaticamente após a seleção para evitar cache
        setTimeout(() => {
          if (fileInputRef.current) {fileInputRef.current.value = '';
          }
        }, 100);
      }
    } else {}};

  // Função para atualizar o status de um arquivo específico
  const updatePendingFileStatus = (fileName: string, status: PendingFile['status'], progress?: string, error?: string) => {
    setPendingFiles(prev => 
      prev.map(p => 
        p.file.name === fileName 
          ? { ...p, status, progress, error }
          : p
      )
    );
  };

  // Função para processar todos os arquivos pendentes
  const handleProcessPendingFiles = () => {
    if (pendingFiles.length === 0) {
      toast.error('Nenhum arquivo para processar!');
      return;
    }

    // Marcar todos como processing
    setPendingFiles(prev => prev.map(p => ({ ...p, status: 'processing' as const, progress: 'Iniciando...' })));
    
    // Processar cada arquivo individualmente
    uploadFilesForAnalysisWithProgress(pendingFiles);
    
    // Limpar o input file com delay
    setTimeout(() => {
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }, 1000);
  };

  // Função para upload de arquivos para análise com progresso (nova versão)
  const uploadFilesForAnalysisWithProgress = async (filesToUpload: PendingFile[]) => {
    if (filesToUpload.length === 0) {
      return;
    }

    try {
      setIsUploading(true);

      // Obter o usuário atual
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('Usuário não autenticado');
      }

      // Upload de cada arquivo
      for (let i = 0; i < filesToUpload.length; i++) {
        const pendingFile = filesToUpload[i];
        const file = pendingFile.file;

        try {
          // Atualizar status para uploading
          updatePendingFileStatus(file.name, 'processing', 'Enviando arquivo...');

          // Upload do arquivo
          const documento = await uploadDocumento(file, user.id);

          // Atualizar status para analyzing
          updatePendingFileStatus(file.name, 'processing', 'Analisando com IA...');

          // Analisar documento
          try {
            const dadosExtraidos = await analisarDocumento(documento.arquivo_url);
            
            await atualizarDadosExtraidos(documento.id, dadosExtraidos);

            // Não mais vincular contato automaticamente - apenas detectar dados
            // O contato será criado apenas quando o documento for aprovado
            updatePendingFileStatus(file.name, 'processing', 'Dados detectados pela IA');

            // Marcar como concluído
            updatePendingFileStatus(file.name, 'completed', 'Processamento concluído');

            // Adicionar aos documentos processados
            const newDoc: UploadedDocument = {
              ...documento,
              dados_extraidos: dadosExtraidos,
              status: 'ready'
            };
            setUploadedDocuments(prev => [...prev, newDoc]);

          } catch (analysisError: any) {
            updatePendingFileStatus(file.name, 'error', undefined, 'Erro na análise automática: ' + analysisError.message);
          }

        } catch (uploadError: any) {
          updatePendingFileStatus(file.name, 'error', undefined, 'Erro no upload: ' + (uploadError.message || 'Erro desconhecido'));
        }
      }

      toast.success(`${filesToUpload.length} arquivo(s) processado(s) para análise!`);

    } catch (error: any) {
      toast.error(error.message || 'Erro ao processar arquivos para análise');
    } finally {
      setIsUploading(false);
    }
  };

  // Função para upload de arquivos apenas para análise (sem criar lançamento) - versão original
  const uploadFilesForAnalysis = async (filesToUpload: File[]) => {try {
      setIsUploading(true);
      const newDocuments: UploadedDocument[] = [];

      // Obter o usuário atual
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('Usuário não autenticado');
      }// Upload de cada arquivo
      for (let i = 0; i < filesToUpload.length; i++) {
        const file = filesToUpload[i];try {
          // Criar documento com status uploading
          const docTemp: UploadedDocument = {
            id: Date.now().toString() + Math.random().toString(),
            nome: file.name,
            arquivo_url: '',
            tipo_arquivo: file.type,
            status: 'uploading'
          };newDocuments.push(docTemp);
          setUploadedDocuments(prev => {return [...prev, docTemp];
          });

          // Upload do arquivo
          const documento = await uploadDocumento(file, user.id);
          
          // Atualizar status para analyzing
          const docIndex = newDocuments.findIndex(d => d.id === docTemp.id);if (docIndex !== -1) {
            newDocuments[docIndex] = {
              ...documento,
              status: 'analyzing'
            };
            setUploadedDocuments(prev => 
              prev.map(d => d.id === docTemp.id ? newDocuments[docIndex] : d)
            );}

          // Analisar documento
          try {const dadosExtraidos = await analisarDocumento(documento.arquivo_url);await atualizarDadosExtraidos(documento.id, dadosExtraidos);

            // Não mais vincular contato automaticamente - apenas detectar dados
            // O contato será criado apenas quando o documento for aprovado

            // Atualizar status para ready
            if (docIndex !== -1) {
              newDocuments[docIndex] = {
                ...newDocuments[docIndex],
                dados_extraidos: dadosExtraidos,
                status: 'ready'
              };
              setUploadedDocuments(prev => 
                prev.map(d => d.id === docTemp.id ? newDocuments[docIndex] : d)
              );}

          } catch (analysisError: any) {
            
            // Marcar como ready mesmo com erro na análise
            if (docIndex !== -1) {
              newDocuments[docIndex] = {
                ...newDocuments[docIndex],
                status: 'ready',
                error: 'Erro na análise automática'
              };
              setUploadedDocuments(prev => 
                prev.map(d => d.id === docTemp.id ? newDocuments[docIndex] : d)
              );
            }
          }

        } catch (uploadError: any) {
          
          // Criar documento com erro se não foi criado ainda
          const docToUpdate: UploadedDocument = {
            id: Date.now().toString() + Math.random().toString(),
            nome: file.name,
            arquivo_url: '',
            tipo_arquivo: file.type,
            status: 'error',
            error: uploadError.message || 'Erro no upload'
          };
          newDocuments.push(docToUpdate);
          setUploadedDocuments(prev => [...prev, docToUpdate]);
        }
      }toast.success(`${filesToUpload.length} arquivo(s) processado(s) para análise!`);

    } catch (error: any) {
      
      toast.error(error.message || 'Erro ao processar arquivos para análise');
    } finally {
      setIsUploading(false);}
  };

  // Função para buscar e vincular contato automaticamente
  const buscarEVincularContato = async (documentoId: string, fornecedor: string | null | undefined) => {
    if (!fornecedor) return null;

    try {
      // Buscar contato pelo nome do fornecedor
      const contatoEncontrado = await contatosService.buscarContatoPorNome(fornecedor);

      if (contatoEncontrado) {
        // Vincular o contato ao documento
        await atualizarContatoDocumento(documentoId, contatoEncontrado.id);

        // Retornar o contato encontrado
        return contatoEncontrado;
      }
    } catch (error) {
      
    }

    return null;
  };

  // Função para analisar um documento com avanço gradual da animação (fluxo original)
  const analisarDocumentoUpload = async (documentoId: string) => {
    try {
      // Buscar o documento para obter a URL
      const documento = await getDocumentoById(documentoId);

      if (!documento) {
        
        return;
      }

      // Avançar para o passo de extração de texto (OCR)
      setProcessingStep(1); // OCR
      onUploadSuccess(undefined, 1, false); // Notificar a página principal
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Avançar para o passo de identificação de fornecedor
      setProcessingStep(2); // Identificando fornecedor
      onUploadSuccess(undefined, 2, false); // Notificar a página principal
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Avançar para o passo de extração de valores
      setProcessingStep(3); // Extraindo valores
      onUploadSuccess(undefined, 3, false); // Notificar a página principal

      // Analisar o documento com IA (processo real)
      const dadosExtraidos = await analisarDocumento(documento.arquivo_url);

      // Avançar para o passo de extração de datas
      setProcessingStep(4); // Extraindo datas
      onUploadSuccess(undefined, 4, false); // Notificar a página principal
      await new Promise(resolve => setTimeout(resolve, 800));

      // Avançar para o passo de classificação
      setProcessingStep(5); // Classificando documento
      onUploadSuccess(undefined, 5, false); // Notificar a página principal
      await new Promise(resolve => setTimeout(resolve, 800));

      // Avançar para o passo de verificação de duplicidade
      setProcessingStep(6); // Verificando duplicidade
      onUploadSuccess(undefined, 6, false); // Notificar a página principal

      // Atualizar o documento com os dados extraídos
      const documentoAtualizado = await atualizarDadosExtraidos(documentoId, dadosExtraidos);

      // Buscar e vincular contato automaticamente
      if (documentoAtualizado && (documentoAtualizado.fornecedor || documentoAtualizado.dados_extraidos?.fornecedor)) {
        const fornecedor = documentoAtualizado.fornecedor || documentoAtualizado.dados_extraidos?.fornecedor;
        const contatoEncontrado = await buscarEVincularContato(documentoId, fornecedor);

        if (contatoEncontrado) {}
      }

      // Avançar para o passo final
      setProcessingStep(7); // Finalizando análise
      onUploadSuccess(undefined, 7, false); // Notificar a página principal
      await new Promise(resolve => setTimeout(resolve, 800));

      return true;
    } catch (error: any) {
      
      return false;
    }
  };

  // Função para visualizar documento
  const handleViewDocument = (documento: UploadedDocument) => {
    if (documento.arquivo_url) {
      window.open(documento.arquivo_url, '_blank');
    }
  };

  // Função para remover documento da lista de análise
  const handleRemoveDocument = (documentId: string) => {
    setUploadedDocuments(prev => prev.filter(d => d.id !== documentId));
  };

  // Limpar ao desmontar o componente
  useEffect(() => {
    return () => {
      if (processingStepTimerRef.current) {
        clearTimeout(processingStepTimerRef.current);
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Função para lidar com o início do arrasto de arquivos
  const handleDragEnter = (e: DragEvent) => {
    // Verificar múltiplas formas se há drawer de lançamento ou documento ativo
    const hasLancamentoDrawerOpen = document.querySelector('[data-drawer-lancamento-open="true"]');
    const hasDocumentoDrawerOpen = document.body.hasAttribute('data-drawer-documento-open');
    const hasEnrichmentActive = document.body.hasAttribute('data-enrichment-drawer-active');
    const isDisabledByDrawer = document.body.hasAttribute('data-disabled-by-drawer');
    
    if (hasLancamentoDrawerOpen || hasDocumentoDrawerOpen || hasEnrichmentActive || isDisabledByDrawer) {
      // NÃO chamar preventDefault() nem stopPropagation() para deixar o drawer interceptar
      return; 
    }
    e.preventDefault();
    e.stopPropagation();

    // Expandir o componente se não estiver expandido
    if (!isExpanded) {
      setIsExpanded(true);
    }

    // Ativar o estado de arrastar
    setIsDragging(true);
  };

  // Limpar timers de processamento
  const clearProcessingTimers = () => {
    if (processingStepTimerRef.current) {
      clearTimeout(processingStepTimerRef.current);
      processingStepTimerRef.current = null;
    }
  };

  // Função para fazer upload dos arquivos
  const uploadFiles = async (filesToUpload?: File[]) => {
    // Usar os arquivos fornecidos ou os do estado
    const currentFiles = filesToUpload || files;

    // Verificar se há arquivos para enviar
    if (currentFiles.length === 0) {
      toast.error('Selecione pelo menos um arquivo para enviar.');
      return;
    }

    // Evitar múltiplos uploads simultâneos
    if (isUploading || isAnalyzing) {
      toast.info('Upload já em andamento, aguarde a conclusão.');
      return;
    }

    setIsUploading(true);
    setUploadedDocIds([]);

    try {
      // Obter o ID do usuário atual
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('Usuário não autenticado');
      }

      const userId = session.user.id;

      // Iniciar o processamento com o primeiro arquivo
      if (currentFiles.length > 0) {
        // Configurar o arquivo que está sendo processado e notificar a página principal
        setProcessingFile(currentFiles[0]);
        setProcessingStep(0); // Iniciar no primeiro passo
        setProcessingComplete(false);

        // Notificar a página principal sobre o início do processamento
        onUploadSuccess(currentFiles[0], 0, false);
      }

      // Upload de cada arquivo usando o serviço - um por um para evitar problemas
      const uploadedDocs = [];
      for (const file of currentFiles) {
        try {
          const doc = await uploadDocumento(file, userId);
          uploadedDocs.push(doc);
        } catch (error: any) {
          
          toast.error(`Erro ao enviar ${file.name}: ${error.message || 'Erro desconhecido'}`);
        }
      }

      // Se nenhum upload foi bem-sucedido, lançar erro
      if (uploadedDocs.length === 0) {
        throw new Error('Nenhum arquivo foi enviado com sucesso');
      }

      // Armazenar os IDs dos documentos enviados
      const docIds = uploadedDocs.map(doc => doc.id);
      setUploadedDocIds(docIds);

      // Limpar a lista de arquivos somente após o upload bem-sucedido
      setFiles([]);

      // Iniciar análise automática dos documentos
      setIsAnalyzing(true);

      // Analisar cada documento
      for (const docId of docIds) {
        try {
          await analisarDocumentoUpload(docId);
        } catch (error: any) {
          
          // Não exibir toast para não sobrecarregar o usuário
        }
      }

      // Marcar o processamento como completo
      setProcessingComplete(true);

      // Notificar a página principal sobre a conclusão
      onUploadSuccess(undefined, 7, true);

      // Aguardar um pouco para que o usuário veja a mensagem de conclusão
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Fechar o componente após o upload bem-sucedido
      setIsExpanded(false);

      // Mostrar toast de sucesso
      toast.success(`${uploadedDocs.length} documento(s) enviado(s) com sucesso!`);
    } catch (error: any) {

// Verificar se é erro de autenticação
      if (error.message && (
        error.message.includes('auth') ||
        error.message.includes('JWT') ||
        error.message.includes('token') ||
        error.message.includes('401') ||
        error.message.includes('não autenticado')
      )) {
        toast.error('Erro de autenticação. Tente fazer login novamente.');
      } else {
        toast.error(error.message || 'Erro ao enviar documentos');
      }
    } finally {
      setIsUploading(false);
      setIsAnalyzing(false);
      setProcessingFile(null);
    }
  };

  const getFileIcon = (file: File) => {
    if (file.type === 'application/pdf') {
      return <FileText className="h-5 w-5 text-red-500" />;
    }
    return <Image className="h-5 w-5 text-blue-500" />;
  };

  // Handlers para outros eventos de drag
  const handleDragOver = (e: DragEvent) => {
    const hasLancamentoDrawerOpen = document.querySelector('[data-drawer-lancamento-open="true"]');
    const hasDocumentoDrawerOpen = document.body.hasAttribute('data-drawer-documento-open');
    const hasEnrichmentActive = document.body.hasAttribute('data-enrichment-drawer-active');
    if (hasLancamentoDrawerOpen || hasDocumentoDrawerOpen || hasEnrichmentActive) {
      return; // Não interceptar se drawer está ativo
    }
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragLeave = (e: DragEvent) => {
    const hasLancamentoDrawerOpen = document.querySelector('[data-drawer-lancamento-open="true"]');
    const hasDocumentoDrawerOpen = document.body.hasAttribute('data-drawer-documento-open');
    const hasEnrichmentActive = document.body.hasAttribute('data-enrichment-drawer-active');
    if (hasLancamentoDrawerOpen || hasDocumentoDrawerOpen || hasEnrichmentActive) {
      return; // Não interceptar se drawer está ativo
    }
    e.preventDefault();
    e.stopPropagation();
  };

  const handleGlobalDrop = (e: DragEvent) => {
    const hasLancamentoDrawerOpen = document.querySelector('[data-drawer-lancamento-open="true"]');
    const hasDocumentoDrawerOpen = document.body.hasAttribute('data-drawer-documento-open');
    const hasEnrichmentActive = document.body.hasAttribute('data-enrichment-drawer-active');
    if (hasLancamentoDrawerOpen || hasDocumentoDrawerOpen || hasEnrichmentActive) {
      console.log('🚫 DocumentosUploadFloating: Drop ignorado, drawer ativo');
      return; // Não interceptar se drawer está ativo
    }
    e.preventDefault();
    e.stopPropagation();
  };

  // Adicionar event listeners para drag and drop global (mas ceder prioridade ao drawer de lançamento)
  useEffect(() => {
    // Adicionar event listeners para drag and drop global com prioridade baixa
    document.addEventListener('dragenter', handleDragEnter, { capture: false });
    document.addEventListener('dragover', handleDragOver, { capture: false });
    document.addEventListener('dragleave', handleDragLeave, { capture: false });
    document.addEventListener('drop', handleGlobalDrop, { capture: false });

    return () => {
      document.removeEventListener('dragenter', handleDragEnter, { capture: false });
      document.removeEventListener('dragover', handleDragOver, { capture: false });
      document.removeEventListener('dragleave', handleDragLeave, { capture: false });
      document.removeEventListener('drop', handleGlobalDrop, { capture: false });
    };
  }, [isExpanded]);

  // Função para remover arquivo da lista de pendentes
  const handleRemovePendingFile = (index: number) => {
    setPendingFiles(prev => prev.filter((_, i) => i !== index));
  };

  // Função para lidar com clique no botão (modo análise)
  const handleButtonClick = () => {setShowAnalysisModal(true);};

  // Função para lidar com o drop de arquivos (modo automático - fluxo atual)
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    setIsAnalysisMode(false); // Modo automático

    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      // Filtrar apenas arquivos PDF, PNG e JPEG
      const validFiles = droppedFiles.filter(file => {
        const isValid =
          file.type === 'application/pdf' ||
          file.type === 'image/png' ||
          file.type === 'image/jpeg';

        if (!isValid) {
          toast.error(`Arquivo não suportado: ${file.name}. Apenas PDF, PNG e JPEG são aceitos.`);
        }

        return isValid;
      });

      if (validFiles.length > 0) {
        setFiles(validFiles);
        setIsExpanded(true);
        uploadFiles(validFiles);
      }
    }
  };

  // Função para limpar arquivos concluídos/com erro
  const handleClearCompletedFiles = () => {
    setPendingFiles(prev => prev.filter(f => f.status === 'pending' || f.status === 'processing'));
  };

  // Contadores para a interface
  const pendingCount = pendingFiles.filter(f => f.status === 'pending').length;
  const processingCount = pendingFiles.filter(f => f.status === 'processing').length;
  const completedCount = pendingFiles.filter(f => f.status === 'completed').length;
  const errorCount = pendingFiles.filter(f => f.status === 'error').length;

  return (
    <div>
      {/* Esconder todo o componente durante processamento/análise */}
      {!isUploading && !isAnalyzing && !processingFile && (
        <>
          {/* Botão flutuante */}
          <div className="fixed bottom-6 right-6 z-50">
            <div
              className="simple-button-container"
              onMouseEnter={() => setIsHovering(true)}
              onMouseLeave={() => setIsHovering(false)}
            >
              <button
                title="Upload de documentos para análise"
                className="h-12 w-12 rounded-full bg-white border border-gray-300 cursor-pointer flex items-center justify-center shadow-lg transition-all duration-300 relative z-[9999] hover:shadow-xl"
                onClick={(e) => {e.preventDefault();
                  e.stopPropagation();
                  handleButtonClick();
                }}
                onMouseEnter={() => {
                  setIsHovering(true);
                }}
                onMouseLeave={() => {
                  setIsHovering(false);
                }}
              >
                <Upload className={`h-5 w-5 transition-colors duration-200 ${
                  isHovering ? 'text-blue-500' : 'text-gray-700'
                }`} />
              </button>
              {isHovering && !isExpanded && (
                <div className="glow-effect"></div>
              )}
            </div>

            {/* Área expandida - Só aparece no drag/drop */}
            <div
              ref={dropzoneRef}
              className={cn(
                "absolute bottom-0 right-0 bg-white rounded-lg shadow-xl p-4 overflow-hidden",
                "border border-gray-200",
                isExpanded
                  ? "slide-in-right opacity-100 w-80 h-auto"
                  : "slide-out-right opacity-0 w-0 h-0 pointer-events-none",
                isDragging && "ring-2 ring-blue-400 bg-blue-50/50 upload-area-dragging"
              )}
              onDragOver={(e) => {
                e.preventDefault();
                setIsDragging(true);
              }}
              onDragLeave={(e) => {
                // Verificar se o mouse saiu realmente da área e não apenas entrou em um elemento filho
                const rect = e.currentTarget.getBoundingClientRect();
                const x = e.clientX;
                const y = e.clientY;

                if (
                  x < rect.left ||
                  x > rect.right ||
                  y < rect.top ||
                  y > rect.bottom
                ) {
                  setIsDragging(false);
                }
              }}
              onDrop={handleDrop}
            >
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-sm font-medium text-gray-700">
                  {isAnalyzing ? 'Analisando documento...' : 
                   isUploading ? 'Enviando documentos...' : 
                   'Criando Lançamento...'}
                </h3>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 text-gray-400 hover:text-gray-700"
                  onClick={handleClose}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {files.length > 0 ? (
                <div className="space-y-2">
                  {/* Se está analisando, mostrar apenas indicador de progresso limpo */}
                  {isAnalyzing ? (
                    <div className="text-center py-4">
                      <div className="mb-3 flex justify-center">
                        <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center">
                          <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                        </div>
                      </div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Processando documento...</h4>
                      <p className="text-xs text-gray-500">
                        {processingFile ? (processingFile as File).name : 'Analisando com IA...'}
                      </p>
                      <div className="mt-3 w-full bg-gray-100 h-1.5 rounded-full overflow-hidden">
                        <div className="bg-blue-500 h-full rounded-full transition-all duration-300 upload-progress-active w-[70%]" />
                      </div>
                    </div>
                  ) : (
                    <>
                      {/* Lista de arquivos - só mostra se não está analisando */}
                      {files.map((file, index) => (
                        <div key={index} className="flex items-center gap-2 text-xs p-2 bg-gray-50 rounded">
                          {getFileIcon(file)}
                          <span className="truncate flex-1">{file.name}</span>
                          <span className="text-gray-400">{(file.size / 1024).toFixed(0)} KB</span>
                        </div>
                      ))}

                      <div className="mt-3">
                        <div className="w-full bg-gray-100 h-1.5 rounded-full overflow-hidden">
                          <div
                            className={cn(
                              "bg-blue-500 h-full rounded-full transition-all duration-300",
                              isUploading && "upload-progress-active",
                              isUploading ? "w-[70%]" : "w-0"
                            )}
                          />
                        </div>
                        <div className="text-xs text-gray-500 mt-1 text-center">
                          {isUploading ? 'Enviando arquivos...' : 'Processamento concluído'}
                        </div>
                      </div>
                    </>
                  )}
                </div>
              ) : (
                <div className="text-center py-6 px-4">
                  <div className="mb-3 flex justify-center">
                    <div className={cn(
                      "simple-upload-icon",
                      isDragging && "upload-icon-dragging"
                    )}>
                      <ArrowUp className="h-6 w-6 text-blue-500" />
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Arraste arquivos aqui para criar lançamentos automaticamente
                  </p>
                  <p className="text-xs text-gray-500">
                    Ou clique no botão para analisar múltiplos arquivos
                  </p>
                </div>
              )}
            </div>
          </div>
        </>
      )}

      {/* Modal para análise de múltiplos documentos */}
      <Dialog 
        open={showAnalysisModal} 
        onOpenChange={(open) => {
          if (!open) {}
          setShowAnalysisModal(open);
        }}
      >
        <DialogContent 
          className="sm:max-w-4xl max-h-[80vh] overflow-y-auto bg-white"
          style={{ zIndex: 10000 }}
        >
          <DialogHeader>
            <DialogTitle>Análise de Documentos</DialogTitle>
            <DialogDescription>
              Envie múltiplos documentos para análise. Eles serão processados mas não criarão lançamentos automaticamente.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Área de upload */}
            <div className={cn(
              "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
              isPasting 
                ? "border-blue-400 bg-blue-50" 
                : "border-gray-300 hover:border-gray-400"
            )}>
              <div className="flex flex-col items-center gap-4">
                <Upload className="h-12 w-12 text-gray-400" />
                <div>
                  <p className="text-lg font-medium text-gray-700">
                    Selecione os arquivos para análise
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    PDF, PNG e JPEG aceitos. Múltiplos arquivos permitidos.
                  </p>
                  <p className="text-xs text-blue-600 mt-2 font-medium">
                    {isPasting ? (
                      <>✨ Colando arquivo(s)...</>
                    ) : (
                      <>💡 Dica: Use Ctrl+V para colar imagens da área de transferência</>
                    )}
                  </p>
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  id="file-upload-modal"
                  className="hidden"
                  multiple
                  accept=".pdf,.png,.jpg,.jpeg"
                  onChange={handleFileSelectModal}
                  title="Selecionar arquivos para análise"
                  aria-label="Selecionar arquivos para análise"
                />
                <Button
                  onClick={() => {if (fileInputRef.current) {fileInputRef.current.click();
                    } else {document.getElementById('file-upload-modal')?.click();
                    }
                  }}
                  disabled={isUploading}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isUploading ? 'Processando...' : 'Selecionar Arquivos'}
                </Button>
              </div>
            </div>

            {/* Lista de arquivos pendentes */}
            {pendingFiles.length > 0 && (
              <div className="space-y-4">
                <Separator />
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Arquivos Selecionados</h3>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1">
                      {pendingCount > 0 && (
                        <Badge variant="outline" className="text-yellow-700 border-yellow-300">
                          {pendingCount} aguardando
                        </Badge>
                      )}
                      {processingCount > 0 && (
                        <Badge variant="outline" className="text-blue-700 border-blue-300">
                          {processingCount} processando
                        </Badge>
                      )}
                      {completedCount > 0 && (
                        <Badge variant="outline" className="text-green-700 border-green-300">
                          {completedCount} concluídos
                        </Badge>
                      )}
                      {errorCount > 0 && (
                        <Badge variant="outline" className="text-red-700 border-red-300">
                          {errorCount} com erro
                        </Badge>
                      )}
                    </div>
                    {(completedCount > 0 || errorCount > 0) && (
                      <Button 
                        onClick={handleClearCompletedFiles}
                        variant="outline"
                        size="sm"
                        className="text-xs"
                      >
                        Limpar Finalizados
                      </Button>
                    )}
                    <Button 
                      onClick={handleProcessPendingFiles}
                      disabled={isUploading || pendingCount === 0}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      {isUploading ? 'Processando...' : `Processar ${pendingCount > 0 ? pendingCount : 'Todos'}`}
                    </Button>
                  </div>
                </div>

                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {pendingFiles.map((file, index) => (
                    <div
                      key={file.id}
                      className={cn(
                        "flex items-center justify-between p-3 border rounded-lg",
                        file.status === 'pending' && "bg-yellow-50 border-yellow-200",
                        file.status === 'processing' && "bg-blue-50 border-blue-200",
                        file.status === 'completed' && "bg-green-50 border-green-200", 
                        file.status === 'error' && "bg-red-50 border-red-200"
                      )}
                    >
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        {getFileIcon(file.file)}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{file.file.name}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <p className="text-xs text-gray-500">
                              {(file.file.size / 1024).toFixed(0)} KB
                            </p>
                            {file.status === 'pending' && (
                              <Badge variant="secondary" className="text-xs">
                                Aguardando
                              </Badge>
                            )}
                            {file.status === 'processing' && (
                              <div className="flex items-center gap-1">
                                <div className="w-3 h-3 border border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                                <Badge variant="secondary" className="text-xs text-blue-700">
                                  {file.progress || 'Processando...'}
                                </Badge>
                              </div>
                            )}
                            {file.status === 'completed' && (
                              <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                                <CheckCircle2 className="h-3 w-3 mr-1" />
                                Concluído
                              </Badge>
                            )}
                            {file.status === 'error' && (
                              <Badge variant="destructive" className="text-xs">
                                Erro
                              </Badge>
                            )}
                          </div>
                          {file.error && (
                            <p className="text-xs text-red-600 mt-1">{file.error}</p>
                          )}
                        </div>
                      </div>

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemovePendingFile(index)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                        disabled={file.status === 'processing'}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Lista de documentos processados */}
            {uploadedDocuments.length > 0 && (
              <div className="space-y-4">
                <Separator />
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Documentos Processados</h3>
                  <Badge variant="outline">
                    {uploadedDocuments.length} arquivo(s)
                  </Badge>
                </div>

                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {uploadedDocuments.map((doc) => (
                    <div
                      key={doc.id}
                      className="flex items-center justify-between p-3 border rounded-lg bg-gray-50"
                    >
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        {getFileIcon({ type: doc.tipo_arquivo } as File)}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{doc.nome}</p>
                          <div className="flex items-center gap-2 mt-1">
                            {doc.status === 'uploading' && (
                              <Badge variant="secondary" className="text-xs">
                                Enviando...
                              </Badge>
                            )}
                            {doc.status === 'analyzing' && (
                              <Badge variant="secondary" className="text-xs">
                                Analisando...
                              </Badge>
                            )}
                            {doc.status === 'ready' && (
                              <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                                <CheckCircle2 className="h-3 w-3 mr-1" />
                                Pronto
                              </Badge>
                            )}
                            {doc.status === 'error' && (
                              <Badge variant="destructive" className="text-xs">
                                Erro
                              </Badge>
                            )}
                            {doc.dados_extraidos && (
                              <div className="text-xs text-gray-600">
                                {doc.dados_extraidos.fornecedor && (
                                  <span className="mr-2">
                                    📊 {doc.dados_extraidos.fornecedor}
                                  </span>
                                )}
                                {doc.dados_extraidos.valor_total && (
                                  <span>
                                    💰 {new Intl.NumberFormat('pt-BR', {
                                      style: 'currency',
                                      currency: 'BRL'
                                    }).format(doc.dados_extraidos.valor_total)}
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                          {doc.error && (
                            <p className="text-xs text-red-600 mt-1">{doc.error}</p>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        {doc.arquivo_url && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewDocument(doc)}
                            className="h-8 w-8 p-0"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveDocument(doc.id)}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Footer da modal */}
            <div className="flex justify-between items-center pt-4 border-t">
              <p className="text-sm text-gray-600">
                Os documentos foram analisados e estão disponíveis na lista principal.
                Você pode agora revisá-los e criar lançamentos manualmente.
              </p>
              <Button onClick={handleCloseAnalysisModal}>
                Fechar
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Estilos para os efeitos */}
      <style jsx global>{`
        .simple-button-container {
          position: relative;
        }

        .glow-effect {
          position: absolute;
          inset: -8px;
          border-radius: 50%;
          background: radial-gradient(circle at center, rgba(59, 130, 246, 0.3) 0%, rgba(59, 130, 246, 0) 70%);
          animation: pulse-subtle 2s infinite ease-in-out;
        }

        .simple-upload-icon {
          position: relative;
          width: 60px;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background: rgba(243, 244, 246, 1);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
          transition: all 0.3s ease;
        }

        .upload-icon-dragging {
          transform: scale(1.2);
          box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
          background: rgba(219, 234, 254, 0.9);
          animation: pulse-icon 1.5s infinite ease-in-out;
        }

        .upload-area-dragging {
          animation: pulse-border 1.5s infinite;
          background: rgba(239, 246, 255, 0.8) !important;
          border-color: rgba(59, 130, 246, 0.5) !important;
        }

        @keyframes pulse-icon {
          0%, 100% {
            transform: scale(1.2);
          }
          50% {
            transform: scale(1.25);
          }
        }

        .upload-progress-active {
          width: 70%;
          animation: progress-pulse 2s infinite;
        }

        /* Animações específicas da variante 13.1 */
        .slide-in-right {
          animation: slideInRight 0.3s forwards;
        }

        .slide-out-right {
          animation: slideOutRight 0.3s forwards;
        }

        @keyframes slideInRight {
          from {
            transform: translateX(30px);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }

        @keyframes slideOutRight {
          from {
            transform: translateX(0);
            opacity: 1;
          }
          to {
            transform: translateX(30px);
            opacity: 0;
          }
        }

        @keyframes pulse-subtle {
          0%, 100% {
            opacity: 0.5;
          }
          50% {
            opacity: 1;
          }
        }

        @keyframes pulse-border {
          0%, 100% {
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
          }
          50% {
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.4);
          }
        }

        @keyframes progress-pulse {
          0%, 100% {
            opacity: 0.9;
          }
          50% {
            opacity: 0.7;
          }
        }
      `}</style>
    </div>
  );
}