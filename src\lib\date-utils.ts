/**
 * Utility functions for handling dates consistently across the application
 */

import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

/**
 * Creates a date object from a string in YYYY-MM-DD format without timezone issues
 * This ensures that the date is interpreted as noon local time to avoid any timezone shifts
 *
 * @param dateString A date string in YYYY-MM-DD format
 * @returns A Date object set to noon local time on the specified date
 */
export function createDateFromISOString(dateString: string | null | undefined): Date | undefined {
  if (!dateString) return undefined;

  try {
    // If the date already includes time information, just parse it
    if (dateString.includes('T')) {
      const date = new Date(dateString);
      // Still set to noon to avoid timezone issues
      date.setHours(12, 0, 0, 0);
      return date;
    }

    // For YYYY-MM-DD format, parse the parts and create a date at noon local time
    const [year, month, day] = dateString.split('-').map(Number);

    // Validate the date parts
    if (isNaN(year) || isNaN(month) || isNaN(day)) {
      return undefined;
    }

    // Create a date at noon local time to avoid timezone issues
    // Month is 0-indexed in JavaScript Date
    return new Date(year, month - 1, day, 12, 0, 0);
  } catch (error) {
    
    return undefined;
  }
}

/**
 * Formats a date to YYYY-MM-DD format without timezone issues
 *
 * @param date A Date object
 * @returns A string in YYYY-MM-DD format
 */
export function formatDateToISOString(date: Date | null | undefined): string | null {
  if (!date) return null;

  try {
    const year = date.getFullYear();
    // Month is 0-indexed in JavaScript Date
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch (error) {
    
    return null;
  }
}

/**
 * Formats a date to the Brazilian format (DD/MM/YYYY) without timezone issues
 *
 * @param date A Date object or ISO string
 * @returns A string in DD/MM/YYYY format
 */
export function formatDateToBrazilian(date: Date | string | null | undefined): string {
  if (!date) return '';

  try {
    // Se for uma string no formato YYYY-MM-DD
    if (typeof date === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(date)) {
      // Extrair ano, mês e dia diretamente da string
      const [year, month, day] = date.split('-').map(Number);

      // Formatar diretamente sem criar objeto Date para evitar problemas de timezone
      return `${day.toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}/${year}`;
    }

    // Para outros formatos de string ou objetos Date
    let dateObject: Date;

    if (typeof date === 'string') {
      dateObject = createDateFromISOString(date) || new Date(date);
    } else {
      dateObject = date;
    }

    // Ensure the date is at noon to avoid timezone issues
    const adjustedDate = new Date(dateObject);
    adjustedDate.setHours(12, 0, 0, 0);

    return format(adjustedDate, 'dd/MM/yyyy', { locale: ptBR });
  } catch (error) {
    
    // Em caso de erro, tentar retornar a string original ou uma mensagem de erro
    return typeof date === 'string' ? date : 'Data inválida';
  }
}