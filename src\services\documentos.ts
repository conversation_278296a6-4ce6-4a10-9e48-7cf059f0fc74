import { Documento, EstatisticasDocumentos, StatusDocumento, DadosExtraidos, DocumentosAgrupados } from '@/types/documentos';
import { uploadAnexo, extrairPathDeUrl } from '@/lib/supabase';
import { LancamentoFormData } from '@/types/lancamento';

/**
 * Função para obter todos os documentos com filtro por status
 * @param status Status dos documentos a serem buscados
 * @param forceServiceRole Se true, força o uso do cliente de serviço
 * @returns Promise com a lista de documentos
 */
export async function getDocumentos(
  status?: StatusDocumento,
  forceServiceRole: boolean = true
): Promise<Documento[]> {
  try {
    // Construir a URL com os parâmetros
    let url = '/api/documentos';
    const params = new URLSearchParams();

    if (status) {
      params.append('status', status);
    }

    if (forceServiceRole) {
      params.append('forceServiceRole', 'true');
    }

    // Adicionar timestamp para evitar cache
    params.append('t', Date.now().toString());

    // Adicionar parâmetros à URL se houver algum
    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    // Fazer a requisição
    const response = await fetch(url, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Erro ao buscar documentos');
    }

    const data = await response.json();
    return data;
  } catch (error: any) {
    
    throw error;
  }
}

/**
 * Função para obter um documento específico por ID
 * @param id ID do documento
 * @param forceServiceRole Se true, força o uso do cliente de serviço
 * @returns Promise com o documento ou null se não encontrado
 */
export async function getDocumentoById(
  id: string,
  forceServiceRole: boolean = true
): Promise<Documento | null> {
  try {
    // Construir a URL com os parâmetros
    const params = new URLSearchParams();

    if (forceServiceRole) {
      params.append('forceServiceRole', 'true');
    }

    // Adicionar timestamp para evitar cache
    params.append('t', Date.now().toString());

    let url = `/api/documentos/${id}?${params.toString()}`;

    const response = await fetch(url, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      const errorData = await response.json();
      throw new Error(errorData.error || 'Erro ao buscar documento');
    }

    const data = await response.json();
    return data;
  } catch (error: any) {
    
    throw error;
  }
}

// Função para fazer upload de um documento
export async function uploadDocumento(
  file: File,
  userId: string
): Promise<Documento> {
  try {
    // Usar uma abordagem mais direta para o upload
    // Criar um FormData para enviar o arquivo e os metadados juntos
    const formData = new FormData();
    formData.append('file', file);
    formData.append('userId', userId);
    formData.append('fileName', file.name);
    formData.append('fileType', file.type);

    // Enviar para a API usando FormData
    const response = await fetch('/api/documentos/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      let errorMessage = 'Erro ao criar documento';
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
      } catch (e) {
        // Se não conseguir fazer parse do JSON, usar o texto da resposta
        errorMessage = await response.text() || `Erro ${response.status}: ${response.statusText}`;
      }
      throw new Error(errorMessage);
    }

    return await response.json();
  } catch (error: any) {
    throw error;
  }
}

// Função para gerar título descritivo baseado nos dados extraídos
function gerarTituloDescritivo(dadosExtraidos: DadosExtraidos): string {
  const fornecedor = dadosExtraidos.fornecedor;
  const valor = dadosExtraidos.valor_total;
  const tipo = dadosExtraidos.tipo_documento;
  const status = dadosExtraidos.status_pagamento;
  
  // Limpar nome do fornecedor
  const fornecedorLimpo = fornecedor 
    ? fornecedor.replace(/\b(LTDA|EIRELI|S\.A\.|SA|ME|EPP|COMÉRCIO|INDÚSTRIA|SERVIÇOS)\b/gi, '').trim()
    : '';
  
  // Formatar valor
  const valorFormatado = valor 
    ? new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(valor)
    : '';
  
  // Gerar título baseado no tipo de documento
  if (tipo === 'comprovante') {
    if (fornecedorLimpo && valor) {
      return `✅ Pagamento ${fornecedorLimpo} - ${valorFormatado} (PAGO)`;
    } else if (fornecedorLimpo) {
      return `✅ Comprovante ${fornecedorLimpo} (PAGO)`;
    } else if (valor) {
      return `✅ Comprovante de pagamento - ${valorFormatado} (PAGO)`;
    } else {
      return '✅ Comprovante de pagamento (PAGO)';
    }
  } else if (tipo === 'boleto') {
    if (fornecedorLimpo && valor) {
      const statusText = status === 'vencido' ? '⚠️ VENCIDO' : '📋 A PAGAR';
      return `${statusText === '⚠️ VENCIDO' ? '⚠️' : '📋'} Boleto ${fornecedorLimpo} - ${valorFormatado} (${statusText.replace('📋 ', '').replace('⚠️ ', '')})`;
    } else if (fornecedorLimpo) {
      return `📋 Boleto ${fornecedorLimpo}`;
    } else if (valor) {
      return `📋 Boleto - ${valorFormatado}`;
    } else {
      return '📋 Boleto de pagamento';
    }
  } else if (tipo === 'nota_fiscal') {
    if (fornecedorLimpo && valor) {
      return `🧾 Nota Fiscal ${fornecedorLimpo} - ${valorFormatado}`;
    } else if (fornecedorLimpo) {
      return `🧾 Nota Fiscal ${fornecedorLimpo}`;
    } else {
      return '🧾 Nota Fiscal';
    }
  } else {
    // Fallback para documentos sem tipo específico
    if (fornecedorLimpo && valor) {
      return `Pagamento ${fornecedorLimpo} - ${valorFormatado}`;
    } else if (fornecedorLimpo) {
      return `Pagamento ${fornecedorLimpo}`;
    } else if (valor) {
      return `Pagamento - ${valorFormatado}`;
    } else {
      return 'Documento financeiro';
    }
  }
}

// Função para atualizar status de pagamento do lançamento quando um comprovante for identificado
async function atualizarStatusPagamentoLancamento(
  lancamentoId: string,
  dadosExtraidos: DadosExtraidos
): Promise<void> {
  try {
    // Preparar dados para atualização
    const dadosAtualizacao: any = {};

    // Se for um comprovante de pagamento, marcar como pago
    if (dadosExtraidos.tipo_documento === 'comprovante') {
      dadosAtualizacao.status = 'Pago';
      
      // Se tiver data de vencimento, usar como data de pagamento
      if (dadosExtraidos.data_vencimento) {
        dadosAtualizacao.data_pagamento = dadosExtraidos.data_vencimento;
      } else {
        // Usar data atual como data de pagamento
        dadosAtualizacao.data_pagamento = new Date().toISOString().split('T')[0];
      }
    }

    // Fazer a requisição para atualizar o lançamento
    const response = await fetch(`/api/lancamentos/${lancamentoId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(dadosAtualizacao),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Erro ao atualizar status do lançamento');
    }
  } catch (error: any) {
    console.error('Erro ao atualizar status de pagamento do lançamento:', error);
    throw error;
  }
}

// Função para buscar documentos relacionados (mesmo fornecedor e valor similar)
async function buscarDocumentosRelacionados(
  dadosExtraidos: DadosExtraidos,
  documentoAtualId: string
): Promise<Documento[]> {
  try {
    if (!dadosExtraidos.fornecedor || !dadosExtraidos.valor_total) {
      return [];
    }

    // Buscar documentos com mesmo fornecedor e valor similar (±5%)
    const valorMin = dadosExtraidos.valor_total * 0.95;
    const valorMax = dadosExtraidos.valor_total * 1.05;

    const response = await fetch('/api/documentos/buscar-relacionados', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fornecedor: dadosExtraidos.fornecedor,
        valor_min: valorMin,
        valor_max: valorMax,
        excluir_id: documentoAtualId
      }),
    });

    if (response.ok) {
      const documentos = await response.json();
      return documentos || [];
    }

    return [];
  } catch (error) {
    console.warn('Erro ao buscar documentos relacionados:', error);
    return [];
  }
}

// Função para atualizar os dados extraídos de um documento
export async function atualizarDadosExtraidos(
  id: string,
  dadosExtraidos: DadosExtraidos
): Promise<Documento> {
  try {
    // Gerar título descritivo baseado nos dados extraídos
    const tituloDescritivo = gerarTituloDescritivo(dadosExtraidos);
    
    // Adicionar um timestamp para evitar cache
    const timestamp = new Date().getTime();



    // Garantir que estamos usando o cliente de serviço para atualizar os dados
    const response = await fetch(`/api/documentos/${id}?t=${timestamp}&forceServiceRole=true`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      body: JSON.stringify({ 
        dados_extraidos: dadosExtraidos,
        descricao: tituloDescritivo
      }),
    });

    if (!response.ok) {
      let errorMessage = 'Erro ao atualizar dados extraídos';
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
        // Error already handled
      } catch (e) {
        // Se não conseguir fazer parse do JSON, usar o texto da resposta
        const errorText = await response.text();
        errorMessage = errorText || `Erro ${response.status}: ${response.statusText}`;
      }
      throw new Error(errorMessage);
    }

    const data = await response.json();

    // NOVA LÓGICA: Detectar e vincular documentos relacionados
    if (dadosExtraidos.tipo_documento === 'comprovante') {
      try {
        // Buscar documentos relacionados (boletos com mesmo fornecedor/valor)
        const documentosRelacionados = await buscarDocumentosRelacionados(dadosExtraidos, id);
        
        // Se encontrou documentos relacionados, tentar vinculá-los
        for (const docRelacionado of documentosRelacionados) {
          // Se o documento relacionado ainda não tem lançamento, criar um grupo
          if (!docRelacionado.lancamento_id && docRelacionado.status === 'pendente') {
            console.log(`Comprovante ${id} relacionado ao documento ${docRelacionado.id}`);
            // Aqui podemos implementar lógica adicional de vinculação se necessário
          }
        }
      } catch (error) {
        console.warn('Erro ao processar documentos relacionados:', error);
      }
    }

    // Se for um comprovante de pagamento, tentar atualizar o status do lançamento relacionado
    if (dadosExtraidos.tipo_documento === 'comprovante' && data.lancamento_id) {
      try {
        await atualizarStatusPagamentoLancamento(data.lancamento_id, dadosExtraidos);
      } catch (error) {
        console.warn('Erro ao atualizar status de pagamento do lançamento:', error);
        // Não falhar a operação principal se não conseguir atualizar o status
      }
    }

    // Forçar uma pequena pausa para garantir que os dados sejam atualizados no banco
    await new Promise(resolve => setTimeout(resolve, 500));

    return data;
  } catch (error: any) {
    
    throw error;
  }
}

// Função para atualizar o contato_id de um documento
export async function atualizarContatoDocumento(
  id: string,
  contato_id: string | null
): Promise<Documento> {
  try {
    // Verificar se a coluna contato_id existe no banco de dados
    // Se não existir, apenas retornar o documento sem atualizar
    try {
      // Adicionar um timestamp para evitar cache
      const timestamp = new Date().getTime();

      // Buscar o documento atual
      const documentoAtual = await getDocumentoById(id);

      if (!documentoAtual) {
        throw new Error('Documento não encontrado');
      }

      // Adicionar o contato_id ao documento em memória
      const documentoAtualizado = {
        ...documentoAtual,
        contato_id: contato_id
      };

      // Tentar atualizar o documento no banco de dados
      // Se a coluna não existir, isso vai falhar, mas não vamos lançar erro
      try {
        const response = await fetch(`/api/documentos/${id}?t=${timestamp}&forceServiceRole=true`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          },
          body: JSON.stringify({ contato_id }),
        });

        if (response.ok) {
          const data = await response.json();
          return data;
        }
      } catch (updateError) {}

      // Se não conseguiu atualizar no banco, retornar o documento atualizado em memória
      return documentoAtualizado;
    } catch (error) {// Buscar o documento atual e retornar sem modificações
      const documento = await getDocumentoById(id);
      if (!documento) {
        throw new Error('Documento não encontrado');
      }
      return documento;
    }
  } catch (error: any) {
    
    throw error;
  }
}

// Função para aprovar um documento e criar um lançamento
export async function aprovarDocumento(
  id: string,
  dadosLancamento: LancamentoFormData
): Promise<Documento> {
  try {
    const response = await fetch(`/api/documentos/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        status: 'aprovado',
        lancamento: dadosLancamento
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Erro ao aprovar documento');
    }

    return await response.json();
  } catch (error: any) {
    
    throw error;
  }
}

// Função para rejeitar um documento
export async function rejeitarDocumento(
  id: string,
  motivo: string
): Promise<Documento> {
  try {
    const response = await fetch(`/api/documentos/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        status: 'rejeitado',
        motivo_rejeicao: motivo
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Erro ao rejeitar documento');
    }

    return await response.json();
  } catch (error: any) {
    
    throw error;
  }
}

// Função para salvar dados do documento sem alterar seu status
export async function salvarDocumento(
  id: string,
  dadosDocumento: any
): Promise<Documento> {
  try {
    // Adicionar um timestamp para evitar cache
    const timestamp = new Date().getTime();

    // Preparar os dados para atualização
    // Remover campos que não devem ser atualizados diretamente
    const { status, ...dadosParaAtualizar } = dadosDocumento;

    // Garantir que obra_id seja tratado corretamente
    // Se for undefined, null ou string vazia, definir explicitamente como null
    const obra_id = dadosParaAtualizar.obra_id === undefined ||
                   dadosParaAtualizar.obra_id === '' ||
                   dadosParaAtualizar.obra_id === null
                   ? null
                   : dadosParaAtualizar.obra_id;

    // Adicionar timestamp de atualização
    const dadosAtualizados = {
      ...dadosParaAtualizar,
      obra_id, // Garantir que obra_id seja explicitamente incluído
      updated_at: new Date().toISOString()
    };

    // Tentar várias abordagens para atualizar o campo obra_id
    if (obra_id !== undefined) {
      // Abordagem 1: Usar a API específica para atualizar apenas o campo obra_id
      try {

        const obraResponse = await fetch(`/api/documentos/atualizar-obra?id=${id}&obra_id=${obra_id}&t=${timestamp}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (obraResponse.ok) {

        } else {

// Abordagem 2: Tentar com SQL direto
          try {
  
            const sqlResponse = await fetch(`/api/documentos/atualizar-obra-sql?id=${id}&obra_id=${obra_id}&t=${timestamp}`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
              }
            });

            if (sqlResponse.ok) {

            } else {
              
            }
          } catch (sqlError) {
            
          }
        }
      } catch (obraError) {
        
      }
    }

    // Tentar atualizar o campo descricao separadamente
    if (dadosParaAtualizar.descricao !== undefined) {
      // Abordagem 1: Usar a API específica para atualizar apenas o campo descricao
      try {

        const descricaoResponse = await fetch(`/api/documentos/atualizar-descricao?id=${id}&descricao=${encodeURIComponent(dadosParaAtualizar.descricao)}&t=${timestamp}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (descricaoResponse.ok) {

        } else {

// Abordagem 2: Tentar com SQL direto
          try {
  
            const sqlResponse = await fetch(`/api/documentos/atualizar-descricao-sql?id=${id}&descricao=${encodeURIComponent(dadosParaAtualizar.descricao)}&t=${timestamp}`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
              }
            });

            if (sqlResponse.ok) {

            } else {
              
            }
          } catch (sqlError) {
            
          }
        }
      } catch (descricaoError) {
        
      }
    }

    // Continuar com a atualização normal para os outros campos
    const response = await fetch(`/api/documentos/${id}?t=${timestamp}&forceServiceRole=true`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      body: JSON.stringify(dadosAtualizados),
    });

    if (!response.ok) {
      let errorMessage = 'Erro ao salvar documento';
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
      } catch (e) {
        // Se não conseguir fazer parse do JSON, usar o texto da resposta
        const errorText = await response.text();
        
        errorMessage = errorText || `Erro ${response.status}: ${response.statusText}`;
      }
      throw new Error(errorMessage);
    }

    try {
      const data = await response.json();

      // Verificar se a atualização foi realmente aplicada
      try {
        const verifyResponse = await fetch(`/api/documentos/${id}?t=${Date.now()}&forceServiceRole=true`);
        if (verifyResponse.ok) {
          const verifyData = await verifyResponse.json();
  
        }
      } catch (verifyError) {
        
      }

      return data;
    } catch (jsonError) {
      
      throw new Error('Erro ao processar resposta do servidor após salvar documento');
    }
  } catch (error: any) {
    
    throw error;
  }
}

/**
 * Função para obter estatísticas dos documentos
 * @param forceServiceRole Se true, força o uso do cliente de serviço
 * @returns Promise com as estatísticas dos documentos
 */
export async function getEstatisticasDocumentos(forceServiceRole: boolean = true): Promise<EstatisticasDocumentos> {
  try {
    // Construir a URL com os parâmetros
    let url = '/api/documentos/estatisticas';

    if (forceServiceRole) {
      url += '?forceServiceRole=true';
    }

    const response = await fetch(url, {
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Erro ao buscar estatísticas');
    }

    return await response.json();
  } catch (error: any) {
    
    throw error;
  }
}

/**
 * Função para excluir um documento
 */
export async function excluirDocumento(id: string): Promise<void> {
  try {
    // Adicionar timestamp para evitar cache
    const timestamp = Date.now();

    const response = await fetch(`/api/documentos/${id}?t=${timestamp}`, {
      method: 'DELETE',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    if (!response.ok) {
      let errorMessage = 'Erro ao excluir documento';
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
        // Error already handled
      } catch (e) {
        // Se não conseguir fazer parse do JSON, usar o texto da resposta
        const errorText = await response.text();
        errorMessage = errorText || `Erro ${response.status}: ${response.statusText}`;
      }
      throw new Error(errorMessage);
    }

  } catch (error: any) {
    
    throw error;
  }
}

/**
 * Função para limpar documentos por status
 * @param status Status dos documentos a serem limpos ou 'all' para todos
 * @returns Promise com o resultado da operação
 */
export async function limparDocumentos(
  status: 'all' | StatusDocumento
): Promise<{ success: number; errors: number; total: number }> {
  try {
    // Adicionar timestamp para evitar cache
    const timestamp = Date.now();

    const response = await fetch(`/api/documentos/limpar?t=${timestamp}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
      body: JSON.stringify({ status })
    });

    if (!response.ok) {
      let errorMessage = 'Erro ao limpar documentos';
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
      } catch (e) {
        // Se não conseguir fazer parse do JSON, usar o texto da resposta
        const errorText = await response.text();
        errorMessage = errorText || `Erro ${response.status}: ${response.statusText}`;
      }
      throw new Error(errorMessage);
    }

    const result = await response.json();
    return result;
  } catch (error: any) {
    throw error;
  }
}

/**
 * Função para obter documentos agrupados por lançamento
 * @param status Status dos documentos a serem buscados
 * @param forceServiceRole Se true, força o uso do cliente de serviço
 * @returns Promise com a lista de documentos agrupados por lançamento
 */
export async function getDocumentosAgrupados(
  status?: StatusDocumento,
  forceServiceRole: boolean = true
): Promise<DocumentosAgrupados[]> {
  try {
    // Construir a URL com os parâmetros
    let url = '/api/documentos/agrupados';
    const params = new URLSearchParams();

    if (status) {
      params.append('status', status);
    }

    if (forceServiceRole) {
      params.append('forceServiceRole', 'true');
    }

    // Adicionar timestamp para evitar cache
    params.append('t', Date.now().toString());

    // Adicionar parâmetros à URL se houver algum
    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    // Fazer a requisição
    const response = await fetch(url, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Erro ao buscar documentos agrupados');
    }

    const data = await response.json();
    return data;
  } catch (error: any) {
    
    throw error;
  }
}

/**
 * Hook para usar o serviço de documentos
 * @returns Objeto com as funções do serviço
 */
export function useDocumentosService() {
  return {
    getDocumentos,
    getDocumentoById,
    uploadDocumento,
    atualizarDadosExtraidos,
    atualizarContatoDocumento,
    aprovarDocumento,
    rejeitarDocumento,
    salvarDocumento,
    excluirDocumento,
    getEstatisticasDocumentos,
    limparDocumentos,
    getDocumentosAgrupados
  };
}