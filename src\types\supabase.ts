export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      documento_anexos: {
        Row: {
          id: string
          documento_id: string
          nome: string
          arquivo_url: string
          arquivo_path: string
          tipo_arquivo: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          documento_id: string
          nome: string
          arquivo_url: string
          arquivo_path: string
          tipo_arquivo: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          documento_id?: string
          nome?: string
          arquivo_url?: string
          arquivo_path?: string
          tipo_arquivo?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "documento_anexos_documento_id_fkey"
            columns: ["documento_id"]
            isOneToOne: false
            referencedRelation: "documentos"
            referencedColumns: ["id"]
          }
        ]
      }
      documentos: {
        Row: {
          id: string
          nome: string
          arquivo_url: string
          arquivo_path: string
          tipo_arquivo: string
          status: string
          fornecedor: string | null
          valor_total: number | null
          data_vencimento: string | null
          motivo_rejeicao: string | null
          dados_extraidos: Json | null
          user_id: string
          lancamento_id: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          nome: string
          arquivo_url: string
          arquivo_path: string
          tipo_arquivo: string
          status: string
          fornecedor?: string | null
          valor_total?: number | null
          data_vencimento?: string | null
          motivo_rejeicao?: string | null
          dados_extraidos?: Json | null
          user_id: string
          lancamento_id?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          nome?: string
          arquivo_url?: string
          arquivo_path?: string
          tipo_arquivo?: string
          status?: string
          fornecedor?: string | null
          valor_total?: number | null
          data_vencimento?: string | null
          motivo_rejeicao?: string | null
          dados_extraidos?: Json | null
          user_id?: string
          lancamento_id?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "documentos_lancamento_id_fkey"
            columns: ["lancamento_id"]
            isOneToOne: false
            referencedRelation: "lancamentos"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documentos_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      lancamento_anexos: {
        Row: {
          id: string
          lancamento_id: string
          nome: string
          arquivo_url: string
          arquivo_path: string
          tipo_arquivo: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          lancamento_id: string
          nome: string
          arquivo_url: string
          arquivo_path: string
          tipo_arquivo: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          lancamento_id?: string
          nome?: string
          arquivo_url?: string
          arquivo_path?: string
          tipo_arquivo?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "lancamento_anexos_lancamento_id_fkey"
            columns: ["lancamento_id"]
            isOneToOne: false
            referencedRelation: "lancamentos"
            referencedColumns: ["id"]
          }
        ]
      }
      categorias: {
        Row: {
          created_at: string
          id: string
          nome: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          nome: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          nome?: string
          updated_at?: string
        }
        Relationships: []
      }
      contatos: {
        Row: {
          chave_pix: string | null
          cpf_cnpj: string | null
          created_at: string | null
          email: string | null
          empresa: string | null
          id: string
          nome_empresa: string
          nome_razao: string | null
          nome_contato: string | null
          observacoes: string | null
          telefone: string | null
          tipo: string
          tipo_pessoa: string | null
        }
        Insert: {
          chave_pix?: string | null
          cpf_cnpj?: string | null
          created_at?: string | null
          email?: string | null
          empresa?: string | null
          id?: string
          nome_empresa: string
          nome_razao?: string | null
          nome_contato?: string | null
          observacoes?: string | null
          telefone?: string | null
          tipo: string
          tipo_pessoa?: string | null
        }
        Update: {
          chave_pix?: string | null
          cpf_cnpj?: string | null
          created_at?: string | null
          email?: string | null
          empresa?: string | null
          id?: string
          nome_empresa?: string
          nome_razao?: string | null
          nome_contato?: string | null
          observacoes?: string | null
          telefone?: string | null
          tipo?: string
          tipo_pessoa?: string | null
        }
        Relationships: []
      }
      lancamentos: {
        Row: {
          categoria_id: string | null
          contato_id: string | null
          created_at: string | null
          data_competencia: string
          data_pagamento: string | null
          descricao: string
          forma_pagamento: Database["public"]["Enums"]["forma_pagamento"]
          id: string
          obra_id: string | null
          observacoes: string | null
          status: Database["public"]["Enums"]["status_lancamento"]
          tipo_lancamento: Database["public"]["Enums"]["tipo_lancamento"]
          updated_at: string | null
          valor_total: number
        }
        Insert: {
          categoria_id?: string | null
          contato_id?: string | null
          created_at?: string | null
          data_competencia: string
          data_pagamento?: string | null
          descricao: string
          forma_pagamento: Database["public"]["Enums"]["forma_pagamento"]
          id?: string
          obra_id?: string | null
          observacoes?: string | null
          status?: Database["public"]["Enums"]["status_lancamento"]
          tipo_lancamento: Database["public"]["Enums"]["tipo_lancamento"]
          updated_at?: string | null
          valor_total: number
        }
        Update: {
          categoria_id?: string | null
          contato_id?: string | null
          created_at?: string | null
          data_competencia?: string
          data_pagamento?: string | null
          descricao?: string
          forma_pagamento?: Database["public"]["Enums"]["forma_pagamento"]
          id?: string
          obra_id?: string | null
          observacoes?: string | null
          status?: Database["public"]["Enums"]["status_lancamento"]
          tipo_lancamento?: Database["public"]["Enums"]["tipo_lancamento"]
          updated_at?: string | null
          valor_total?: number
        }
        Relationships: [
          {
            foreignKeyName: "fk_lancamentos_categoria"
            columns: ["categoria_id"]
            isOneToOne: false
            referencedRelation: "categorias"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "lancamentos_categoria_id_fkey"
            columns: ["categoria_id"]
            isOneToOne: false
            referencedRelation: "categorias"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "lancamentos_contato_id_fkey"
            columns: ["contato_id"]
            isOneToOne: false
            referencedRelation: "contatos"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "lancamentos_obra_id_fkey"
            columns: ["obra_id"]
            isOneToOne: false
            referencedRelation: "obras"
            referencedColumns: ["id"]
          },
        ]
      }
      obras: {
        Row: {
          cliente_id: string | null
          created_at: string | null
          data_fim: string | null
          data_inicio: string | null
          descricao: string | null
          id: string
          nome: string
          orcamento_previsto: number | null
          status: string
        }
        Insert: {
          cliente_id?: string | null
          created_at?: string | null
          data_fim?: string | null
          data_inicio?: string | null
          descricao?: string | null
          id?: string
          nome: string
          orcamento_previsto?: number | null
          status?: string
        }
        Update: {
          cliente_id?: string | null
          created_at?: string | null
          data_fim?: string | null
          data_inicio?: string | null
          descricao?: string | null
          id?: string
          nome?: string
          orcamento_previsto?: number | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "obras_cliente_id_fkey"
            columns: ["cliente_id"]
            isOneToOne: false
            referencedRelation: "contatos"
            referencedColumns: ["id"]
          },
        ]
      }
      parcelas: {
        Row: {
          anexos: Json | null
          created_at: string | null
          data_pagamento: string | null
          id: string
          lancamento_id: string
          numero: number
          status: Database["public"]["Enums"]["status_parcela"]
          updated_at: string | null
          valor: number
          vencimento: string
        }
        Insert: {
          anexos?: Json | null
          created_at?: string | null
          data_pagamento?: string | null
          id?: string
          lancamento_id: string
          numero: number
          status?: Database["public"]["Enums"]["status_parcela"]
          updated_at?: string | null
          valor: number
          vencimento: string
        }
        Update: {
          anexos?: Json | null
          created_at?: string | null
          data_pagamento?: string | null
          id?: string
          lancamento_id?: string
          numero?: number
          status?: Database["public"]["Enums"]["status_parcela"]
          updated_at?: string | null
          valor?: number
          vencimento?: string
        }
        Relationships: [
          {
            foreignKeyName: "parcelas_lancamento_id_fkey"
            columns: ["lancamento_id"]
            isOneToOne: false
            referencedRelation: "lancamentos"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      criar_lancamento_com_parcelas: {
        Args: {
          p_descricao: string
          p_valor_total: number
          p_data_competencia: string
          p_forma_pagamento: string
          p_tipo_lancamento: string
          p_status: string
          p_observacoes: string
          p_obra_id: string
          p_contato_id: string
          p_categoria_id: string
          p_numero_parcelas: number
        }
        Returns: string
      }
    }
    Enums: {
      forma_pagamento:
        | "dinheiro"
        | "pix"
        | "cartao_credito"
        | "cartao_debito"
        | "cheque"
        | "transferencia"
        | "boleto"
      status_lancamento: "Em aberto" | "Pago" | "Cancelado"
      status_lancamento_old:
        | "pendente"
        | "parcialmente_pago"
        | "pago"
        | "cancelado"
      status_parcela: "pendente" | "pago" | "cancelado"
      tipo_lancamento: "receita" | "despesa"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      forma_pagamento: [
        "dinheiro",
        "pix",
        "cartao_credito",
        "cartao_debito",
        "cheque",
        "transferencia",
        "boleto",
      ],
      status_lancamento: ["Em aberto", "Pago", "Cancelado"],
      status_lancamento_old: [
        "pendente",
        "parcialmente_pago",
        "pago",
        "cancelado",
      ],
      status_parcela: ["pendente", "pago", "cancelado"],
      tipo_lancamento: ["receita", "despesa"],
    },
  },
} as const