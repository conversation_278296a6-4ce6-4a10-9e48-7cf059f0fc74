import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';
import { cookies } from 'next/headers';
import { createServiceClient } from '@/lib/supabase/service-client';
import { StatusDocumento } from '@/types/documentos';

// POST /api/documentos/limpar - Limpa documentos por status
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    // Obter o body da requisição
    const body = await request.json();
    const status = body.status as StatusDocumento | 'all';

    if (!status) {
      return NextResponse.json(
        { error: 'Status não fornecido' },
        { status: 400 }
      );
    }

    // Inicializar o cliente do Supabase
    const supabase = await getSupabaseRouteClient();

    // Verificar a sessão
    let session;
    try {
      const { data } = await supabase.auth.getSession();
      session = data.session;

      if (!session) {
        // Tentar obter o usuário como alternativa
        const { data: userData } = await supabase.auth.getUser();
        if (!userData.user) {
          return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }
      }
    } catch (authError) {
      return NextResponse.json({ error: 'Erro de autenticação' }, { status: 401 });
    }

    // Criar cliente de serviço para garantir permissões
    const serviceClient = createServiceClient();
    if (!serviceClient) {
      return NextResponse.json({ error: 'Erro ao criar cliente de serviço' }, { status: 500 });
    }

    // Buscar documentos a serem excluídos
    let query = serviceClient.from('documentos').select('id, arquivo_path');

    if (status !== 'all') {
      query = query.eq('status', status);
    }

    const { data: documentos, error: queryError } = await query;

    if (queryError) {
      return NextResponse.json({ error: queryError.message }, { status: 500 });
    }

    if (!documentos || documentos.length === 0) {
      return NextResponse.json({ message: 'Nenhum documento encontrado para excluir', count: 0 });
    }

    // Excluir arquivos do storage
    const arquivosParaExcluir = documentos
      .filter(doc => doc.arquivo_path)
      .map(doc => doc.arquivo_path);

    if (arquivosParaExcluir.length > 0) {
      try {
        const { error: storageError } = await serviceClient.storage
          .from('documentos')
          .remove(arquivosParaExcluir);

        // Continuar mesmo com erro no storage
      } catch (storageError) {
        // Continuar mesmo com erro no storage
      }
    }

    // Excluir documentos do banco de dados
    const idsParaExcluir = documentos.map(doc => doc.id);

    // Excluir em lotes de 10 para evitar problemas com limites de API
    const batchSize = 10;
    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < idsParaExcluir.length; i += batchSize) {
      const batch = idsParaExcluir.slice(i, i + batchSize);

      try {
        const { error: deleteError } = await serviceClient
          .from('documentos')
          .delete()
          .in('id', batch);

        if (deleteError) {
          errorCount += batch.length;
        } else {
          successCount += batch.length;
        }
      } catch (batchError) {
        errorCount += batch.length;
      }

      // Pequena pausa entre lotes para evitar sobrecarga
      if (i + batchSize < idsParaExcluir.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return NextResponse.json({
      message: `Documentos excluídos com sucesso: ${successCount}, falhas: ${errorCount}`,
      success: successCount,
      errors: errorCount,
      total: documentos.length
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Erro ao limpar documentos: ' + error.message },
      { status: 500 }
    );
  }
}