# Objetivo geral

O projeto tem como objetivo criar uma aplicação web para gestão financeira de obras, substituindo o controle atual feito em planilhas e mensagens de WhatsApp. A plataforma permitirá registrar e acompanhar receitas, despesas e contratos de forma centralizada por obra, com aprovação de lançamentos antes da contabilização. As principais funcionalidades incluem: menu de obras, dashboards financeiros (geral e por obra), cadastro de lançamentos manuais ou automáticos via WhatsApp (usando Evolution API), controle de contratos com terceiros (por medição), gestão de contatos (fornecedores e clientes), configurações da empresa e login de usuários com permissões básicas. Tudo será construído em React, com Supabase como backend e interface com shadcn/ui.

## **Negocio (Regras de Negócio e Fluxo das Telas)**

# Esta seção descreve as **regras de negócio, funcionalidades e a interação do usuário com as telas** da aplicação de gestão financeira de obras. Cobriremos cada item solicitado: menus, formulários de criação/edição, dashboards, aprovação de lançamentos e integração via WhatsApp (Evolution API). A linguagem é orientada a como o usuário e o sistema se comportam, garantindo clareza e objetividade.

### **Visão Geral e Acesso**

# - **Login de Usuários:** Ao acessar a aplicação, o usuário é apresentado a uma tela de login. O login requer email e senha (conforme cadastro prévio na plataforma). Somente usuários autenticados podem acessar as funcionalidades internas. O Supabase Auth cuidará da validação. Não há, por padrão, níveis de acesso diferenciados no módulo financeiro (todos os logados podem ver os dados e aprovar lançamentos), a não ser possivelmente para área de configurações mais sensíveis.

- **Após Login:** o usuário é direcionado para o **Dashboard Financeiro Geral da Empresa** (visão geral). A navegação principal (menu) estará disponível para acessar as seções: **Obras**, **Lançamentos**, **Contratos**, **Contatos** e **Configurações**. Essas seções podem ser acessadas via um menu lateral ou superior.

### **Menu de Obras e Gestão de Obras**

# - **Menu "Obras":** Lista todas as obras em andamento (e possivelmente opção de ver concluídas). Para cada obra listada, exibe informações resumidas, como nome da obra, cliente, status, e indicadores financeiros resumidos (por exemplo, saldo atual da obra, porcentagem do orçamento utilizado).

- O usuário pode selecionar uma obra da lista para ver detalhes (Dashboard individual da obra) ou criar uma nova obra.

- **Criação de Obra:** Ao clicar em “Nova Obra”, abre-se um formulário para inserir os dados da obra:
  - Nome da obra (obrigatório).
    - Deve ser campo texto
  - Descrição da obra (opcional).
    - Deve ser campo texto
  - Cliente 
    - Selecionar um contato do tipo cliente, ou ser possível cadastrar um novo de maneira rápida, opcional se não aplicável.
    - Para criação poderemos digitar o nome e incluir
    - Após o registro ser incluído poderemos editar o mesmo clicando em um botão para edição
  - Orçamento previsto (valor total previsto ou contratado para a obra, opcional mas importante para comparação financeira).
  - Data de início (opcional) e data de término prevista (opcional).
  - Status 
    - Em andamento
    - Planejada
    - Concluída

- Ao salvar, a nova obra passa a constar na lista e mostra um toaster de confirmação. 

- **Edição de Obra:** Na lista de obras ou na tela de detalhes da obra, deve haver a opção de editar. Permite atualizar campos como nome, datas, cliente, orçamento ou status. Não deve permitir mudança que quebre vínculos (ex: trocar cliente irá apenas atualizar referência; se obra for marcada “concluída”, pode-se exigir que não haja lançamentos pendentes etc. – essas validações podem ser consideradas).**Exclusão de Obra:** Opcionalmente, permitir deletar uma obra _apenas se_ não houver lançamentos ou contratos lançados (ou se for um erro de cadastro). Caso contrário, melhor desativar ou marcar como cancelada para não perder histórico.

### **Dashboard Financeiro Geral da Empresa**

- **Visão geral:** Esta é a tela inicial pós-login. Apresenta indicadores financeiros consolidados de todas as obras em andamento, dando ao usuário um panorama da situação financeira da empresa.

- **Obras em andamento:** Exibe um card ou linha por obra ativa, incluindo:

  - Nome da obra.

  - Saldo atual da obra (diferença entre entradas e saídas já realizadas na obra). Se positivo, indica caixa restante ou lucro até o momento; se negativo, indica gasto acima do recebido (possivelmente a empresa adiantou pagamentos).

  - Previsto vs. Realizado da obra: pode ser mostrado como R$X de R$Y gastos, ou uma barra de progresso indicando quanto do orçamento previsto já foi consumido. O _previsto_ nesse contexto pode ser o orçamento previsto da obra (campo orcamento\_previsto) ou, se não houver, o valor total contratado pelo cliente (soma dos lançamentos de tipo “construtora” previstos). O _realizado_ seria total pago (lançamentos aprovados e marcados como pagos) até o momento.

  - Pagamentos futuros: um indicativo do total de contas a pagar próximas (soma das despesas tipo “terceiros” pendentes) e receitas a receber próximas (soma das receitas tipo “construtora” pendentes), ou simplesmente um alerta se existem pagamentos vencendo em X dias.

- **Totais gerais:** Além do detalhamento por obra, o dashboard geral pode mostrar:

  - **Saldo geral da empresa:** soma de todos os saldos das obras ou, de forma mais simples, total de receitas realizadas menos despesas realizadas em todas as obras.

  - **Receitas totais vs Despesas totais:** valores agregados (realizados até agora) e possivelmente previstos agregados.

  - **Gráfico ou resumo temporal:** Opcionalmente, um gráfico de fluxo de caixa ao longo do tempo consolidado, ou distribuição de despesas por categoria.

- **Interatividade:** Elementos do dashboard geral (como o card de uma obra) são clicáveis para entrar no **Dashboard individual da obra** correspondente.

- **Atualização de dados:** Os valores exibidos devem refletir os lançamentos aprovados. Lançamentos pendentes de aprovação ou pagamentos futuros que ainda não ocorreram podem aparecer em indicadores de “a pagar/receber”, mas não contabilizados em saldos realizados até serem efetivados (pagos/recebidos).


### **Dashboard Financeiro Individual por Obra**

Quando o usuário seleciona uma obra (por exemplo, clicando no nome da obra no menu ou no dashboard geral), é apresentado o **dashboard detalhado daquela obra**:

- **Cabeçalho da Obra:** Mostra nome da obra, cliente (se houver), status, datas, e orçamento previsto. Pode também indicar percentual concluído em termos de tempo ou financeira (opcional).

- **Saldo Atual da Obra:** em destaque, o saldo atual = total de receitas recebidas (tipo construtora, aprovados e pagos) - total de despesas pagas (tipo terceiros). Esse saldo mostra se a obra está superavitária ou precisando de recursos.

- **Previsto vs Realizado:** Detalhado para a obra:

  - _Receitas:_ Mostra o total previsto de entrada (por exemplo, valor total do contrato com cliente) vs o valor já recebido (realizado). Se a obra tem um contrato principal registrado (via lançamentos de tipo construtora talvez já inseridos como previsto nas etapas), pode-se basear nisso. Caso contrário, pode usar orcamento\_previsto como previsto de receita e receitas realizadas de fato.
  - _Despesas:_ Se houver um orçamento de custos (pode ser derivado do orcamento\_previsto ou não), exibir total de despesas previstas vs despesas realizadas. Se a empresa tiver inserido categorias e orçamentos por categoria, poderia detalhar, mas nesta versão provavelmente apenas comparar contra orcamento\_previsto se ele representa custo esperado. Caso orcamento\_previsto represente margem ou receita, talvez ter um campo de orçamento de custo seria necessário; simplificando: podemos assumir orcamento\_previsto = custo previsto total, e comparar com despesas reais.
  - Essa comparação pode ser exibida como valores e/ou gráfico de barras de progresso.

- **Pagamentos Futuros:** Lista ou quadro destacando próximos pagamentos relacionados à obra:

  - Lista de **despesas a pagar** (lançamentos do tipo “terceiros” pendentes, ordenados por data de vencimento ascendente). Exibe para cada: descrição, fornecedor, valor da parcela, data de vencimento. Talvez limitar aos próximos 5 ou próximos 30 dias, com opção de ver todos.
  - Lista de **receitas a receber** (lançamentos do tipo “construtora” pendentes, por ex parcelas do cliente a vencer).
  - Itens vencidos (pendentes cujo vencimento já passou) podem ser destacados em vermelho para chamar atenção.

- **Resumo de Contratos:** Se a obra tem contratos de terceiros em andamento, apresentar um resumo:

  - Por exemplo, “Contrato Elétrica: 1/3 medições pagas, valor total R$X, próximo pagamento em \<data>”.
  - Pode ser uma seção listando contratos ativos com status breve (dando opção de ver detalhes na tela de contratos).

- **Lista de Lançamentos da Obra:** Abaixo dos indicadores, mostrar um extrato ou tabela de todos os lançamentos da obra (ou pelo menos os mais recentes):

  - Colunas: Data do lançamento, Descrição, Tipo (Construtora/Terceiros), Valor (positivo para receita, negativo para despesa ou indicado separadamente), Status (aprovado/pendente, pago/pendente).
  - Permitir filtrar ou segmentar: por tipo (entradas/saídas), por aprovados/pendentes, por período.
  - O usuário pode clicar em um lançamento para editar/detalhar (se ainda não aprovado, ou mesmo ver detalhes se aprovado).

- **Ações na tela da obra:** Botões claros para **Novo Lançamento** (já associando à obra atual), **Novo Contrato** (para adicionar um contrato de terceiro nessa obra), e talvez **Relatório** (exportar dados da obra, se necessário).

**Atualização em tempo real:** Idealmente, se um lançamento é aprovado ou um pagamento é marcado como efetuado em alguma tela, os indicadores do dashboard da obra refletem imediatamente (podemos usar recursos de reatividade do Supabase, como assinaturas realtime, para atualizar sem reload).

### **Menu de Configurações (Gerais e da Empresa)**

### - **Menu "Configurações":** Destinado a configurações gerais da aplicação e informações da empresa usuária. Itens que podem constar:

  - **Dados da Empresa:** Campos como nome da empresa (construtora), CNPJ, endereço, telefone – caso queiram armazenar para uso interno ou em relatórios. Esses dados podem ser usados futuramente para emissão de relatórios ou integração com outros sistemas.

  - **Usuários:** Se houver necessidade de gerenciar usuários (convidar novo usuário, remover). Isso depende se haverá múltiplos usuários além do administrador. Poderia listar os usuários com acesso e permitir adicionar (enviar convite de cadastro) ou remover. (Se o sistema for single-tenant mas multi-usuário, possivelmente sim).

  - **Categorias e Plano de Contas:** Se implementado o módulo de categorias de lançamento, nas configurações pode haver a gestão dessas categorias: criar/editar/remover categorias de despesa/receita que serão usadas nos lançamentos.

### **Tabela Lancamentos (Financial Entries)**

Registra cada **lançamento financeiro** (entrada ou saída de dinheiro) da empresa, vinculado a uma obra. Este é o núcleo financeiro do sistema. Campos conforme sugerido, incluindo aprovação e possíveis parcelas:

- **id** (UUID) – Identificador do lançamento (chave primária).
- **obra\_id** (UUID, fk Obras) – Referência à obra relacionada ao lançamento.
- **tipo\_lancamento** (Texto) – Tipo do lançamento: “construtora” ou “terceiros”.
  - _Construtora_ indica entradas ou receitas ligadas à construtora (por ex, parcelas pagas pelo cliente da obra, etapas do contrato principal da obra).
  - _Terceiros_ indica saídas ou despesas pagas a terceiros (fornecedores, prestadores).
- **valor\_total** (Decimal) – Valor total do lançamento. É a soma de todas as parcelas (por exemplo, R$1000 total em 4 parcelas de R$250).
- **data\_competencia** (Data) – Data do lançamento financeiro (data em que foi registrado ou efetivado o gasto/receita). Por exemplo, data da compra ou da emissão da nota.
- **forma\_pagamento** (Texto) – Forma de pagamento utilizada. Exemplos: “Transferência bancária”, “PIX”, “Boleto”, “Cartão de crédito”, etc. Este campo pode aceitar textos livres ou ser padronizado por tipo.
- **descricao** (Texto) – Descrição do lançamento, detalhando do que se trata. Ex: “Compra de telhas metálicas”, “Pagamento 2ª medição elétrica”, “Receita etapa fundação”.
- **contato\_id** (UUID, fk Contatos) – Referência ao contato **fornecedor ou cliente** relacionado a este lançamento:
  - Se tipo\_lancamento = “terceiros” (despesa), normalmente aqui estará o fornecedor/prestador que recebeu o pagamento.
  - Se tipo\_lancamento = “construtora” (receita), aqui pode estar o cliente que efetuou o pagamento (geralmente o cliente da obra). Caso a obra tenha um cliente único já associado, este campo é opcional.
- **observacoes** (Texto) – Campo livre para observações adicionais sobre este lançamento (ex: detalhes da nota fiscal, parcelamentos específicos, quaisquer informações relevantes).
- **status** (Texto) – Status do pagamento efetivo: “Em aberto” (a pagar/receber), “Concluído” (já pago ou recebido), ou “Cancelado”.
- **created\_at** (Timestamp) – Data/hora de criação do lançamento (inclusão no sistema).


### **Tabela Parcelas**

### Registra cada **parcela** associada a um lançamento financeiro, permitindo controlar vencimentos e status individualmente.* **id** (UUID) – Identificador da parcela (chave primária).

* **lancamento\_id** (UUID, fk Lançamentos) – Referência ao lançamento financeiro ao qual esta parcela pertence.
* **numero** (Inteiro) – Número sequencial da parcela (1, 2, 3, …).
* **valor** (Decimal) – Valor monetário desta parcela.
* **vencimento** (Data) – Data de vencimento desta parcela.
* **status** (Texto) – Estado atual: “pendente”, “pago” ou “vencido”.
* **data\_pagamento** (Data, opcional) – Data em que esta parcela foi efetivamente paga.
* **created\_at** (Timestamp) – Data/hora de criação deste registro de parcela.
