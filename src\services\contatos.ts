'use client';

import { Database } from "@/types/supabase";
import { supabase } from '@/lib/supabase/client';
import { Contato, CreateContatoDTO, UpdateContatoDTO } from "@/types/contatos";

// Este tipo é o que vem diretamente do Supabase
type SupabaseContato = Database["public"]["Tables"]["contatos"]["Row"];

export function useContatosService() {
  // Função para converter o contato do Supabase para o formato do frontend
  function mapSupabaseContatoToContato(contatoDb: SupabaseContato): Contato {
    return {
      id: contatoDb.id,
      nome_empresa: contatoDb.nome_empresa || '', // Após renomeação da coluna
      nome_razao: contatoDb.nome_razao,
      nome_contato: contatoDb.nome_contato,
      email: contatoDb.email || undefined,
      telefone: contatoDb.telefone || undefined,
      tipo: contatoDb.tipo as 'fornecedor' | 'prestador' | 'cliente',
      tipo_pessoa: contatoDb.tipo_pessoa as 'fisica' | 'juridica' | null,
      cpf_cnpj: contatoDb.cpf_cnpj,
      created_at: contatoDb.created_at || undefined,
      chave_pix: contatoDb.chave_pix
    };
  }

  // Função para converter o contato do frontend para o formato do Supabase (para criação/atualização)
  function mapContatoToSupabaseContato(contato: CreateContatoDTO | UpdateContatoDTO): any {
    return {
      ...contato
    };
  }

  return {
    async listarContatos(): Promise<Contato[]> {
      const { data, error } = await supabase
        .from('contatos')
        .select('*')
        .order('nome_empresa');

      if (error) {
        
        throw error;
      }

      // Mapeando os resultados para o formato esperado pelo frontend
      return (data || []).map(mapSupabaseContatoToContato);
    },

    async buscarContatoPorId(id: string): Promise<Contato | null> {
      // Validar o ID antes de fazer a consulta
      if (!id || id === 'desconhecido') {
        return null;
      }

      try {
        const { data, error } = await supabase
          .from('contatos')
          .select('*')
          .eq('id', id)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            return null;
          }
          throw error;
        }

        return data ? mapSupabaseContatoToContato(data) : null;
      } catch (error) {
        return null;
      }
    },

    async criarContato(contato: CreateContatoDTO): Promise<Contato | null> {
      const supabaseContato = mapContatoToSupabaseContato(contato);

      const { data, error } = await supabase
        .from('contatos')
        .insert(supabaseContato)
        .select()
        .single();

      if (error) {
        
        throw error;
      }

      return data ? mapSupabaseContatoToContato(data) : null;
    },

    async atualizarContato(id: string, contato: UpdateContatoDTO): Promise<Contato | null> {
      const supabaseContato = mapContatoToSupabaseContato(contato);

      const { data, error } = await supabase
        .from('contatos')
        .update(supabaseContato)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        
        throw error;
      }

      return data ? mapSupabaseContatoToContato(data) : null;
    },

    async excluirContato(id: string): Promise<void> {
      const { error } = await supabase
        .from('contatos')
        .delete()
        .eq('id', id);

      if (error) {
        
        throw error;
      }
    },

    async buscarContatoPorNome(nome: string): Promise<Contato | null> {
      if (!nome || nome.trim() === '') {
        return null;
      }

      try {
        // Normalizar o nome para busca (remover acentos, converter para minúsculas)
        const nomeNormalizado = nome.toLowerCase().trim();

        // Buscar contatos
        const { data, error } = await supabase
          .from('contatos')
          .select('*');

        if (error) {
          
          return null;
        }

        if (!data || data.length === 0) {
          return null;
        }

        // Função para calcular a similaridade entre duas strings (0-1)
        const calcularSimilaridade = (a: string, b: string): number => {
          if (!a || !b) return 0;
          a = a.toLowerCase().trim();
          b = b.toLowerCase().trim();

          // Verificar se uma string contém a outra
          if (a.includes(b) || b.includes(a)) {
            // Quanto mais próximos os tamanhos, maior a similaridade
            const maxLength = Math.max(a.length, b.length);
            const minLength = Math.min(a.length, b.length);
            return minLength / maxLength;
          }

          // Verificar palavras em comum
          const palavrasA = a.split(/\s+/);
          const palavrasB = b.split(/\s+/);

          let palavrasComuns = 0;
          for (const palavraA of palavrasA) {
            if (palavraA.length <= 2) continue; // Ignorar palavras muito curtas
            for (const palavraB of palavrasB) {
              if (palavraB.length <= 2) continue;
              if (palavraA.includes(palavraB) || palavraB.includes(palavraA)) {
                palavrasComuns++;
                break;
              }
            }
          }

          // Calcular similaridade baseada em palavras comuns
          const totalPalavras = Math.max(
            palavrasA.filter(p => p.length > 2).length,
            palavrasB.filter(p => p.length > 2).length
          );

          return totalPalavras > 0 ? palavrasComuns / totalPalavras : 0;
        };

        // Encontrar o contato mais similar
        let melhorContato = null;
        let melhorSimilaridade = 0;

        for (const contato of data) {
          // Verificar similaridade com nome_empresa, nome_razao e nome_contato
          const similaridadeEmpresa = calcularSimilaridade(contato.nome_empresa || '', nomeNormalizado);
          const similaridadeRazao = calcularSimilaridade(contato.nome_razao || '', nomeNormalizado);
          const similaridadeContato = calcularSimilaridade(contato.nome_contato || '', nomeNormalizado);

          // Usar a maior similaridade encontrada
          const similaridade = Math.max(similaridadeEmpresa, similaridadeRazao, similaridadeContato);

          // Se a similaridade for alta o suficiente e melhor que a anterior
          if (similaridade > 0.5 && similaridade > melhorSimilaridade) {
            melhorSimilaridade = similaridade;
            melhorContato = contato;
          }
        }

        return melhorContato ? mapSupabaseContatoToContato(melhorContato) : null;
      } catch (error) {
        
        return null;
      }
    }
  };
}