export interface User {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
  role: 'admin' | 'user';
}

export interface Company {
  id: string;
  name: string;
  cnpj: string;
  address?: string;
  phone?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Construction {
  id: string;
  name: string;
  description?: string;
  clientId?: string;
  budget: number;
  startDate: Date;
  expectedEndDate?: Date;
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

export interface Transaction {
  id: string;
  constructionId: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  category: string;
  date: Date;
  dueDate: Date;
  status: 'pending_approval' | 'approved' | 'rejected' | 'paid';
  paymentType: 'construtora' | 'terceiros';
  attachments?: string[];
  approvedBy?: string;
  approvedAt?: Date;
  paidAt?: Date;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Contract {
  id: string;
  constructionId: string;
  contractorId: string; // ID do fornecedor/terceiro
  description: string;
  totalValue: number;
  numberOfPayments: number;
  startDate: Date;
  endDate?: Date;
  status: 'active' | 'completed' | 'cancelled';
  measurements: Measurement[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Measurement {
  id: string;
  contractId: string;
  description: string;
  value: number;
  date: Date;
  status: 'pending' | 'approved' | 'rejected' | 'paid';
  attachments?: string[];
  approvedBy?: string;
  approvedAt?: Date;
  paidAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Contact {
  id: string;
  name: string;
  type: 'client' | 'contractor';
  cnpjCpf?: string;
  email?: string;
  phone?: string;
  address?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Category {
  id: string;
  name: string;
  type: 'income' | 'expense';
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}