import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';
import { cookies } from 'next/headers';
import { createServiceClient } from '@/lib/supabase/service-client';
import { verificarAutenticacao } from '@/lib/auth-helpers';

// GET /api/documentos/anexos - Obter anexos de um documento
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Inicializar o cliente do Supabase
    const supabase = await getSupabaseRouteClient();

    // Verificar a autenticação
    const auth = await verificarAutenticacao(supabase);
    if (!auth.autenticado) {
      return auth.resposta || NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Obter o ID do documento da query string
    const { searchParams } = new URL(request.url);
    const documentoId = searchParams.get('documentoId');

    if (!documentoId) {
      return NextResponse.json({ error: 'ID do documento não fornecido' }, { status: 400 });
    }

    // Criar cliente de serviço para contornar RLS
    const serviceClient = createServiceClient();
    if (!serviceClient) {
      return NextResponse.json({ error: 'Erro ao criar cliente de serviço' }, { status: 500 });
    }

    // Buscar anexos do documento
    const { data, error } = await serviceClient
      .from('documento_anexos')
      .select('*')
      .eq('documento_id', documentoId)
      .order('created_at', { ascending: false });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Erro interno do servidor: ' + error.message },
      { status: 500 }
    );
  }
}