.file-preview-image-container {
  max-height: 100%;
  max-width: 100%;
  transition: transform 0.2s ease-in-out;
}

.file-preview-image {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
}

/* Classes para transformação dinâmica */
.transform-scale-100.transform-rotate-0 { transform: scale(1) rotate(0deg); }
.transform-scale-100.transform-rotate-90 { transform: scale(1) rotate(90deg); }
.transform-scale-100.transform-rotate-180 { transform: scale(1) rotate(180deg); }
.transform-scale-100.transform-rotate-270 { transform: scale(1) rotate(270deg); }

.transform-scale-125.transform-rotate-0 { transform: scale(1.25) rotate(0deg); }
.transform-scale-125.transform-rotate-90 { transform: scale(1.25) rotate(90deg); }
.transform-scale-125.transform-rotate-180 { transform: scale(1.25) rotate(180deg); }
.transform-scale-125.transform-rotate-270 { transform: scale(1.25) rotate(270deg); }

.transform-scale-150.transform-rotate-0 { transform: scale(1.5) rotate(0deg); }
.transform-scale-150.transform-rotate-90 { transform: scale(1.5) rotate(90deg); }
.transform-scale-150.transform-rotate-180 { transform: scale(1.5) rotate(180deg); }
.transform-scale-150.transform-rotate-270 { transform: scale(1.5) rotate(270deg); }

.transform-scale-175.transform-rotate-0 { transform: scale(1.75) rotate(0deg); }
.transform-scale-175.transform-rotate-90 { transform: scale(1.75) rotate(90deg); }
.transform-scale-175.transform-rotate-180 { transform: scale(1.75) rotate(180deg); }
.transform-scale-175.transform-rotate-270 { transform: scale(1.75) rotate(270deg); }

.transform-scale-200.transform-rotate-0 { transform: scale(2) rotate(0deg); }
.transform-scale-200.transform-rotate-90 { transform: scale(2) rotate(90deg); }
.transform-scale-200.transform-rotate-180 { transform: scale(2) rotate(180deg); }
.transform-scale-200.transform-rotate-270 { transform: scale(2) rotate(270deg); }

.transform-scale-225.transform-rotate-0 { transform: scale(2.25) rotate(0deg); }
.transform-scale-225.transform-rotate-90 { transform: scale(2.25) rotate(90deg); }
.transform-scale-225.transform-rotate-180 { transform: scale(2.25) rotate(180deg); }
.transform-scale-225.transform-rotate-270 { transform: scale(2.25) rotate(270deg); }

.transform-scale-250.transform-rotate-0 { transform: scale(2.5) rotate(0deg); }
.transform-scale-250.transform-rotate-90 { transform: scale(2.5) rotate(90deg); }
.transform-scale-250.transform-rotate-180 { transform: scale(2.5) rotate(180deg); }
.transform-scale-250.transform-rotate-270 { transform: scale(2.5) rotate(270deg); }

.transform-scale-275.transform-rotate-0 { transform: scale(2.75) rotate(0deg); }
.transform-scale-275.transform-rotate-90 { transform: scale(2.75) rotate(90deg); }
.transform-scale-275.transform-rotate-180 { transform: scale(2.75) rotate(180deg); }
.transform-scale-275.transform-rotate-270 { transform: scale(2.75) rotate(270deg); }

.transform-scale-300.transform-rotate-0 { transform: scale(3) rotate(0deg); }
.transform-scale-300.transform-rotate-90 { transform: scale(3) rotate(90deg); }
.transform-scale-300.transform-rotate-180 { transform: scale(3) rotate(180deg); }
.transform-scale-300.transform-rotate-270 { transform: scale(3) rotate(270deg); }

.transform-scale-50.transform-rotate-0 { transform: scale(0.5) rotate(0deg); }
.transform-scale-50.transform-rotate-90 { transform: scale(0.5) rotate(90deg); }
.transform-scale-50.transform-rotate-180 { transform: scale(0.5) rotate(180deg); }
.transform-scale-50.transform-rotate-270 { transform: scale(0.5) rotate(270deg); }

.transform-scale-75.transform-rotate-0 { transform: scale(0.75) rotate(0deg); }
.transform-scale-75.transform-rotate-90 { transform: scale(0.75) rotate(90deg); }
.transform-scale-75.transform-rotate-180 { transform: scale(0.75) rotate(180deg); }
.transform-scale-75.transform-rotate-270 { transform: scale(0.75) rotate(270deg); }
