import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

// POST /api/documentos/buscar-relacionados - Buscar documentos relacionados
export async function POST(request: NextRequest) {
  try {
    // Importar o cliente de serviço
    const { createServiceClient } = await import('@/lib/supabase/service-client');

    // Usar o cliente de serviço para ignorar as políticas RLS
    const supabase = createServiceClient();

    if (!supabase) {
      return NextResponse.json({ error: 'Erro ao criar cliente de serviço' }, { status: 500 });
    }

    // Obter os parâmetros da requisição
    const { fornecedor, valor_min, valor_max, excluir_id } = await request.json();

    if (!fornecedor || !valor_min || !valor_max) {
      return NextResponse.json(
        { error: 'Parâmetros obrigatórios: fornecedor, valor_min, valor_max' },
        { status: 400 }
      );
    }

    // Buscar documentos com critérios similares
    let query = supabase
      .from('documentos')
      .select(`
        id,
        nome,
        arquivo_url,
        arquivo_path,
        tipo_arquivo,
        status,
        fornecedor,
        valor_total,
        data_vencimento,
        motivo_rejeicao,
        dados_extraidos,
        user_id,
        lancamento_id,
        contato_id,
        obra_id,
        descricao,
        created_at,
        updated_at
      `)
      .eq('status', 'pendente') // Apenas documentos pendentes
      .gte('valor_total', valor_min)
      .lte('valor_total', valor_max);

    // Buscar por fornecedor nos dados extraídos ou no campo fornecedor
    query = query.or(`fornecedor.ilike.%${fornecedor}%,dados_extraidos->>fornecedor.ilike.%${fornecedor}%`);

    // Excluir o documento atual da busca
    if (excluir_id) {
      query = query.neq('id', excluir_id);
    }

    const { data: documentos, error } = await query.limit(10);

    if (error) {
      console.error('Erro ao buscar documentos relacionados:', error);
      return NextResponse.json({ error: 'Erro ao buscar documentos relacionados' }, { status: 500 });
    }

    return NextResponse.json(documentos || []);
  } catch (error: any) {
    console.error('Erro interno:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor: ' + error.message },
      { status: 500 }
    );
  }
} 