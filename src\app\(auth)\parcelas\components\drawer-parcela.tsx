import React, { useState, useEffect, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ooter,
} from '@/components/ui/sheet';
import { Input } from '@/components/ui/input';
import { DatePicker } from '@/components/ui/date-picker';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Dropzone } from '@/components/ui/dropzone';
import { Label } from '@/components/ui/label';
import { uploadAnexo, getSignedUrlAnexo, extrairPathDeUrl } from '@/lib/supabase';
import { useParcelasService } from '@/services/parcelas';
import { toast } from 'sonner';
import { Check, Trash2, Upload, ExternalLink, Edit, RefreshCcw } from 'lucide-react';
import Link from 'next/link';
import { Database } from '@/types/supabase';
import { Di<PERSON>, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { DrawerContato } from '@/app/(auth)/contatos/components/drawer-contato';
import { formatCurrency } from '@/lib/utils';

interface DrawerParcelaProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  parcela: any | null;
  index: number;
  total: number;
  onSubmit?: () => void;
}

export function DrawerParcela({ open, onOpenChange, parcela, index, total, onSubmit }: DrawerParcelaProps) {
  // Usamos uma string para o valor de entrada em vez de número para evitar NaN
  const [valor, setValor] = useState<string>('');

  // Função auxiliar para lidar com datas ISO sem problemas de fuso horário
  const normalizarData = (dataStr?: string): Date | undefined => {
    if (!dataStr) return undefined;

    // Dividir a data em partes (assumindo formato YYYY-MM-DD)
    const partes = dataStr.split('T')[0].split('-');
    if (partes.length !== 3) return undefined;

    // Criar uma nova data preservando o dia exato sem ajustes de fuso horário
    // Adicionamos 12 horas (meio-dia) para garantir que a data não seja afetada por fuso horário
    const ano = parseInt(partes[0], 10);
    const mes = parseInt(partes[1], 10) - 1; // O mês em JS é baseado em zero (0-11)
    const dia = parseInt(partes[2], 10);

    // Criar data no horário 12:00 (meio-dia) para evitar problemas com mudanças de dia devido ao fuso
    return new Date(ano, mes, dia, 12, 0, 0);
  };

  const [vencimento, setVencimento] = useState<Date | undefined>(normalizarData(parcela?.vencimento));
  const [status, setStatus] = useState<Database["public"]["Enums"]["status_parcela"]>(parcela?.status || 'pendente');
  const [dataPagamento, setDataPagamento] = useState<Date | undefined>(normalizarData(parcela?.data_pagamento));
  const [observacao, setObservacao] = useState<string>(parcela?.observacao || '');
  const [loading, setLoading] = useState(false);
  const { updateParcela } = useParcelasService();
  const [anexos, setAnexos] = useState<any[]>(parcela?.anexos || []);
  const [novosArquivos, setNovosArquivos] = useState<File[]>([]);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewType, setPreviewType] = useState<string | null>(null);
  const [drawerContatoOpen, setDrawerContatoOpen] = useState(false);
  const [contatoId, setContatoId] = useState<string | null>(null);

  useEffect(() => {
    // Verificar se parcela.valor existe e é válido
    if (parcela && parcela.valor !== undefined && parcela.valor !== null) {
      // Converter para string garantindo que seja um valor válido
      try {

        // Se for número, usar diretamente
        if (typeof parcela.valor === 'number') {
          // Vamos garantir que o número seja tratado corretamente
          setValor(parcela.valor.toString());

        } else {
          // Se for string ou outro tipo, tentar converter para número
          // Remover qualquer caracter não numérico exceto ponto e vírgula
          const valorStr = String(parcela.valor).replace(/[^\d.,]/g, '').replace(',', '.');
          const valorNum = parseFloat(valorStr);

          if (!isNaN(valorNum)) {
            setValor(valorNum.toString());
          } else {
            setValor('0');
          }
        }
      } catch (error) {
        
        setValor('0');
      }
    } else {
      setValor('0');
    }

    setVencimento(normalizarData(parcela?.vencimento));
    setStatus(parcela?.status || 'pendente');
    setDataPagamento(normalizarData(parcela?.data_pagamento));
    setObservacao(parcela?.observacao || '');
    setAnexos(parcela?.anexos || []);
    setNovosArquivos([]);
  }, [parcela, open]);

  // Formatação do valor para exibição
  const formatarValorParaExibicao = (valor: string): string => {
    try {
      // Limpa o valor para garantir que só fique com números e vírgula
      const valorLimpo = valor.replace(/[^\d,.-]/g, '').replace(',', '.');
      const numero = parseFloat(valorLimpo);

      if (isNaN(numero)) {
        return '';
      }

      // Garantir precisão de 2 casas decimais para consistência
      const valorArredondado = parseFloat(numero.toFixed(2));

      // Formata como moeda brasileira: R$ 1.234,56
      return formatCurrency(valorArredondado);
    } catch (error) {
      return valor; // Retorna o valor original em caso de erro
    }
  };

  // Tratamento de mudança no campo de valor
  const handleValorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Removemos todos os caracteres não numéricos para salvar apenas os números
    const apenasNumeros = inputValue.replace(/[^\d]/g, '');

    // Converte para valor em centavos (para lidar com decimais corretamente)
    const valorEmCentavos = parseInt(apenasNumeros) || 0;

    // Converte de centavos para reais (divide por 100 para obter os decimais)
    const valorEmReais = valorEmCentavos / 100;

    // Salva como string para preservar a precisão
    setValor(valorEmReais.toString());

  };

  // Formatar o valor para exibição no input
  const valorFormatadoParaInput = useMemo(() => {
    try {
      const valorNumerico = parseFloat(valor);
      if (isNaN(valorNumerico)) return '';

      // Formatar como moeda brasileira, mas sem o símbolo R$
      return valorNumerico.toLocaleString('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    } catch (error) {
      return valor;
    }
  }, [valor]);

  // Determinar se a parcela está atrasada para exibição visual
  const isAtrasada = status === 'pendente' && vencimento && vencimento < new Date() && vencimento.setHours(0, 0, 0, 0) < new Date().setHours(0, 0, 0, 0);

  const badgeStatus = status === 'pago'
    ? { text: 'Pago', color: 'bg-green-600 text-white' }
    : status === 'cancelado'
      ? { text: 'Cancelado', color: 'bg-gray-600 text-white' }
      : isAtrasada
        ? { text: 'Pendente (Atrasada)', color: 'bg-red-600 text-white hover:bg-red-700' }
        : { text: 'Pendente', color: 'bg-yellow-500 text-yellow-900' };

  const handleMarcarComoPago = () => {
    setStatus('pago');
    // Criar a data atual usando meio-dia para evitar problemas de fuso horário
    const dataAtual = new Date();
    const dataMeioDia = new Date(
      dataAtual.getFullYear(),
      dataAtual.getMonth(),
      dataAtual.getDate(),
      12, 0, 0
    );
    setDataPagamento(dataMeioDia);
  };
  const handleEstornar = () => {
    setStatus('pendente');
    setDataPagamento(undefined);
  };

  const handleRemoveNovoArquivo = (idx: number) => {
    setNovosArquivos(prev => prev.filter((_, i) => i !== idx));
  };
  const handleRemoveAnexoSalvo = (idx: number) => {
    setAnexos(prev => prev.filter((_, i) => i !== idx));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Converter o valor para número com validação adequada
    const converterParaNumero = (valorStr: string): number | null => {
      try {
        // Remover formatação monetária: símbolos, pontos e trocar vírgula por ponto
        const valorLimpo = valorStr.replace(/[^\d,.-]/g, '').replace(',', '.');
        const numero = parseFloat(valorLimpo);

        // Importante: garantir exatamente 2 casas decimais para evitar problemas de precisão
        // Isso é crítico para manter consistência com validações no backend
        return isNaN(numero) ? null : parseFloat(numero.toFixed(2));
      } catch (error) {
        
        return null;
      }
    };

    // Validar o valor
    const valorNumerico = converterParaNumero(valor);

    if (valorNumerico === null || valorNumerico <= 0) {
      toast.error('O valor deve ser maior que zero.');
      return;
    }

    if (!vencimento) {
      toast.error('Informe a data de vencimento.');
      return;
    }

    if (vencimento < new Date()) {
      toast.warning('Atenção: vencimento está no passado!');
    }

    setLoading(true);
    try {

      let anexosParaSalvar = [...anexos];
      if (novosArquivos.length > 0) {
        const uploads = await Promise.all(novosArquivos.map(file => uploadAnexo(file, `parcela-${parcela.id}`)));
        anexosParaSalvar = [
          ...anexosParaSalvar,
          ...novosArquivos.map((file, idx) => ({
            url: uploads[idx],
            nome: file.name,
            tipo: file.type,
            path: extrairPathDeUrl(uploads[idx])
          }))
        ];
      }

      // Formatar as datas corretamente para evitar problemas de fuso horário
      const formatarDataISO = (data?: Date): string | null => {
        if (!data) return null;
        // Formatar como YYYY-MM-DD mantendo o dia exato
        const ano = data.getFullYear();
        const mes = String(data.getMonth() + 1).padStart(2, '0'); // +1 porque mês em JS começa do 0
        const dia = String(data.getDate()).padStart(2, '0');
        return `${ano}-${mes}-${dia}`;
      };

      // Usar o valor numérico validado
      await updateParcela(parcela.id, {
        valor: valorNumerico,
        vencimento: formatarDataISO(vencimento)!,
        status: status,
        data_pagamento: status === 'pago' && dataPagamento ? formatarDataISO(dataPagamento) : null,
        anexos: anexosParaSalvar.map(({ url, nome, tipo }) => ({ url, nome, tipo })),
      });

      toast.success('Parcela atualizada com sucesso!');
      onOpenChange(false);
      onSubmit?.();
    } catch (error: any) {
      
      toast.error(`Erro ao atualizar parcela: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Dados do lançamento pai (garantia de objeto)
  const lanc = parcela && typeof parcela.lancamentos === 'object' && parcela.lancamentos !== null ? parcela.lancamentos : null;

  // Função para abrir preview
  const handlePreview = async (anexo: any) => {
    try {
      const path = anexo.path || (anexo.url ? extrairPathDeUrl(anexo.url) : undefined);
      if (!path) throw new Error('Path do anexo não encontrado');
      const url = await getSignedUrlAnexo(path);
      if (anexo.tipo?.startsWith('image/')) {
        setPreviewType('image');
        setPreviewUrl(url);
      } else if (anexo.tipo === 'application/pdf') {
        setPreviewType('pdf');
        setPreviewUrl(url);
      } else {
        window.open(url, '_blank');
      }
    } catch (e) {
      toast.error('Erro ao gerar link temporário do anexo');
    }
  };

  // Função para abrir o drawer de edição do contato
  const handleEditarContato = (e: React.MouseEvent) => {
    // Impedir que o clique se propague para elementos pai
    e.stopPropagation();
    e.preventDefault();

    if (lanc?.contato_id) {
      setContatoId(lanc.contato_id);
      setDrawerContatoOpen(true);
    }
  };

  // Função para atualizar dados após edição do contato
  const handleContatoEditado = async () => {
    // Simplesmente fechar o drawer de contato sem mostrar mensagem
    // A atualização será feita depois se necessário
    setDrawerContatoOpen(false);

    // Apenas recarregar os dados se onSubmit existir,
    // mas sem fechar o drawer principal ou mostrar toast
    if (onSubmit) {
      await onSubmit();
    }
  };

  return (
    <>
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent className="sm:max-w-[600px] flex flex-col h-full p-0">
          <SheetHeader className="shrink-0 p-6 pb-2">
            <div className="flex items-center gap-3 flex-wrap">
              <SheetTitle>
                Parcela #{index} / {total} – {new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(parseFloat(valor) || 0)}
              </SheetTitle>
              <Badge className={badgeStatus.color}>{badgeStatus.text}</Badge>
            </div>
          </SheetHeader>
          <form onSubmit={handleSubmit} className="flex flex-col gap-6 flex-1 p-6 overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="valor">Valor (R$)</Label>
                <Input
                  id="valor"
                  type="text"
                  value={valorFormatadoParaInput}
                  onChange={handleValorChange}
                  className="bg-background h-10"
                  placeholder="R$ 0,00"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="vencimento">Vencimento *</Label>
                <DatePicker date={vencimento} setDate={setVencimento} placeholder="Selecione a data" className="h-10" />
              </div>
              <div className="col-span-2 flex gap-2 items-center">
                {status !== 'pago' ? (
                  <Button type="button" variant="default" onClick={handleMarcarComoPago}>
                    Marcar como pago
                  </Button>
                ) : (
                  <Button type="button" variant="outline" onClick={handleEstornar}>
                    Estornar pagamento
                  </Button>
                )}
                {status === 'pago' && dataPagamento && (
                  <span className="text-xs text-muted-foreground ml-2">Pago em {dataPagamento.toLocaleDateString('pt-BR')}</span>
                )}
              </div>
            </div>
            {/* Card de dados do lançamento pai */}
            {lanc && (
              <Card className="mb-2 bg-muted/70">
                <CardHeader className="pb-2">
                  <CardTitle className="text-base"></CardTitle>
                </CardHeader>
                <CardContent className="pt-0 text-sm space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-semibold">Descrição:</span>
                    <Link href={`/lancamentos?id=${lanc?.id ?? ''}`} className="underline flex items-center gap-1">
                      {lanc?.descricao ?? '-'}
                      <ExternalLink className="w-3 h-3 inline" />
                    </Link>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold">Fornecedor/Cliente:</span>
                    <div className="flex items-center">
                      <span>
                        {lanc?.contatos?.nome_empresa || lanc?.contatos?.nome_razao || lanc?.contatos?.nome_contato || '-'}
                      </span>
                      {lanc?.contato_id && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 ml-1 -mr-1.5"
                          onClick={handleEditarContato}
                          title="Editar dados do fornecedor"
                          type="button"
                        >
                          <Edit className="h-3.5 w-3.5" />
                        </Button>
                      )}
                    </div>
                  </div>
                  <div><span className="font-semibold">Obra:</span> {lanc?.obras?.nome ?? '-'}</div>
                  <div><span className="font-semibold">Forma de pagamento:</span> {lanc?.forma_pagamento ?? '-'}</div>

                  {/* Dados de pagamento do fornecedor */}
                  {lanc?.contatos && (
                    <>
                      <div className="border-t border-muted-foreground/20 pt-2 mt-2">
                        <div className="font-semibold text-primary">Dados para pagamento</div>
                        {lanc.contatos.chave_pix ? (
                          <div className="mt-1">
                            <span className="font-medium">PIX:</span> {lanc.contatos.chave_pix}
                          </div>
                        ) : (
                          <div className="text-muted-foreground text-xs italic">
                            Nenhuma informação de pagamento cadastrada para este fornecedor.
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            )}
            {/* Observações */}
            <div>
              <label className="block text-sm font-medium mb-1">Observações</label>
              <Textarea rows={3} value={observacao} onChange={e => setObservacao(e.target.value)} placeholder="Digite observações para esta parcela" />
            </div>
            {/* Anexos - full width */}
            <div className="col-span-2">
              <label className="block text-sm font-medium mb-1">Anexos</label>
              {novosArquivos.length > 0 && (
                <ul className="mb-2 space-y-1">
                  {novosArquivos.map((file, idx) => (
                    <li key={idx} className="flex items-center bg-muted rounded px-2 py-1">
                      <Check className="text-green-600 w-4 h-4 mr-2" />
                      <span className="flex-1 truncate text-sm">{file.name}</span>
                      <button type="button" className="ml-2 p-1 hover:text-destructive" onClick={() => handleRemoveNovoArquivo(idx)} aria-label="Remover arquivo">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </li>
                  ))}
                </ul>
              )}
              {anexos && anexos.length > 0 && (
                <ul className="mb-2 space-y-1">
                  {anexos.map((anexo, idx) => (
                    <li key={idx} className="flex items-center bg-muted rounded px-2 py-1">
                      <Check className="text-green-600 w-4 h-4 mr-2" />
                      <button type="button" className="flex-1 truncate text-sm underline text-left" title={anexo.nome} onClick={() => handlePreview(anexo)}>
                        {anexo.nome || 'Arquivo'}
                      </button>
                      <button type="button" className="ml-2 p-1 hover:text-destructive" onClick={() => handleRemoveAnexoSalvo(idx)} aria-label="Remover anexo salvo">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </li>
                  ))}
                </ul>
              )}
              <Dropzone onDrop={files => setNovosArquivos(prev => [...prev, ...files])} multiple accept={{'application/pdf': ['.pdf'], 'image/png': ['.png'], 'image/jpeg': ['.jpg', '.jpeg']}}>
                <div className="flex flex-col items-center justify-center py-4">
                  <Upload className="w-6 h-6 text-muted-foreground mb-1" />
                  <span className="text-xs text-muted-foreground">Arraste e solte ou clique para anexar</span>
                  <span className="text-[11px] text-muted-foreground mt-1">Você pode enviar PDF, PNG ou JPEG</span>
                </div>
              </Dropzone>
            </div>
            <SheetFooter className="mt-auto flex flex-row gap-2 justify-end">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
                Cancelar
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Salvando...' : 'Salvar'}
              </Button>
            </SheetFooter>
          </form>
          {/* Modal de preview de anexo */}
          <Dialog open={!!previewUrl} onOpenChange={open => { if (!open) setPreviewUrl(null); }}>
            <DialogContent className="w-[90vw] max-w-5xl h-[90vh] flex items-center justify-center">
              <DialogTitle className="sr-only">Preview do anexo</DialogTitle>
              {previewType === 'image' && previewUrl && (
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="max-h-full max-w-full w-auto h-auto object-contain mx-auto block"
                />
              )}
              {previewType === 'pdf' && previewUrl && (
                <iframe src={previewUrl} title="PDF Preview" className="w-full h-[70vh]" />
              )}
            </DialogContent>
          </Dialog>
        </SheetContent>
      </Sheet>

      {/* Drawer para edição do contato - movido para fora do Sheet principal */}
      <DrawerContato
        open={drawerContatoOpen}
        onOpenChange={setDrawerContatoOpen}
        contatoId={contatoId}
        onSuccess={handleContatoEditado}
      />
    </>
  );
}