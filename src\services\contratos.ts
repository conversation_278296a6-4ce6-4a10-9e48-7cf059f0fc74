'use client';

import { Contrato, CreateContratoDTO, UpdateContratoDTO } from '@/types/contratos';
import { useSupabase } from '@/hooks/useSupabase';

export function useContratosService() {
  const supabase = useSupabase();

  return {
    async listarContratos(): Promise<Contrato[]> {
      const { data, error } = await supabase
        .from('contratos')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },

    async buscarContratoPorId(id: string): Promise<Contrato | null> {
      const { data, error } = await supabase
        .from('contratos')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    },

    async criarContrato(contrato: CreateContratoDTO): Promise<void> {
      const { error } = await supabase
        .from('contratos')
        .insert(contrato);

      if (error) throw error;
    },

    async atualizarContrato(id: string, contrato: UpdateContratoDTO): Promise<void> {
      const { error } = await supabase
        .from('contratos')
        .update(contrato)
        .eq('id', id);

      if (error) throw error;
    },

    async excluirContrato(id: string): Promise<void> {
      const { error } = await supabase
        .from('contratos')
        .delete()
        .eq('id', id);

      if (error) throw error;
    }
  };
}