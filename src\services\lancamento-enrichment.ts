'use client';

import { supabase } from '@/lib/supabase/client';
import { LancamentoComParcelas } from '@/types/lancamentos';
import { Documento } from '@/types/documentos';
import { EnrichmentResult } from '@/app/(auth)/lancamentos/components/enrichment-dropzone';

export class LancamentoEnrichmentService {
  /**
   * Aplica enriquecimento a um lançamento baseado nos resultados da análise de documentos
   */
  static async applyEnrichment(
    lancamentoId: string, 
    enrichmentResult: EnrichmentResult
  ): Promise<void> {
    try {
      // 1. Atualizar o status do lançamento se necessário
      if (enrichmentResult.status) {
        await this.updateLancamentoStatus(lancamentoId, enrichmentResult.status);
      }

      // 2. Atualizar dados enriquecidos (valor, datas, forma de pagamento)
      if (enrichmentResult.dadosEnriquecidos) {
        await this.updateLancamentoDados(lancamentoId, enrichmentResult.dadosEnriquecidos);
      }

      // 3. Associar documentos ao lançamento
      if (enrichmentResult.novosDocumentos.length > 0) {
        await this.associateDocumentsToLancamento(lancamentoId, enrichmentResult.novosDocumentos);
      }

      // 4. Associar novo contato ao lançamento se criado
      if (enrichmentResult.novoContato) {
        await this.associateContatoToLancamento(lancamentoId, enrichmentResult.novoContato.id);
      }

    } catch (error) {
      console.error('Erro ao aplicar enriquecimento:', error);
      throw new Error('Falha ao aplicar enriquecimento ao lançamento');
    }
  }

  /**
   * Atualiza o status do lançamento
   */
  private static async updateLancamentoStatus(
    lancamentoId: string, 
    novoStatus: 'pago' | 'pendente' | 'vencido'
  ): Promise<void> {
    // Mapear status para o enum do banco
    let statusBanco: 'Em aberto' | 'Pago' | 'Cancelado';
    
    switch (novoStatus) {
      case 'pago':
        statusBanco = 'Pago';
        break;
      case 'pendente':
      case 'vencido':
        statusBanco = 'Em aberto';
        break;
      default:
        statusBanco = 'Em aberto';
    }

    const updateData: any = {
      status: statusBanco
    };

    // Se marcando como pago, definir data de pagamento
    if (novoStatus === 'pago') {
      updateData.data_pagamento = new Date().toISOString();
    }

    const { error } = await supabase
      .from('lancamentos')
      .update(updateData)
      .eq('id', lancamentoId);

    if (error) {
      throw error;
    }
  }

  /**
   * Atualiza dados enriquecidos do lançamento
   */
  private static async updateLancamentoDados(
    lancamentoId: string,
    dadosEnriquecidos: {
      valor_total?: number;
      data_vencimento?: string;
      forma_pagamento?: string;
      data_pagamento?: string;
    }
  ): Promise<void> {
    const updateData: any = {};

    if (dadosEnriquecidos.valor_total) {
      updateData.valor_total = dadosEnriquecidos.valor_total;
    }

    if (dadosEnriquecidos.data_vencimento) {
      updateData.data_competencia = dadosEnriquecidos.data_vencimento;
    }

    if (dadosEnriquecidos.forma_pagamento) {
      updateData.forma_pagamento = dadosEnriquecidos.forma_pagamento;
    }

    if (dadosEnriquecidos.data_pagamento) {
      updateData.data_pagamento = dadosEnriquecidos.data_pagamento;
    }

    if (Object.keys(updateData).length > 0) {
      const { error } = await supabase
        .from('lancamentos')
        .update(updateData)
        .eq('id', lancamentoId);

      if (error) {
        throw error;
      }
    }
  }

  /**
   * Associa documentos ao lançamento
   */
  private static async associateDocumentsToLancamento(
    lancamentoId: string,
    documentos: Documento[]
  ): Promise<void> {
    for (const documento of documentos) {
      const { error } = await supabase
        .from('documentos')
        .update({ lancamento_id: lancamentoId })
        .eq('id', documento.id);

      if (error) {
        console.error(`Erro ao associar documento ${documento.id}:`, error);
        // Continuar com outros documentos mesmo se um falhar
      }
    }
  }

  /**
   * Associa novo contato ao lançamento
   */
  private static async associateContatoToLancamento(
    lancamentoId: string,
    contatoId: string
  ): Promise<void> {
    const { error } = await supabase
      .from('lancamentos')
      .update({ contato_id: contatoId })
      .eq('id', lancamentoId);

    if (error) {
      throw error;
    }
  }

  /**
   * Valida se um lançamento pode ser enriquecido
   */
  static canEnrichLancamento(lancamento: LancamentoComParcelas): boolean {
    // Não permitir enriquecimento de lançamentos cancelados
    if (lancamento.status === 'Cancelado') {
      return false;
    }

    return true;
  }

  /**
   * Obtém sugestões de enriquecimento baseado no lançamento atual
   */
  static getEnrichmentSuggestions(lancamento: LancamentoComParcelas): string[] {
    const suggestions: string[] = [];

    if (lancamento.status === 'Em aberto') {
      suggestions.push('📄 Adicione um comprovante para marcar como pago');
    }

    if (!lancamento.contato_id) {
      suggestions.push('👤 Adicione um documento com dados de contato para criar fornecedor');
    }

    if (!lancamento.documentos || lancamento.documentos.length === 0) {
      suggestions.push('📎 Adicione documentos relacionados a este lançamento');
    }

    return suggestions;
  }
} 