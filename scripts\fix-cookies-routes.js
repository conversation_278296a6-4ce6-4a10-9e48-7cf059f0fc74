const fs = require('fs');
const path = require('path');

// Função para encontrar todos os arquivos route.ts
function findRouteFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      files.push(...findRouteFiles(fullPath));
    } else if (item === 'route.ts') {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Função para aplicar as correções
function fixCookiesInFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Verificar se já tem as correções aplicadas
  if (content.includes('getSupabaseRouteClient') || content.includes("export const dynamic = 'force-dynamic'")) {
    console.log(`✓ ${filePath} - já corrigido`);
    return false;
  }
  
  // Verificar se usa cookies() e createRouteHandlerClient
  if (!content.includes('cookies()') || !content.includes('createRouteHandlerClient')) {
    console.log(`- ${filePath} - não precisa de correção`);
    return false;
  }
  
  console.log(`🔧 Corrigindo ${filePath}...`);
  
  // Adicionar import do utilitário
  if (!content.includes("import { getSupabaseRouteClient }")) {
    content = content.replace(
      /import { createRouteHandlerClient } from '@supabase\/auth-helpers-nextjs';/,
      `import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';`
    );
    modified = true;
  }
  
  // Adicionar dynamic export
  if (!content.includes("export const dynamic = 'force-dynamic'")) {
    // Encontrar a primeira função export e adicionar antes dela
    const exportMatch = content.match(/export async function (GET|POST|PUT|DELETE|PATCH)/);
    if (exportMatch) {
      const insertIndex = content.indexOf(exportMatch[0]);
      content = content.slice(0, insertIndex) + 
                "export const dynamic = 'force-dynamic';\n\n" + 
                content.slice(insertIndex);
      modified = true;
    }
  }
  
  // Substituir padrões de criação de cliente
  const patterns = [
    {
      old: /const cookieStore = cookies\(\);\s*const supabase = createRouteHandlerClient\(\{ cookies: \(\) => cookieStore \}\);/g,
      new: 'const supabase = await getSupabaseRouteClient();'
    },
    {
      old: /const cookieStore = cookies\(\);\s*supabase = createRouteHandlerClient\(\{ cookies: \(\) => cookieStore \}\);/g,
      new: 'supabase = await getSupabaseRouteClient();'
    }
  ];
  
  for (const pattern of patterns) {
    if (pattern.old.test(content)) {
      content = content.replace(pattern.old, pattern.new);
      modified = true;
    }
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ ${filePath} - corrigido com sucesso`);
    return true;
  }
  
  console.log(`⚠️  ${filePath} - não foi possível aplicar correções automaticamente`);
  return false;
}

// Executar o script
const apiDir = path.join(__dirname, '..', 'src', 'app', 'api');
const routeFiles = findRouteFiles(apiDir);

console.log(`Encontrados ${routeFiles.length} arquivos route.ts`);
console.log('Aplicando correções...\n');

let corrected = 0;
for (const file of routeFiles) {
  if (fixCookiesInFile(file)) {
    corrected++;
  }
}

console.log(`\n✨ Processo concluído! ${corrected} arquivos corrigidos.`); 