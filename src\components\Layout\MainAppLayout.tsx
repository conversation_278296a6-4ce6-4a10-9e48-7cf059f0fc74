'use client'

import { Sidebar } from './Sidebar'
// import { Header } from './Header' // Removido import
import { supabase } from '@/lib/supabase/client'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { ChevronLeft, ChevronRight, CircleDot } from 'lucide-react'
import { useSidebar } from '@/contexts/SidebarContext'
import { clearSupabaseCookies, resetSession, emergencyFix } from '@/lib/fixCookies'

export function MainAppLayout({ children }: { children: React.ReactNode }) {

  const router = useRouter()
  const pathname = usePathname()
  const [loading, setLoading] = useState(true)
  const [authError, setAuthError] = useState<string | null>(null)
  const { isCollapsed } = useSidebar()

  useEffect(() => {
    // Effect for pathname changes
  }, [pathname])

  // Função para tentar limpar cookies problemáticos
  const clearAuthCookies = () => {
    return clearSupabaseCookies();
  }

  useEffect(() => {
    const checkUser = async () => {
      try {
        const { data, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          // Erro explícito na sessão

          // Verifica se é um erro de parsing de cookie
          if (sessionError.message?.includes('parse') ||
              sessionError.message?.includes('token') ||
              sessionError.message?.includes('cookie')) {

            setAuthError('Erro de autenticação. Cookies podem estar corrompidos.')
            setLoading(false)
            return
          }

          // Outros erros, redireciona
          throw sessionError
        }

        if (!data?.session) {
          // Sem sessão, mas não necessariamente um erro

          // Se a página atual não for login, mostre o erro mas não redirecione
          if (!pathname.includes('/login')) {
            setAuthError('Sessão não encontrada. Acesse manualmente a página de login se necessário.')
            setLoading(false)
          } else {
            // Já estamos na página de login, só continua
            setLoading(false)
          }
          return
        }

        // Sessão válida
        setLoading(false)
      } catch (err: any) {
        // Erro durante checagem de sessão

        // Verifica se é um erro de parsing
        if (err.message?.includes('parse') ||
            err.message?.includes('token') ||
            err.message?.includes('cookie')) {

          setAuthError('Erro de autenticação. Use os botões para tentar corrigir.')
          setLoading(false)
        } else {
          // Outros erros, mostrar mas não redirecionar
          setAuthError(err.message || 'Erro desconhecido na autenticação')
          setLoading(false)
        }
      }
    }

    checkUser()
  }, [pathname, router])

  const handleForceLogin = () => {
    resetSession();
  }

  const handleEmergencyFix = () => {
    // Limpar todos os cookies relacionados ao Supabase
    clearSupabaseCookies();

    // Limpar localStorage e sessionStorage
    try {
      localStorage.clear();
      sessionStorage.clear();
    } catch (e) {
      // Silently handle error
    }

    // Redirecionar para a página de login
    window.location.href = '/login';
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-black"></div>
      </div>
    )
  }

  if (authError) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md p-6 bg-red-50 border border-red-200 rounded-lg">
          <h2 className="text-xl font-bold text-red-700 mb-3">Erro de Autenticação</h2>
          <p className="text-red-600 mb-6">{authError}</p>
          <div className="flex flex-col gap-3">
            <Button onClick={() => window.location.reload()} className="w-full">
              Recarregar Página
            </Button>
            <Button onClick={handleForceLogin} variant="outline" className="w-full">
              Ir para Login
            </Button>
            <Button
              onClick={handleEmergencyFix}
              variant="destructive"
              className="w-full mt-2"
            >
              Correção de Emergência
            </Button>
            <Button
              onClick={() => window.location.href = '/'}
              variant="ghost"
              className="w-full mt-2"
            >
              Voltar ao Início
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Restaurar estrutura original
  return (
    <div className="min-h-screen bg-muted">
      <aside className={`
        fixed inset-y-0 left-0 z-30 bg-muted border-border
        transition-all duration-300 ease-in-out
        ${isCollapsed ? 'w-16' : 'w-64'}
        ${isCollapsed ? 'lg:w-16' : 'lg:w-64'}
        lg:block
        translate-x-0
      `}>
        <div className="flex flex-col h-full">
          <div className="flex items-center h-16 px-4 flex-shrink-0 bg-muted">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center h-8 w-8 rounded-md bg-primary">
                <CircleDot className="h-5 w-5 text-primary-foreground" />
              </div>
              {!isCollapsed && (
                <h1 className="text-base font-medium text-foreground">POC Automacao</h1>
              )}
            </div>
          </div>
          <nav className="flex-1 px-3 py-4 overflow-y-auto">
            <Sidebar collapsed={isCollapsed} />
          </nav>
        </div>
      </aside>

      <div className={`
        flex flex-col min-h-screen
        transition-all duration-300 ease-in-out
        ${isCollapsed ? 'lg:pl-16' : 'lg:pl-64'}
      `}>
        <main className="flex-1 m-4 content-padding rounded-lg bg-white shadow-md">
          {/* Estrutura de header interna removida, apenas children */}
          <div className="content-container">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}