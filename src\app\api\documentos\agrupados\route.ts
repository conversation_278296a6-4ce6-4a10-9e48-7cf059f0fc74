import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { createServiceClient } from '@/lib/supabase/service-client';

export const dynamic = 'force-dynamic';

// GET /api/documentos/agrupados - Lista documentos agrupados por lançamento
export async function GET(request: NextRequest) {
  try {
    // Obter status da query string, se disponível
    const url = new URL(request.url);
    const status = url.searchParams.get('status') as any;

    // Criar cliente Supabase diretamente com as variáveis de ambiente
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      return NextResponse.json(
        { error: 'Configuração do servidor incompleta' },
        { status: 500 }
      );
    }

    // Criar cliente Supabase
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // Verificar se devemos forçar o uso do cliente de serviço
    const forceServiceRole = url.searchParams.get('forceServiceRole') === 'true';

    // Usar o cliente de serviço se solicitado
    let clientToUse = supabase;

    if (forceServiceRole) {
      const serviceClient = createServiceClient();

      if (!serviceClient) {
        return NextResponse.json({ error: 'Erro ao criar cliente de serviço' }, { status: 500 });
      }

      clientToUse = serviceClient;
    }

    // Buscar todos os documentos com informações do lançamento e contagem de anexos
    let query = clientToUse
      .from('documentos')
      .select(`
        *,
        lancamentos:lancamento_id (
          id,
          descricao,
          valor_total,
          data_competencia,
          data_pagamento,
          tipo_lancamento,
          status,
          forma_pagamento,
          contato_id,
          obra_id
        ),
        documento_anexos (
          id
        )
      `)
      .order('created_at', { ascending: false });

    // Filtrar por status se necessário
    if (status) {
      query = query.eq('status', status);
    }

    const { data: documentos, error: queryError } = await query;

    if (queryError) {
      return NextResponse.json(
        { error: 'Erro ao consultar documentos: ' + queryError.message },
        { status: 500 }
      );
    }

    if (!documentos) {
      return NextResponse.json([]);
    }

    // Agrupar documentos por lançamento
    const agrupamentos = new Map();

    documentos.forEach((documento: any) => {
      const lancamentoId = documento.lancamento_id;
      const chave = lancamentoId || 'sem_lancamento';

      if (!agrupamentos.has(chave)) {
        if (lancamentoId && documento.lancamentos) {
          // Criar grupo para lançamento específico
          agrupamentos.set(chave, {
            id: lancamentoId,
            tipo: 'lancamento',
            lancamento: documento.lancamentos,
            documentos: [],
            quantidade_documentos: 0
          });
        } else {
          // Criar grupo para documentos sem lançamento
          agrupamentos.set(chave, {
            id: null,
            tipo: 'sem_lancamento',
            documentos: [],
            quantidade_documentos: 0
          });
        }
      }

      // Remover informações desnecessárias e adicionar contagem de anexos
      const { lancamentos, documento_anexos, ...documentoLimpo } = documento;
      
      // Adicionar contagem de anexos ao documento
      const documentoComAnexos = {
        ...documentoLimpo,
        quantidade_anexos: documento_anexos ? documento_anexos.length : 0
      };
      
      const grupo = agrupamentos.get(chave);
      grupo.documentos.push(documentoComAnexos);
      grupo.quantidade_documentos++;
    });

    // Converter o Map para array e ordenar
    const resultado = Array.from(agrupamentos.values()).sort((a, b) => {
      // Colocar lançamentos primeiro, depois documentos sem lançamento
      if (a.tipo === 'lancamento' && b.tipo === 'sem_lancamento') return -1;
      if (a.tipo === 'sem_lancamento' && b.tipo === 'lancamento') return 1;
      
      // Dentro do mesmo tipo, ordenar por data mais recente
      const dataA = new Date(a.documentos[0]?.created_at || 0);
      const dataB = new Date(b.documentos[0]?.created_at || 0);
      return dataB.getTime() - dataA.getTime();
    });

    return NextResponse.json(resultado);
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Erro ao listar documentos agrupados: ' + error.message },
      { status: 500 }
    );
  }
} 