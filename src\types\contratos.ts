export interface Contrato {
  id: string;
  numero: string;
  descricao: string;
  valor: number;
  data_inicio: string;
  data_fim: string | null;
  status: 'ativo' | 'encerrado' | 'cancelado';
  obra_id: string | null;
  contratado_id: string | null;
  created_at?: string;
  updated_at?: string;
}

export type CreateContratoDTO = Omit<Contrato, 'id' | 'created_at' | 'updated_at'>;
export type UpdateContratoDTO = Partial<CreateContratoDTO>;