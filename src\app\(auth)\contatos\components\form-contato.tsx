'use client';

import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { useContatosService } from '@/services/contatos';
import { Contato } from '@/types/contatos';
import { Spinner } from '@/components/ui/spinner';

const formSchema = z.object({
  nome_empresa: z.string().min(1, 'Nome da empresa é obrigatório'),
  nome_razao: z.string().optional().nullable(),
  nome_contato: z.string().optional().nullable(),
  email: z.string().email('Email inválido').optional().or(z.literal('')),
  telefone: z.string().optional(),
  chave_pix: z.string().optional(),
  tipo: z.enum(['fornecedor', 'prestador', 'cliente'], {
    required_error: 'Tipo é obrigatório'
  }),
  tipo_pessoa: z.enum(['fisica', 'juridica']).optional().nullable(),
  cpf_cnpj: z.string().optional().nullable()
});

type FormData = z.infer<typeof formSchema>;

interface FormContatoProps {
  contato?: Contato | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export function FormContato({ contato, onSuccess, onCancel }: FormContatoProps) {
  const contatosService = useContatosService();
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      nome_empresa: '',
      nome_razao: '',
      nome_contato: '',
      email: '',
      telefone: '',
      chave_pix: '',
      tipo: undefined,
      tipo_pessoa: null,
      cpf_cnpj: ''
    }
  });

  const { formState } = form;
  const isSubmitting = formState.isSubmitting;
  const tipoPessoa = form.watch('tipo_pessoa');

  useEffect(() => {
    if (contato) {
      form.reset({
        nome_empresa: contato.nome_empresa,
        nome_razao: contato.nome_razao || '',
        nome_contato: contato.nome_contato || '',
        email: contato.email || '',
        telefone: contato.telefone || '',
        chave_pix: contato.chave_pix || '',
        tipo: contato.tipo,
        tipo_pessoa: contato.tipo_pessoa || null,
        cpf_cnpj: contato.cpf_cnpj || ''
      });
    }
  }, [contato, form]);

  async function onSubmit(data: FormData) {
    try {
      if (contato) {
        await contatosService.atualizarContato(contato.id, data);
      } else {
        await contatosService.criarContato(data);
      }
      onSuccess();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      toast.error(`Erro ao ${contato ? 'atualizar' : 'criar'} contato: ${errorMessage}`);
    }
  }

  const getCpfCnpjPlaceholder = () => {
    if (!tipoPessoa) return "Selecione o tipo de pessoa primeiro";
    return tipoPessoa === 'fisica' ? "CPF (apenas números)" : "CNPJ (apenas números)";
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="nome_empresa"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome Empresa</FormLabel>
              <FormControl>
                <Input placeholder="Digite o nome da empresa" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="nome_razao"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Razão Social</FormLabel>
              <FormControl>
                <Input placeholder="Digite a razão social" {...field} value={field.value || ''} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="nome_contato"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome do Contato</FormLabel>
              <FormControl>
                <Input placeholder="Digite o nome do contato" {...field} value={field.value || ''} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="tipo_pessoa"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tipo de Pessoa</FormLabel>
                <Select 
                  value={field.value || ''} 
                  onValueChange={(value) => {
                    field.onChange(value);
                    form.setValue('cpf_cnpj', '');
                  }}
                  disabled={isSubmitting}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o tipo de pessoa" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="bg-background text-foreground z-50">
                    <SelectItem value="fisica">Pessoa Física</SelectItem>
                    <SelectItem value="juridica">Pessoa Jurídica</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="cpf_cnpj"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tipoPessoa === 'fisica' ? 'CPF' : tipoPessoa === 'juridica' ? 'CNPJ' : 'CPF/CNPJ'}</FormLabel>
                <FormControl>
                  <Input 
                    placeholder={getCpfCnpjPlaceholder()} 
                    {...field} 
                    value={field.value || ''} 
                    disabled={!tipoPessoa}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" placeholder="Digite o email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="telefone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Telefone</FormLabel>
              <FormControl>
                <Input placeholder="Digite o telefone" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="chave_pix"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Chave PIX</FormLabel>
              <FormControl>
                <Input placeholder="Digite a chave PIX (opcional)" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="tipo"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tipo</FormLabel>
              <Select 
                value={field.value} 
                onValueChange={field.onChange}
                disabled={isSubmitting}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo de contato" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent className="bg-background text-foreground z-50">
                  <SelectItem value="cliente">Cliente</SelectItem>
                  <SelectItem value="fornecedor">Fornecedor</SelectItem>
                  <SelectItem value="prestador">Prestador de Serviço</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancelar
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Spinner className="mr-2 h-4 w-4" />}
            {contato ? 'Atualizar' : 'Criar'} Contato
          </Button>
        </div>
      </form>
    </Form>
  );
}