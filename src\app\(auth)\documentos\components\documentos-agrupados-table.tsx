'use client';

import React, { useState, useEffect, useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { 
  Eye,
  MoreHorizontal,
  Trash2,
  FileText,
  Download,
  Receipt,
  FileX,
  FilePlus,
  Paperclip,
  ChevronDown,
  ChevronRight,
  TrendingUp,
  TrendingDown,
  Sparkles
} from 'lucide-react';
import { getDocumentosAgrupados } from '@/services/documentos';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useContatosService } from '@/services/contatos';
import { Contato } from '@/types/contatos';
import { Documento, DocumentosAgrupados } from '@/types/documentos';
import { toast } from 'sonner';

interface DocumentosAgrupadosTableProps {
  status?: string;
  documentosAgrupados?: DocumentosAgrupados[];
  loading?: boolean;
  filtros?: {
    termo?: string;
    obraId?: string;
    contatoId?: string;
    dataInicio?: Date;
    dataFim?: Date;
  };
  onSelectDocumento?: (documento: Documento) => void;
  onDocumentosChange?: () => void;
  onDocumentClick?: (documento: Documento) => void;
  onDocumentDeleted?: () => void;
}

export function DocumentosAgrupadosTable({ 
  status,
  documentosAgrupados: documentosAgrupadosExternos,
  loading: loadingExterno,
  filtros, 
  onSelectDocumento,
  onDocumentosChange,
  onDocumentClick, 
  onDocumentDeleted 
}: DocumentosAgrupadosTableProps) {
  const [documentosAgrupados, setDocumentosAgrupados] = useState<DocumentosAgrupados[]>([]);
  const [loading, setLoading] = useState(true);
  const [contatos, setContatos] = useState<Contato[]>([]);
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());
  const contatosService = useContatosService();

  // Se documentos externos são fornecidos, usá-los diretamente
  const documentosParaExibir = documentosAgrupadosExternos || documentosAgrupados;
  const isLoading = loadingExterno !== undefined ? loadingExterno : loading;

  useEffect(() => {
    // Só carregar dados se não temos documentos externos
    if (!documentosAgrupadosExternos) {
      loadDocumentosAgrupados();
    }
    loadContatos();
  }, [status, filtros, documentosAgrupadosExternos]);

  const loadContatos = async () => {
    try {
      const data = await contatosService.listarContatos();
      setContatos(data);
    } catch (error) {
      console.error('Erro ao carregar contatos:', error);
    }
  };

  const getContatoNome = (contatoId?: string | null) => {
    if (!contatoId) return 'N/A';
    const contato = contatos.find(c => c.id === contatoId);
    return contato?.nome_empresa || 'N/A';
  };

  const toggleGroup = (groupId: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId);
    } else {
      newExpanded.add(groupId);
    }
    setExpandedGroups(newExpanded);
  };

  const loadDocumentosAgrupados = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (status) params.append('status', status);
      params.append('forceServiceRole', 'true');
      
      const response = await fetch(`/api/documentos/agrupados?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Erro ao carregar documentos agrupados');
      }
      const data = await response.json();
      setDocumentosAgrupados(data);
    } catch (error) {
      console.error('Erro ao carregar documentos agrupados:', error);
      toast.error('Erro ao carregar documentos agrupados');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (documento: Documento) => {
    if (!documento.arquivo_url) return;
    
    try {
      // Criar link para download
      const link = document.createElement('a');
      link.href = documento.arquivo_url;
      link.download = documento.nome;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Erro ao fazer download:', error);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      // TODO: Implementar exclusão de documento
      console.log('Excluir documento:', id);
      onDocumentDeleted?.();
      onDocumentosChange?.();
    } catch (error) {
      console.error('Erro ao excluir documento:', error);
    }
  };





  const formatValor = (valor: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(valor);
  };

  const formatData = (data: string) => {
    return format(new Date(data), 'dd/MM/yyyy', { locale: ptBR });
  };



  // Função para obter o contato mais comum do grupo
  const getGrupoContato = (documentos: Documento[]) => {
    const contatoCount = documentos.reduce((acc, doc) => {
      const contatoId = doc.contato_id || 'N/A';
      acc[contatoId] = (acc[contatoId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const contatoMaisComum = Object.entries(contatoCount).reduce((a, b) => contatoCount[a[0]] > contatoCount[b[0]] ? a : b)[0];
    return contatoMaisComum === 'N/A' ? 'N/A' : getContatoNome(contatoMaisComum);
  };

  // Função para obter o projeto mais comum do grupo
  const getGrupoProjeto = (documentos: Documento[]) => {
    const projetoCount = documentos.reduce((acc, doc) => {
      const projetoId = doc.obra_id || 'N/A';
      acc[projetoId] = (acc[projetoId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const projetoMaisComum = Object.entries(projetoCount).reduce((a, b) => projetoCount[a[0]] > projetoCount[b[0]] ? a : b)[0];
    return projetoMaisComum === 'N/A' ? 'N/A' : projetoMaisComum; // TODO: buscar nome do projeto
  };

  // Função para exibir valor com ícone do tipo
  const getValorComTipo = (valor: number, tipo?: 'receita' | 'despesa') => {
    if (!tipo) return formatValor(valor);
    
    const Icon = tipo === 'receita' ? TrendingUp : TrendingDown;
    const colorClass = tipo === 'receita' ? 'text-green-600' : 'text-red-600';
    
    return (
      <div className={`flex items-center gap-1 ${colorClass}`}>
        <Icon className="h-3 w-3" />
        <span>{formatValor(valor)}</span>
      </div>
    );
  };

  // Função para obter o valor total do grupo
  const getGrupoValor = (grupo: DocumentosAgrupados) => {
    if (grupo.tipo === 'lancamento' && grupo.lancamento) {
      return getValorComTipo(grupo.lancamento.valor_total, grupo.lancamento.tipo_lancamento);
    } else {
      // Para documentos sem lançamento, somar apenas valores de documentos que não sejam comprovantes
      const valorTotal = grupo.documentos.reduce((total, doc) => {
        // Não somar comprovantes (que são apenas evidências de pagamento)
        const isComprovante = doc.dados_extraidos?.tipo_documento === 'comprovante';
        if (isComprovante) {
          return total;
        }
        
        return total + (doc.valor_total || 0);
      }, 0);
      
      if (valorTotal > 0) {
        return formatValor(valorTotal);
      } else {
        // Se não há valores de boletos/notas, mas há comprovantes, mostrar o valor do comprovante
        const comprovanteComValor = grupo.documentos.find(doc => 
          doc.dados_extraidos?.tipo_documento === 'comprovante' && doc.valor_total
        );
        
        if (comprovanteComValor) {
          return (
            <div className="flex items-center gap-1 text-green-600">
              <span>✅</span>
              <span>{formatValor(comprovanteComValor.valor_total!)}</span>
              <span className="text-xs">(PAGO)</span>
            </div>
          );
        }
        
        return <span className="text-xs text-muted-foreground">-</span>;
      }
    }
  };

  // Função para obter a data do grupo
  const getGrupoData = (grupo: DocumentosAgrupados) => {
    if (grupo.tipo === 'lancamento' && grupo.lancamento?.data_competencia) {
      return formatData(grupo.lancamento.data_competencia);
    } else {
      // Para documentos sem lançamento, pegar a data de vencimento mais próxima
      const datasVencimento = grupo.documentos
        .filter(doc => doc.data_vencimento)
        .map(doc => new Date(doc.data_vencimento!))
        .sort((a, b) => a.getTime() - b.getTime());
      
      if (datasVencimento.length > 0) {
        return format(datasVencimento[0], 'dd/MM/yyyy', { locale: ptBR });
      } else {
        return <span className="text-xs text-muted-foreground">-</span>;
      }
    }
  };

  // Função para obter o título do grupo
  const getGrupoTitulo = (grupo: DocumentosAgrupados) => {
    if (grupo.tipo === 'lancamento' && grupo.lancamento) {
      return grupo.lancamento.descricao;
    } else {
      // Para documentos sem lançamento, usar a descrição do primeiro documento que tiver
      const documentoComDescricao = grupo.documentos.find(doc => doc.descricao && doc.descricao.trim() !== '');
      
      if (documentoComDescricao) {
        return documentoComDescricao.descricao;
      }
      
      // Se nenhum documento tem descrição, tentar gerar baseado nos dados extraídos
      const documentoComDados = grupo.documentos.find(doc => 
        doc.dados_extraidos && (doc.dados_extraidos.fornecedor || doc.dados_extraidos.valor_total)
      );
      
      if (documentoComDados && documentoComDados.dados_extraidos) {
        const fornecedor = documentoComDados.dados_extraidos.fornecedor;
        const valor = documentoComDados.dados_extraidos.valor_total;
        
        if (fornecedor && valor) {
          const valorFormatado = new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
          }).format(valor);
          const fornecedorLimpo = fornecedor.replace(/\b(LTDA|EIRELI|S\.A\.|SA|ME|EPP|COMÉRCIO|INDÚSTRIA|SERVIÇOS)\b/gi, '').trim();
          return `Pagamento ${fornecedorLimpo} - ${valorFormatado}`;
        }
        
        if (fornecedor) {
          const fornecedorLimpo = fornecedor.replace(/\b(LTDA|EIRELI|S\.A\.|SA|ME|EPP|COMÉRCIO|INDÚSTRIA|SERVIÇOS)\b/gi, '').trim();
          return `Pagamento ${fornecedorLimpo}`;
        }
      }
      
      // Fallback para documentos sem dados
      return 'Documentos sem lançamento';
    }
  };

  // Função para obter ícone do tipo de documento
  const getTipoDocumentoIcon = (documento: Documento) => {
    const tipo = documento.dados_extraidos?.tipo_documento;
    const status = documento.dados_extraidos?.status_pagamento;
    
    switch (tipo) {
      case 'comprovante':
        return <span className="text-green-600 text-xs">✅</span>;
      case 'boleto':
        if (status === 'vencido') {
          return <span className="text-red-600 text-xs">⚠️</span>;
        }
        return <span className="text-blue-600 text-xs">📋</span>;
      case 'nota_fiscal':
        return <span className="text-purple-600 text-xs">🧾</span>;
      default:
        return <FileText className="h-3 w-3 text-gray-600" />;
    }
  };

  // Função para obter a ação do grupo
  const getGrupoAcao = (grupo: DocumentosAgrupados) => {
    if (grupo.tipo === 'lancamento') {
      return (
        <div className="flex items-center gap-2">
          <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
          <span className="text-sm text-blue-700 font-medium">Atualizar</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-2">
          <div className="h-2 w-2 bg-green-500 rounded-full"></div>
          <span className="text-sm text-green-700 font-medium">Criar</span>
        </div>
      );
    }
  };

  // Função para calcular o status consolidado do grupo
  const getStatusConsolidadoGrupo = (documentos: Documento[]): 'pendente' | 'pago' => {
    // Se algum documento for um comprovante com status "pago", o grupo é considerado pago
    const temComprovantePago = documentos.some(doc => 
      doc.dados_extraidos?.tipo_documento === 'comprovante' && 
      doc.dados_extraidos?.status_pagamento === 'pago'
    );
    
    return temComprovantePago ? 'pago' : 'pendente';
  };

  // Função para criar um documento representativo do grupo com status consolidado
  const criarDocumentoConsolidado = (grupo: DocumentosAgrupados): Documento => {
    const documentoPrincipal = grupo.documentos[0];
    const statusConsolidado = getStatusConsolidadoGrupo(grupo.documentos);
    
    // Buscar dados de pagamento do comprovante (se houver)
    const comprovante = grupo.documentos.find(doc => 
      doc.dados_extraidos?.tipo_documento === 'comprovante'
    );
    
    // Criar um documento com dados consolidados do grupo
    return {
      ...documentoPrincipal,
      dados_extraidos: {
        ...documentoPrincipal.dados_extraidos,
        status_pagamento: statusConsolidado,
        // Se há comprovante, usar seus dados de pagamento
        data_pagamento: comprovante?.dados_extraidos?.data_pagamento || undefined,
        forma_pagamento_realizada: comprovante?.dados_extraidos?.forma_pagamento_realizada || undefined,
        // Manter outros dados do documento principal
        fornecedor: documentoPrincipal.dados_extraidos?.fornecedor || documentoPrincipal.fornecedor || undefined,
        valor_total: documentoPrincipal.dados_extraidos?.valor_total || documentoPrincipal.valor_total || undefined,
        data_vencimento: documentoPrincipal.dados_extraidos?.data_vencimento || documentoPrincipal.data_vencimento || undefined,
      }
    };
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="border rounded-md">
        <Table>
          <TableHeader className="bg-muted">
            <TableRow>
              <TableHead>Documento</TableHead>
              <TableHead>Cliente</TableHead>
              <TableHead>Projeto</TableHead>
              <TableHead>Valor</TableHead>
              <TableHead>Data de Vencimento</TableHead>
              <TableHead className="w-[120px]">Ação</TableHead>
              <TableHead className="w-[100px]">Opções</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {documentosParaExibir.map((grupo) => {
              const groupId = grupo.id || 'sem_lancamento';
              const isExpanded = expandedGroups.has(groupId);
              const totalAnexos = grupo.documentos.reduce((total, doc) => total + (doc.quantidade_anexos || 0), 0);
              
              return (
                <React.Fragment key={`grupo-${groupId}`}>
                  {/* Linha de cabeçalho do grupo */}
                  <TableRow 
                    className="bg-muted/30 hover:bg-muted/40 cursor-pointer font-medium"
                    onClick={() => toggleGroup(groupId)}
                  >
                    <TableCell>
                      <div className="flex items-center gap-3">
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <ChevronRight className="h-4 w-4 text-muted-foreground" />
                        )}
                        {grupo.tipo === 'lancamento' ? (
                          <Receipt className="h-5 w-5 text-blue-600" />
                        ) : (
                          <FilePlus className="h-5 w-5 text-green-600" />
                        )}
                        <div>
                          <div className="font-medium">
                            {getGrupoTitulo(grupo)}
                          </div>
                          {totalAnexos > 0 && (
                            <div className="text-sm text-muted-foreground font-normal flex items-center gap-1">
                              <Paperclip className="h-3 w-3" />
                              <span>{totalAnexos}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {grupo.tipo === 'lancamento' && grupo.lancamento?.contato_id 
                        ? getContatoNome(grupo.lancamento.contato_id)
                        : getGrupoContato(grupo.documentos)
                      }
                    </TableCell>
                    <TableCell>
                      {grupo.tipo === 'lancamento' && grupo.lancamento?.obra_id 
                        ? grupo.lancamento.obra_id // TODO: buscar nome do projeto
                        : getGrupoProjeto(grupo.documentos)
                      }
                    </TableCell>
                    <TableCell>
                      {getGrupoValor(grupo)}
                    </TableCell>
                    <TableCell>
                      {getGrupoData(grupo)}
                    </TableCell>
                    <TableCell>
                      {getGrupoAcao(grupo)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-7 w-7" 
                          onClick={(e) => {
                            e.stopPropagation();
                            // Abrir documento consolidado do grupo
                            if (grupo.documentos.length > 0) {
                              const documentoConsolidado = criarDocumentoConsolidado(grupo);
                              (onSelectDocumento || onDocumentClick)?.(documentoConsolidado);
                            }
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                  
                  {/* Linhas dos documentos do grupo (só se expandido) */}
                  {isExpanded && grupo.documentos.map((documento, index) => (
                    <TableRow 
                      key={documento.id}
                      className="cursor-pointer hover:bg-gray-50 border-l-4 border-l-blue-200"
                      onClick={() => (onSelectDocumento || onDocumentClick)?.(documento)}
                    >
                      <TableCell>
                        <div className="flex items-center gap-3 ml-8">
                          <div className="h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                            {getTipoDocumentoIcon(documento)}
                          </div>
                          <div>
                            <div className="font-medium text-sm flex items-center gap-2">
                              {documento.nome}
                              {documento.dados_extraidos?.tipo_documento === 'comprovante' && (
                                <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">PAGO</span>
                              )}
                              {documento.dados_extraidos?.status_pagamento === 'vencido' && (
                                <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">VENCIDO</span>
                              )}
                            </div>
                            <div className="text-xs text-gray-500 flex items-center gap-2">
                              <span>{documento.tipo_arquivo}</span>
                              {documento.dados_extraidos?.tipo_documento && (
                                <span className="capitalize">• {documento.dados_extraidos.tipo_documento}</span>
                              )}
                              {documento.quantidade_anexos && documento.quantidade_anexos > 0 && (
                                <div className="flex items-center gap-1">
                                  <Paperclip className="h-3 w-3" />
                                  <span>{documento.quantidade_anexos}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span>{getContatoNome(documento.contato_id)}</span>
                          {!documento.contato_id && documento.dados_extraidos?.contato_detectado && (
                            <div className="flex items-center gap-1 text-yellow-600">
                              <Sparkles className="h-3 w-3" />
                              <span className="text-xs">
                                {documento.dados_extraidos.contato_detectado.nome_empresa || 
                                 documento.dados_extraidos.fornecedor ||
                                 'Detectado'}
                              </span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {documento.obra_id || 'N/A'}
                      </TableCell>
                      <TableCell>
                        <span className="text-xs text-muted-foreground">-</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-xs text-muted-foreground">-</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-xs text-muted-foreground">-</span>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-7 w-7">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="bg-background text-foreground z-50">
                            <DropdownMenuItem 
                              onClick={(e) => {
                                e.stopPropagation();
                                (onSelectDocumento || onDocumentClick)?.(documento);
                              }}
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              Ver detalhes
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownload(documento);
                              }}
                            >
                              <Download className="mr-2 h-4 w-4" />
                              Download
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDelete(documento.id);
                              }}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Excluir
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </React.Fragment>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {documentosParaExibir.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          Nenhum documento encontrado
        </div>
      )}
    </div>
  );
} 