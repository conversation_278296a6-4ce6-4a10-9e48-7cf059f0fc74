import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Criar cliente Supabase com service_role para acesso direto ao banco
const createServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    return null;
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

// POST /api/visualizacoes/bypass - Cria uma nova visualização contornando autenticação
export async function POST(request: NextRequest) {
  try {

    // Obter o body da requisição
    const body = await request.json();
    const { nome, descricao, filtros_json, user_id, contexto } = body;

    // Validações básicas
    if (!nome || typeof nome !== 'string' || nome.trim().length === 0) {
      return NextResponse.json({ error: 'Nome é obrigatório' }, { status: 400 });
    }

    if (!filtros_json || typeof filtros_json !== 'object') {
      return NextResponse.json({ error: 'Filtros inválidos' }, { status: 400 });
    }

    if (!user_id) {
      return NextResponse.json({ error: 'É necessário estar autenticado para criar uma visualização' }, { status: 401 });
    }

    // Criar cliente Supabase com service role
    const supabase = createServiceClient();
    if (!supabase) {
      return NextResponse.json({ error: 'Erro ao criar cliente Supabase' }, { status: 500 });
    }

    // Inserir visualização diretamente usando service role (contorna RLS)
    try {
      // Inserir diretamente na tabela usando service role
      const { data, error } = await supabase
        .from('visualizacoes')
        .insert({
          user_id,
          contexto: contexto || 'lancamentos',
          nome,
          descricao: descricao || null,
          filtros_json
        })
        .select()
        .single();

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data, { status: 201 });
    } catch (insertError: any) {
      return NextResponse.json({ error: 'Erro ao criar visualização: ' + (insertError?.message || 'desconhecido') }, { status: 500 });
    }
  } catch (error: any) {
    return NextResponse.json({ error: 'Erro interno do servidor: ' + (error?.message || 'desconhecido') }, { status: 500 });
  }
}