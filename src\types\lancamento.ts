export interface Lancamento {
  id: string;
  created_at: string;
  updated_at: string;
  descricao: string;
  valor: number;
  data_vencimento: string;
  data_pagamento?: string;
  status: 'pendente' | 'pago' | 'cancelado';
  tipo: 'receita' | 'despesa';
  categoria_id?: string;
  fornecedor_id?: string;
  conta_id?: string;
  user_id: string;
  parcelas?: Parcela[];
}

export interface Parcela {
  id: string;
  lancamento_id: string;
  numero: number;
  valor: number;
  data_vencimento: string;
  data_pagamento?: string;
  status: 'pendente' | 'pago' | 'cancelado';
}

export interface LancamentoFormData {
  descricao: string;
  valor: number;
  data_vencimento: string;
  data_pagamento?: string;
  status: 'pendente' | 'pago' | 'cancelado';
  tipo: 'receita' | 'despesa';
  categoria_id?: string;
  fornecedor_id?: string;
  conta_id?: string;
  user_id: string;
  parcelas?: ParcelaFormData[];
}

export interface ParcelaFormData {
  numero: number;
  valor: number;
  data_vencimento: string;
  data_pagamento?: string;
  status: 'pendente' | 'pago' | 'cancelado';
}