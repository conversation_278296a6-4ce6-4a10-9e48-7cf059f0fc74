import { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { supabase } from '@/lib/supabase/client'; // Usar o cliente Supabase diretamente
import { useVisualizacoesService } from './filtros/visualizacoes-service';
import { toast } from 'sonner';

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  filtrosAtuais: any;
  onVisualizacaoCriada: (nova: any) => void;
}

export default function NovaVisualizacaoModal({ open, onOpenChange, filtrosAtuais, onVisualizacaoCriada }: Props) {
  const [nome, setNome] = useState('');
  const [descricao, setDescricao] = useState('');
  const [loading, setLoading] = useState(false);
  const [erro, setErro] = useState<string | null>(null);
  const visualizacoesService = useVisualizacoesService();
  const { criarVisualizacao } = visualizacoesService;

  // Log para verificar se o hook está funcionando

  async function handleSalvar() {
    
    setErro(null);

    // Validar nome
    if (!nome.trim()) {
      setErro('O nome é obrigatório');
      return;
    }

    // Garantir que filtrosAtuais seja um objeto válido, mesmo que vazio
    if (!filtrosAtuais || typeof filtrosAtuais !== 'object') {
      filtrosAtuais = {};
    }

    setLoading(true);

    try {
      // Variável para armazenar o ID do usuário
      let userId;

      try {
        // Tentar obter o usuário do cliente Supabase

        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          
          throw sessionError;
        }

        if (sessionData?.session?.user) {
          userId = sessionData.session.user.id;
          
        } else {

// Tentar obter via getUser como fallback
          const { data: userData, error: userError } = await supabase.auth.getUser();

          if (userError) {
            
            throw userError;
          }

          if (userData?.user) {
            userId = userData.user.id;

          } else {
            
            setErro('Você precisa estar autenticado para salvar uma visualização. Por favor, faça login novamente.');
            setLoading(false);
            return;
          }
        }
      } catch (authError) {
        
        setErro('Erro ao verificar sua autenticação. Por favor, faça login novamente.');
        setLoading(false);
        return;
      }

      // Garantir que temos um ID de usuário válido
      if (!userId) {
        
        setErro('Não foi possível identificar o usuário. Por favor, faça login novamente.');
        setLoading(false);
        return;
      }

      // Usar o serviço para criar a visualização através do hook
      const novaVisualizacao = await visualizacoesService.criarVisualizacao(
        nome,
        filtrosAtuais,
        userId,
        descricao,
        'lancamentos'
      );

      if (!novaVisualizacao) {
        
        setErro('Erro ao criar visualização. Tente novamente.');
        setLoading(false);
        return;
      }

      // Verificar se a função onVisualizacaoCriada existe antes de chamá-la
      if (typeof onVisualizacaoCriada === 'function') {
        onVisualizacaoCriada(novaVisualizacao);
      }

      setNome('');
      setDescricao('');
      onOpenChange(false);
      toast.success('Visualização salva com sucesso');
    } catch (e: any) {
      setErro(e.message || 'Erro inesperado');
    } finally {
      setLoading(false);
    }
  }

  function handleClose() {
    setNome('');
    setDescricao('');
    setErro(null);
    onOpenChange(false);
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[480px]">
        <DialogHeader>
          <DialogTitle>Nova visualização</DialogTitle>
          <DialogDescription>Salve os filtros atuais para reutilizar depois.</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Nome <span className="text-destructive">*</span></label>
            <Input
              value={nome}
              onChange={e => setNome(e.target.value)}
              placeholder="Ex: Pendentes do Sete Lagos"
              maxLength={60}
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Descrição <span className="text-muted-foreground">(opcional)</span></label>
            <Textarea
              value={descricao}
              onChange={e => setDescricao(e.target.value)}
              placeholder="Descrição opcional para esta visualização"
              className="resize-none h-[80px]"
              maxLength={200}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Filtros aplicados</label>
            <div className="bg-muted p-3 rounded-md text-sm">
              {Object.keys(filtrosAtuais || {}).length > 0 ? (
                <pre className="whitespace-pre-wrap break-all">
                  {JSON.stringify(filtrosAtuais, null, 2)}
                </pre>
              ) : (
                <div className="text-muted-foreground italic">
                  Nenhum filtro selecionado. Esta visualização mostrará todos os lançamentos.
                </div>
              )}
            </div>
          </div>
          {erro && <div className="text-destructive text-sm">{erro}</div>}
          <div className="flex justify-end gap-2 pt-2">
            <Button
              variant="ghost"
              onClick={handleClose}
              disabled={loading}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSalvar}
              disabled={loading || !nome.trim()}
            >
              {loading ? 'Salvando...' : 'Salvar visualização'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}