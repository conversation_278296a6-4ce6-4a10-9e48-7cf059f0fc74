import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Toaster } from 'sonner';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Automação Financeira',
  description: 'Sistema de automação financeira para gestão de lançamentos e documentos',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="pt-BR">
      <body className={inter.className}>
        {/* Script para filtrar logs do Fast Refresh apenas em desenvolvimento */}
        {process.env.NODE_ENV === 'development' && (
          <script
            dangerouslySetInnerHTML={{
              __html: `
                (function() {
                  const originalLog = console.log;
                  const originalWarn = console.warn;
                  const originalInfo = console.info;
                  
                  // Lista de padrões de logs para filtrar
                  const logFilters = [
                    /\\[Fast Refresh\\]/,
                    /rebuilding/,
                    /report-hmr-latency/,
                    /hot-reloader-client/,
                    /webpack-hmr/
                  ];
                  
                  // Função para verificar se o log deve ser filtrado
                  function shouldFilter(message) {
                    const msgStr = String(message);
                    return logFilters.some(filter => filter.test(msgStr));
                  }
                  
                  // Interceptar console.log
                  console.log = function(...args) {
                    if (!shouldFilter(args[0])) {
                      originalLog.apply(console, args);
                    }
                  };
                  
                  // Interceptar console.warn
                  console.warn = function(...args) {
                    if (!shouldFilter(args[0])) {
                      originalWarn.apply(console, args);
                    }
                  };
                  
                  // Interceptar console.info
                  console.info = function(...args) {
                    if (!shouldFilter(args[0])) {
                      originalInfo.apply(console, args);
                    }
                  };
                })();
              `,
            }}
          />
        )}
        {children}
        <Toaster position="top-right" />
      </body>
    </html>
  );
}