import { createClient } from '@supabase/supabase-js';

// Configuração para testes
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

// Criar cliente Supabase para testes
const supabase = createClient(supabaseUrl, supabaseKey);

// Função para criar múltiplos lançamentos de teste
async function createTestLancamentos(count: number) {
  const lancamentoIds: string[] = [];

  // Obter uma obra para usar nos testes
  const { data: obra } = await supabase
    .from('obras')
    .select('id')
    .limit(1)
    .single();

  if (!obra) {
    throw new Error('Nenhuma obra encontrada para teste');
  }

  // Criar lançamentos de teste
  for (let i = 0; i < count; i++) {
    try {
      // Criar lançamento
      const { data: lancamento, error } = await supabase
        .from('lancamentos')
        .insert({
          obra_id: obra.id,
          tipo_lancamento: 'terceiros',
          valor_total: 100 + i,
          data_competencia: new Date().toISOString().split('T')[0],
          forma_pagamento: 'pix',
          descricao: `Lançamento de teste para exclusão em massa #${i + 1}`,
          status: 'Em aberto'
        })
        .select()
        .single();

      if (error) {
        console.error(`Erro ao criar lançamento de teste #${i + 1}: ${error.message}`);
        continue;
      }

      // Criar parcela
      const { error: parcelaError } = await supabase
        .from('parcelas')
        .insert({
          lancamento_id: lancamento.id,
          numero: 1,
          valor: 100 + i,
          vencimento: new Date().toISOString().split('T')[0],
          status: 'pendente'
        });

      if (parcelaError) {
        console.error(`Erro ao criar parcela para lançamento #${i + 1}: ${parcelaError.message}`);
        continue;
      }

      lancamentoIds.push(lancamento.id);
    } catch (error: any) {
      console.error(`Erro ao criar lançamento #${i + 1}: ${error.message}`);
    }
  }

  return lancamentoIds;
}

// Função para testar a exclusão em massa de lançamentos
async function testBulkDeleteLancamentos() {
  try {
    // Criar lançamentos de teste (5 lançamentos)
    const lancamentoIds = await createTestLancamentos(5);
    console.log(`${lancamentoIds.length} lançamentos de teste criados com IDs: ${lancamentoIds.join(', ')}`);

    if (lancamentoIds.length === 0) {
      throw new Error('Nenhum lançamento de teste foi criado');
    }

    // Verificar se os lançamentos foram criados
    const { data: lancamentos, error: getLancamentosError } = await supabase
      .from('lancamentos')
      .select('*')
      .in('id', lancamentoIds);

    if (getLancamentosError) {
      throw new Error(`Erro ao verificar lançamentos: ${getLancamentosError.message}`);
    }

    console.log(`${lancamentos.length} lançamentos encontrados`);

    // Verificar se as parcelas foram criadas
    const { data: parcelas, error: getParcelasError } = await supabase
      .from('parcelas')
      .select('*')
      .in('lancamento_id', lancamentoIds);

    if (getParcelasError) {
      throw new Error(`Erro ao verificar parcelas: ${getParcelasError.message}`);
    }

    console.log(`${parcelas.length} parcelas encontradas para os lançamentos`);

    // Testar a função RPC para excluir múltiplos lançamentos
    console.log('Testando exclusão em massa via RPC...');
    const { data: deleteResults, error: deleteError } = await supabase.rpc(
      'delete_many_lancamentos',
      { p_ids: lancamentoIds }
    );

    if (deleteError) {
      throw new Error(`Erro ao excluir lançamentos via RPC: ${deleteError.message}`);
    }

    console.log('Resultados da exclusão em massa:', deleteResults);

    // Verificar se os lançamentos foram excluídos
    const { data: lancamentosAposExclusao, error: checkError } = await supabase
      .from('lancamentos')
      .select('*')
      .in('id', lancamentoIds);

    if (checkError) {
      throw new Error(`Erro ao verificar exclusão: ${checkError.message}`);
    }

    if (lancamentosAposExclusao && lancamentosAposExclusao.length > 0) {
      console.warn(`${lancamentosAposExclusao.length} lançamentos não foram excluídos corretamente`);
      console.log('IDs não excluídos:', lancamentosAposExclusao.map(l => l.id).join(', '));
    } else {
      console.log('Todos os lançamentos foram excluídos com sucesso!');
    }

    // Verificar se as parcelas foram excluídas
    const { data: parcelasAposExclusao, error: checkParcelasError } = await supabase
      .from('parcelas')
      .select('*')
      .in('lancamento_id', lancamentoIds);

    if (checkParcelasError) {
      throw new Error(`Erro ao verificar exclusão de parcelas: ${checkParcelasError.message}`);
    }

    if (parcelasAposExclusao && parcelasAposExclusao.length > 0) {
      console.warn(`${parcelasAposExclusao.length} parcelas não foram excluídas corretamente`);
    } else {
      console.log('Todas as parcelas foram excluídas com sucesso!');
    }
    
    return { 
      success: true, 
      message: 'Teste de exclusão em massa concluído',
      totalCreated: lancamentoIds.length,
      totalDeleted: lancamentoIds.length - (lancamentosAposExclusao?.length || 0)
    };
  } catch (error: any) {
    console.error('Erro no teste de exclusão em massa:', error);
    return { success: false, message: error.message };
  }
}

// Executar o teste
testBulkDeleteLancamentos().then(result => {
  console.log('Resultado do teste:', result);
});
