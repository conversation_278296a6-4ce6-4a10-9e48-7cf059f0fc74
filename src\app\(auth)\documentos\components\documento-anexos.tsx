'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { DocumentoAnexo } from '@/types/documentos';
import { getDocumentoAnexos, uploadDocumentoAnexo, excluirDocumentoAnexo } from '@/services/documento-anexos';
import { FileText, Image, Upload, X, Loader2, FileSpreadsheet, FileArchive, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { FilePreview } from '@/components/FilePreview';
import { useAuth } from '@/hooks/useAuth';

interface DocumentoAnexosProps {
  documentoId: string;
  disabled?: boolean;
}

export function DocumentoAnexos({ documentoId, disabled = false }: DocumentoAnexosProps) {
  const [anexos, setAnexos] = useState<DocumentoAnexo[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewFile, setPreviewFile] = useState<{ url?: string; name?: string; type?: string }>({});

  // Usar o hook de autenticação
  const { isAuthenticated, isLoading: authLoading, refreshAuth, checkAuth } = useAuth();

  // Carregar anexos ao montar o componente e quando a autenticação estiver pronta
  useEffect(() => {
    if (documentoId && isAuthenticated && !authLoading) {
      loadAnexos();
    }
  }, [documentoId, isAuthenticated, authLoading]);

  const loadAnexos = async () => {
    try {
      setLoading(true);
      setAuthError(false); // Resetar o estado de erro de autenticação
      setErrorMessage('');

      // Verificar se o usuário está autenticado antes de carregar os anexos
      if (!isAuthenticated) {
        // Tentar atualizar a sessão
        const refreshed = await refreshAuth();
        if (!refreshed) {
          setAuthError(true);
          setErrorMessage('Você precisa estar autenticado para visualizar os anexos.');
          return;
        }
      }

      // Buscar os anexos diretamente do Supabase
      const data = await getDocumentoAnexos(documentoId);
      setAnexos(data);
    } catch (error: any) {

// Definir o estado de erro
      setAuthError(true);
      setErrorMessage(error.message || 'Erro ao carregar anexos');

      // Mostrar toast apenas para erros não relacionados à autenticação
      if (!error.message?.includes('autenticação') &&
          !error.message?.includes('auth') &&
          !error.message?.includes('JWT') &&
          !error.message?.includes('token') &&
          !error.message?.includes('session')) {
        toast.error('Erro ao carregar anexos');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    try {
      setUploading(true);
      setAuthError(false); // Resetar o estado de erro de autenticação
      setErrorMessage('');

      // Verificar se o usuário está autenticado antes de fazer upload
      if (!isAuthenticated) {
        setAuthError(true);
        setErrorMessage('Você precisa estar autenticado para adicionar anexos.');
        return;
      }

      // Upload de cada arquivo
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        await uploadDocumentoAnexo(file, documentoId);
      }

      // Recarregar a lista de anexos
      await loadAnexos();
      toast.success('Anexo(s) adicionado(s) com sucesso');
    } catch (error: any) {

// Definir o estado de erro
      setAuthError(true);
      setErrorMessage(error.message || 'Erro ao adicionar anexo');

      // Mostrar toast para todos os erros
      toast.error(error.message || 'Erro ao adicionar anexo');
    } finally {
      setUploading(false);
      // Limpar o input de arquivo
      e.target.value = '';
    }
  };

  const handleDelete = async (anexoId: string) => {
    try {
      setAuthError(false); // Resetar o estado de erro de autenticação
      setErrorMessage('');

      // Verificar se o usuário está autenticado antes de excluir
      if (!isAuthenticated) {
        setAuthError(true);
        setErrorMessage('Você precisa estar autenticado para excluir anexos.');
        return;
      }

      await excluirDocumentoAnexo(anexoId);
      // Atualizar a lista de anexos localmente
      setAnexos(anexos.filter(anexo => anexo.id !== anexoId));
      toast.success('Anexo excluído com sucesso');
    } catch (error: any) {

// Definir o estado de erro
      setAuthError(true);
      setErrorMessage(error.message || 'Erro ao excluir anexo');

      // Mostrar toast para todos os erros
      toast.error(error.message || 'Erro ao excluir anexo');
    }
  };

  const handlePreview = (anexo: DocumentoAnexo) => {
    setPreviewFile({
      url: anexo.arquivo_url,
      name: anexo.nome,
      type: anexo.tipo_arquivo
    });
    setPreviewOpen(true);
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) {
      return <FileText className="h-5 w-5 text-red-500" />;
    } else if (fileType.includes('image')) {
      return <Image className="h-5 w-5 text-blue-500" />;
    } else if (fileType.includes('spreadsheet') || fileType.includes('excel') || fileType.includes('csv')) {
      return <FileSpreadsheet className="h-5 w-5 text-green-500" />;
    } else if (fileType.includes('word') || fileType.includes('document')) {
      return <FileText className="h-5 w-5 text-blue-700" />;
    } else {
      return <FileArchive className="h-5 w-5 text-gray-500" />;
    }
  };

  // Estado para controlar erros de autenticação
  const [authError, setAuthError] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Função para tentar novamente após erro de autenticação
  const handleRetryAfterAuthError = async () => {
    setAuthError(false);
    setErrorMessage('');

    // Tentar atualizar a sessão antes de carregar os anexos
    const refreshed = await refreshAuth();
    if (refreshed) {
      loadAnexos();
    } else {
      // Se não conseguir atualizar a sessão, verificar a autenticação novamente
      await checkAuth();
      if (isAuthenticated) {
        loadAnexos();
      } else {
        setAuthError(true);
        setErrorMessage('Não foi possível autenticar. Por favor, recarregue a página.');
      }
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium">Anexos Adicionais</h3>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleRetryAfterAuthError}
            title="Recarregar anexos"
            disabled={loading || authLoading}
            className="h-8 w-8"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
        {!disabled && !authError && isAuthenticated && !authLoading && (
          <div className="relative">
            <input
              type="file"
              id="file-upload"
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              onChange={handleFileChange}
              multiple
              disabled={uploading}
              aria-label="Selecionar arquivos para upload"
              title="Selecionar arquivos para upload"
            />
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
              disabled={uploading}
            >
              {uploading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Enviando...</span>
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4" />
                  <span>Adicionar Anexo</span>
                </>
              )}
            </Button>
          </div>
        )}
      </div>

      {loading || authLoading ? (
        <div className="flex items-center justify-center p-4">
          <Loader2 className="h-5 w-5 animate-spin mr-2" />
          <span>{authLoading ? 'Verificando autenticação...' : 'Carregando anexos...'}</span>
        </div>
      ) : !isAuthenticated ? (
        <div className="text-center p-4 border rounded-md bg-muted/50">
          <p className="text-sm text-destructive mb-2">Erro de autenticação</p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="flex items-center gap-2 mx-auto"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Recarregar página</span>
          </Button>
        </div>
      ) : authError ? (
        <div className="text-center p-4 border rounded-md bg-muted/50">
          <p className="text-sm text-destructive mb-2">{errorMessage || 'Erro ao carregar anexos'}</p>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRetryAfterAuthError}
            className="flex items-center gap-2 mx-auto"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Tentar novamente</span>
          </Button>
        </div>
      ) : anexos.length === 0 ? (
        <div className="text-center p-4 border rounded-md bg-muted/50">
          <p className="text-sm text-muted-foreground">Nenhum anexo adicional</p>
        </div>
      ) : (
        <div className="border rounded-md divide-y">
          {anexos.map((anexo) => (
            <div key={anexo.id} className="flex items-center justify-between p-3">
              <div className="flex items-center space-x-3">
                {getFileIcon(anexo.tipo_arquivo)}
                <div>
                  <p className="text-sm font-medium truncate max-w-[200px] md:max-w-[400px]">
                    {anexo.nome}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {new Date(anexo.created_at).toLocaleDateString('pt-BR')}
                  </p>
                </div>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handlePreview(anexo)}
                  title="Visualizar anexo"
                >
                  <FileText className="h-4 w-4" />
                </Button>
                {!disabled && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDelete(anexo.id)}
                    title="Excluir anexo"
                    className="text-destructive hover:text-destructive"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Componente de preview de arquivo */}
      <FilePreview
        file={previewFile}
        open={previewOpen}
        onOpenChange={setPreviewOpen}
      />
    </div>
  );
}