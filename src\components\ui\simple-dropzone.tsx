'use client';

import { useState, useRef, useEffect } from 'react';
import { Upload, FileText, CheckCircle2, AlertCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { uploadDocumento, getDocumentoById } from '@/services/documentos';
import { analisarDocumento } from '@/services/ai-analysis';
import { supabase } from '@/lib/supabase/client';

interface SimpleDropzoneProps {
  isVisible: boolean;
  onDrop: (files: FileList) => void;
  className?: string;
  onUploadComplete?: () => void;
}

interface ProcessingFile {
  file: File;
  status: 'uploading' | 'analyzing' | 'completed' | 'error';
  progress: number;
  error?: string;
}

export function SimpleDropzone({ isVisible, onDrop, className, onUploadComplete }: SimpleDropzoneProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [processingFiles, setProcessingFiles] = useState<ProcessingFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const dropzoneRef = useRef<HTMLDivElement>(null);

  console.log('🎯 SimpleDropzone: Renderizado com isVisible =', isVisible);

  // Adicionar listener global para capturar drops quando visível
  useEffect(() => {
    if (!isVisible) return;

    const handleGlobalDrop = (e: DragEvent) => {
      console.log('🎯 SimpleDropzone: Global drop capturado!');
      e.preventDefault();
      e.stopPropagation();
      
      if (e.dataTransfer?.files) {
        const files = Array.from(e.dataTransfer.files);
        console.log('🎯 SimpleDropzone: Processando', files.length, 'arquivos via global listener');
        
        // Simular evento React
        const syntheticEvent = {
          preventDefault: () => {},
          stopPropagation: () => {},
          dataTransfer: { files: e.dataTransfer.files }
        } as React.DragEvent;
        
        handleDrop(syntheticEvent);
      }
    };

    document.addEventListener('drop', handleGlobalDrop, true);
    
    return () => {
      document.removeEventListener('drop', handleGlobalDrop, true);
    };
  }, [isVisible]);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('🎯 SimpleDropzone: dragOver');
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('🎯 SimpleDropzone: dragLeave');
    
    // Só sair do drag over se realmente saiu do dropzone
    if (dropzoneRef.current && !dropzoneRef.current.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    console.log('🎯 SimpleDropzone: DROP EVENT!', e.dataTransfer.files.length, 'arquivos');
    console.log('🎯 SimpleDropzone: Event target:', e.target);
    console.log('🎯 SimpleDropzone: Current target:', e.currentTarget);

    const droppedFiles = Array.from(e.dataTransfer.files);
    
    // Filtrar apenas arquivos válidos
    const validFiles = droppedFiles.filter(file => {
      const isValid = 
        file.type === 'application/pdf' ||
        file.type === 'image/png' ||
        file.type === 'image/jpeg';

      if (!isValid) {
        toast.error(`Arquivo não suportado: ${file.name}. Apenas PDF, PNG e JPEG são aceitos.`);
      }

      return isValid;
    });

    console.log('🎯 SimpleDropzone: Arquivos válidos:', validFiles.length);

    if (validFiles.length === 0) return;

    // Chamar callback original
    onDrop(e.dataTransfer.files);

    // Processar upload
    await processUpload(validFiles);
  };

  const processUpload = async (files: File[]) => {
    setIsProcessing(true);
    
    const processingItems: ProcessingFile[] = files.map(file => ({
      file,
      status: 'uploading',
      progress: 0
    }));

    setProcessingFiles(processingItems);

    try {
      // Obter usuário atual
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Usuário não autenticado');
      }

      const userId = session.user.id;
      const uploadedDocuments = [];

      // Processar cada arquivo
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        try {
          // Fase 1: Upload
          setProcessingFiles(prev => prev.map((item, idx) => 
            idx === i ? { ...item, status: 'uploading', progress: 25 } : item
          ));

          const documento = await uploadDocumento(file, userId);
          uploadedDocuments.push(documento);

          // Fase 2: Análise
          setProcessingFiles(prev => prev.map((item, idx) => 
            idx === i ? { ...item, status: 'analyzing', progress: 75 } : item
          ));

          // Buscar o documento completo para obter a URL
          const documentoCompleto = await getDocumentoById(documento.id);
          if (documentoCompleto && documentoCompleto.arquivo_url) {
            await analisarDocumento(documentoCompleto.arquivo_url);
          }

          // Concluído
          setProcessingFiles(prev => prev.map((item, idx) => 
            idx === i ? { ...item, status: 'completed', progress: 100 } : item
          ));

        } catch (error) {
          console.error(`Erro ao processar ${file.name}:`, error);
          setProcessingFiles(prev => prev.map((item, idx) => 
            idx === i ? { 
              ...item, 
              status: 'error', 
              error: error instanceof Error ? error.message : 'Erro desconhecido' 
            } : item
          ));
        }
      }

      if (uploadedDocuments.length > 0) {
        toast.success(`${uploadedDocuments.length} documento(s) processado(s) com sucesso!`);
        onUploadComplete?.();
      }

    } catch (error) {
      console.error('Erro no processamento:', error);
      toast.error('Erro ao processar arquivos');
    } finally {
      setIsProcessing(false);
      // Limpar lista após 3 segundos
      setTimeout(() => setProcessingFiles([]), 3000);
    }
  };

  const getStatusIcon = (status: ProcessingFile['status']) => {
    switch (status) {
      case 'uploading':
      case 'analyzing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: ProcessingFile['status']) => {
    switch (status) {
      case 'uploading':
        return 'Enviando...';
      case 'analyzing':
        return 'Analisando...';
      case 'completed':
        return 'Concluído';
      case 'error':
        return 'Erro';
      default:
        return '';
    }
  };

  if (!isVisible) return null;

  return (
    <div
      ref={dropzoneRef}
      className={cn(
        "fixed inset-4 z-[99999] bg-blue-50/90 backdrop-blur-sm border-4 border-dashed border-blue-400 rounded-lg",
        "flex flex-col items-center justify-center transition-all duration-200",
        "pointer-events-auto animate-pulse-border-drawer",
        isDragOver 
          ? "border-blue-500 bg-blue-100/90 scale-[1.02]" 
          : "border-blue-400",
        className
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <div className="text-center space-y-4">
        <div className={cn(
          "mx-auto w-16 h-16 rounded-full flex items-center justify-center transition-colors",
          isDragOver ? "bg-primary text-primary-foreground" : "bg-muted"
        )}>
          {isDragOver ? (
            <FileText className="w-8 h-8" />
          ) : (
            <Upload className="w-8 h-8" />
          )}
        </div>
        
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">
            {isDragOver ? "Solte os arquivos aqui" : "Arraste arquivos aqui"}
          </h3>
          <p className="text-sm text-muted-foreground">
            {isDragOver 
              ? "Solte para fazer upload dos documentos" 
              : "Arraste documentos para fazer upload"
            }
          </p>
        </div>

        {/* Lista de arquivos sendo processados */}
        {processingFiles.length > 0 && (
          <div className="mt-6 space-y-2 max-w-md">
            {processingFiles.map((item, index) => (
              <div key={index} className="flex items-center gap-3 p-3 bg-white/80 rounded-lg">
                <FileText className="h-5 w-5 text-gray-500 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {item.file.name}
                  </p>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(item.status)}
                    <span className="text-xs text-gray-600">
                      {getStatusText(item.status)}
                    </span>
                    {item.error && (
                      <span className="text-xs text-red-600 truncate">
                        {item.error}
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-xs text-gray-500">
                  {item.progress}%
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 