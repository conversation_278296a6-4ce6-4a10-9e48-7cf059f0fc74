@tailwind base;
@tailwind components;
@tailwind utilities;

@import '../styles/pdf-viewer.css';

:root {
  --primary: #000000;
  --primary-foreground: #ffffff;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 35.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Componentes e classes utilitárias personalizadas */
@layer components {
  .content-container {
    @apply px-2; /* Espaçamento padrão para os containers de conteúdo */
  }

  .content-padding {
    @apply p-6; /* Padding padrão para conteúdos */
  }

  .page-container {
    @apply space-y-6; /* Espaçamento vertical entre seções */
  }

  /* Estilos para colunas fixas em tabelas */
  .sticky-column {
    @apply sticky right-0 z-20 bg-background shadow-[-4px_0_8px_rgba(0,0,0,0.1)];
  }

  /* Estilos para tabelas com scroll */
  .table-container {
    position: relative;
    width: 100%;
    overflow: auto;
  }

  /* Estilos para cabeçalhos fixos */
  .sticky-header {
    position: sticky !important;
    right: 0 !important;
    z-index: 20 !important;
    background-color: hsl(var(--muted)) !important;
    width: 60px !important;
    text-align: center !important;
  }

  /* Garantir que o cabeçalho da coluna de ações tenha a cor correta */
  thead tr th[data-sticky="true"] {
    background-color: hsl(var(--muted)) !important;
  }

  /* Estilos para células fixas */
  .sticky-cell {
    position: sticky !important;
    right: 0 !important;
    z-index: 20 !important;
    background-color: hsl(var(--background)) !important;
    width: 60px !important;
    text-align: center !important;
    opacity: 1 !important;
    background: hsl(var(--background)) !important;
  }

  /* Garantir que a célula de ações mantenha o fundo sólido durante o hover */
  tr:hover .sticky-cell,
  tr:hover td[data-sticky="true"],
  .sticky-cell,
  td[data-sticky="true"] {
    background-color: hsl(var(--background)) !important;
    opacity: 1 !important;
    -webkit-backdrop-filter: none !important;
    backdrop-filter: none !important;
  }

  /* Sobrescrever qualquer estilo de hover que possa afetar a opacidade */
  tr:hover .sticky-cell *,
  tr:hover td[data-sticky="true"] *,
  .sticky-cell *,
  td[data-sticky="true"] * {
    opacity: 1 !important;
  }

  /* Garantir que a célula de ações nunca fique transparente */
  tr:hover td.sticky-cell,
  tr:hover td[data-sticky="true"],
  td.sticky-cell,
  td[data-sticky="true"] {
    background-color: hsl(var(--background)) !important;
    opacity: 1 !important;
    -webkit-backdrop-filter: none !important;
    backdrop-filter: none !important;
  }

  /* Estilo específico para a coluna de ações */
  .sticky-cell,
  [data-sticky="true"] {
    background: hsl(var(--background)) !important;
  }

  /* Garantir que a coluna de ações não fique transparente durante o hover */
  tr:hover .sticky-cell,
  tr:hover [data-sticky="true"] {
    background: hsl(var(--background)) !important;
    opacity: 1 !important;
  }

  /* Estilo para o cabeçalho da coluna de ações */
  thead th[sticky] {
    background-color: hsl(var(--muted)) !important;
  }

  /* Estilo para o cabeçalho da coluna de ações em tabelas aninhadas */
  tr.bg-muted\/40 th[sticky] {
    background-color: hsl(var(--muted)/40) !important;
  }

  /* Garantir que todos os cabeçalhos em tabelas aninhadas tenham o mesmo fundo */
  tr.bg-muted\/40 th {
    background-color: hsl(var(--muted)/40) !important;
  }

  /* Estilos para sombra e borda quando há scroll */
  .table-container::-webkit-scrollbar {
    height: 8px;
  }

  .table-container::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border));
    border-radius: 4px;
  }

  /* Detecta quando há scroll horizontal */
  .table-container.has-scroll .sticky-header,
  .table-container.has-scroll .sticky-cell {
    border-left: 1px solid hsl(var(--border)) !important;
    box-shadow: -4px 0 8px rgba(0,0,0,0.1) !important;
  }

  /* Quando não há scroll, não mostra borda nem sombra */
  .table-container:not(.has-scroll) .sticky-header,
  .table-container:not(.has-scroll) .sticky-cell {
    border-left: none !important;
    box-shadow: none !important;
  }

  /* Estilos para células fixas em linhas com hover */
  tr:hover td[data-sticky="true"] {
    background-color: hsl(var(--background)) !important;
  }

  /* Estilos para linhas selecionadas com células fixas */
  tr[data-state="selected"] td[data-sticky="true"] {
    background-color: hsl(var(--background)) !important;
  }

  /* Estilos para a última coluna da tabela */
  .table-last-column {
    @apply border-l border-border;
  }
}

.container {
  @apply min-h-screen grid place-items-center;
}

.main {
  @apply flex flex-col items-center gap-8 p-8;
}

.list {
  @apply text-center p-4 list-none;
}

.code {
  @apply bg-gray-50 rounded-lg p-3 font-mono;
}

.buttonContainer {
  @apply flex gap-4 items-center mt-8;
}

.primaryButton {
  @apply flex items-center gap-2 px-6 py-3 rounded-lg border border-gray-200
         bg-black text-white transition-all duration-200 hover:bg-gray-800;
}

.secondaryButton {
  @apply flex items-center gap-2 px-6 py-3 rounded-lg border border-gray-200
         transition-all duration-200 hover:bg-gray-50;
}

.footer {
  @apply flex justify-center items-center gap-8 p-8 border-t border-gray-200;
}

.footerLink {
  @apply flex items-center gap-2 text-gray-600 transition-colors duration-200 hover:text-black;
}

.progress-bar {
  width: var(--progress);
}

/* Glowing border animation */
@keyframes pulse-blue {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.glow-border-blue {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.7);
  animation: pulse-blue 2s infinite;
}

/* Fade animations */
@keyframes fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fade-out {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(10px); }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-in-out;
}

.animate-fade-out {
  animation: fade-out 0.5s ease-in-out;
}

/* Shimmer animation for glowing effects */
@keyframes shimmer {
  0% { background-position: 0% 0; }
  100% { background-position: 200% 0; }
}

.shimmer-effect {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.3) 50%, rgba(59, 130, 246, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite linear;
}

/* Floating animation */
@keyframes floating {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0px); }
}

.animate-float {
  animation: floating 3s ease-in-out infinite;
}

/* Slow bounce animation */
@keyframes bounce-slow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8px); }
}

.animate-bounce-slow {
  animation: bounce-slow 2s infinite;
}

/* Slow spin animation */
@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin-slow {
  animation: spin-slow 4s linear infinite;
}

/* Glow pulse animation */
@keyframes glow-pulse {
  0% { box-shadow: 0 0 5px 0px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px 5px rgba(59, 130, 246, 0.7); }
  100% { box-shadow: 0 0 5px 0px rgba(59, 130, 246, 0.5); }
}

.animate-glow-pulse {
  animation: glow-pulse 3s infinite;
}

/* Progress glow animation */
@keyframes progress-glow {
  0% { filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.5)); }
  50% { filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.8)); }
  100% { filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.5)); }
}

.progress-glow {
  animation: progress-glow 2s infinite;
}

/* Typing animation */
@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

.animate-typing {
  overflow: hidden;
  white-space: nowrap;
  animation: typing 1s steps(30, end);
}

/* Confetti animation */
@keyframes confetti-slow {
  0% { transform: translate3d(0, 0, 0) rotateX(0) rotateY(0); }
  100% { transform: translate3d(25px, 105px, 0) rotateX(360deg) rotateY(180deg); }
}

.animate-confetti {
  animation: confetti-slow 3s ease-in-out infinite;
}

/* Enhanced document notification shadow */
.document-notification-shadow {
  box-shadow: 0 0 25px rgba(59, 130, 246, 0.4), 0 10px 20px rgba(0, 0, 0, 0.15);
}

/* Progress width utility */
.progress-width {
  width: var(--progress-width, 0%);
}

/* Animation delay utilities */
.delay-500 {
  animation-delay: 0.5s;
}

/* Animação de borda rotativa */
@keyframes border-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Animação de borda pulsante */
@keyframes border-pulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

/* Animação de borda deslizante */
@keyframes border-flow {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Animação de brilho */
@keyframes glow {
  0%, 100% {
    filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.8));
  }
}

/* Animação de borda em movimento - Versão original */
.processing-border {
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
}

.processing-border::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  padding: 2px; /* Borda mais grossa */
  background: conic-gradient(
    from 0deg,
    rgba(59, 130, 246, 0),
    rgba(59, 130, 246, 1),
    rgba(99, 102, 241, 1),
    rgba(59, 130, 246, 0)
  );
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  animation: border-spin 3s linear infinite;
  pointer-events: none;
  z-index: 1;
}

/* Versão alternativa com borda mais brilhante */
.processing-border::after {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(
    90deg,
    rgba(59, 130, 246, 0) 0%,
    rgba(59, 130, 246, 0.8) 50%,
    rgba(59, 130, 246, 0) 100%
  );
  background-size: 200% 100%;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  animation: border-flow 2s ease-in-out infinite;
  pointer-events: none;
  opacity: 0.8;
  z-index: 2;
}

/* Estilos para o sidebar quando minimizado */
.sidebar-menu-item {
  position: relative;
}

.sidebar-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Garantir que o submenu apareça na frente de todos os elementos */
aside nav div[class*="fixed"],
aside nav div[class*="absolute"] {
  z-index: 9999 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
  min-width: 180px !important;
  background-color: white !important;
  position: fixed !important;
}

/* Estilo específico para o submenu do sidebar quando está expandido (inline) */
.sidebar-submenu-inline {
  position: relative !important;
  z-index: 10 !important;
  background-color: transparent !important;
  margin-top: 0 !important;
}

/* Estilo específico para o submenu do sidebar quando está recolhido (floating) */
.sidebar-submenu-floating {
  position: fixed !important;
  z-index: 9999 !important;
  background-color: white !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  border-radius: 8px !important;
  padding: 8px !important;
  min-width: 200px !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  margin-top: 0 !important;
  animation: fadeIn 0.2s ease-in-out !important;
}

/* Mantendo para compatibilidade com código existente */
.sidebar-submenu {
  position: relative !important;
  z-index: 10 !important;
  background-color: transparent !important;
  margin-top: 0 !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Estilo para o container do item do menu */
.sidebar-menu-item {
  position: relative !important;
}

/* Estilo para o submenu quando o sidebar está minimizado */
@media (max-width: 768px) {
  .sidebar-submenu-floating {
    left: 50px !important;
  }
}

/* Animações para os componentes de teste visual */

/* Borda moderna */
.modern-border {
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(59, 130, 246, 0.15);
}

.modern-border::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  padding: 2px;
  background: conic-gradient(
    from 0deg,
    rgba(59, 130, 246, 0),
    rgba(59, 130, 246, 1),
    rgba(99, 102, 241, 1),
    rgba(59, 130, 246, 0)
  );
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  animation: border-spin 4s linear infinite;
  pointer-events: none;
}

/* Borda minimalista */
.minimal-border {
  position: relative;
  transition: all 0.3s ease;
}

.minimal-border::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  height: 1px;
  width: 0;
  background: linear-gradient(to right, #e5e7eb, #3b82f6, #e5e7eb);
  animation: minimal-border-flow 2s ease-in-out infinite;
}

@keyframes minimal-border-flow {
  0% {
    width: 0;
    left: 0;
  }
  50% {
    width: 100%;
    left: 0;
  }
  100% {
    width: 0;
    left: 100%;
  }
}

/* Borda com efeito de vidro */
.glass-border {
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
}

.glass-border::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1.5px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1),
    rgba(59, 130, 246, 0.5),
    rgba(139, 92, 246, 0.5),
    rgba(255, 255, 255, 0.1)
  );
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  animation: glass-border-flow 3s linear infinite;
  pointer-events: none;
}

@keyframes glass-border-flow {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 200% 50%;
  }
}

/* Card flutuante */
.card-float {
  animation: card-float 3s ease-in-out infinite;
  box-shadow:
    0 10px 25px -5px rgba(59, 130, 246, 0.1),
    0 8px 10px -6px rgba(59, 130, 246, 0.1);
}

@keyframes card-float {
  0%, 100% {
    transform: translateY(0);
    box-shadow:
      0 10px 25px -5px rgba(59, 130, 246, 0.1),
      0 8px 10px -6px rgba(59, 130, 246, 0.1);
  }
  50% {
    transform: translateY(-5px);
    box-shadow:
      0 15px 30px -10px rgba(59, 130, 246, 0.2),
      0 10px 15px -8px rgba(59, 130, 246, 0.2);
  }
}

/* Efeito de digitação */
.typing-effect {
  border-right: 2px solid transparent;
  animation: typing 1s steps(40) 1, blink 1s step-end infinite;
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink {
  from, to { border-color: transparent }
  50% { border-color: #3b82f6 }
}

/* Animação de conclusão suave */
@keyframes completion {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-completion {
  animation: completion 0.6s ease-out forwards;
}

/* Variante 1 - Cantos Arredondados */
.variant1-card {
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.7);
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.08),
    0 1px 8px rgba(0, 0, 0, 0.03);
  transition: all 0.5s ease;
}

.variant1-card::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(
    90deg,
    rgba(59, 130, 246, 0.5),
    rgba(147, 51, 234, 0.5),
    rgba(59, 130, 246, 0.5)
  );
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0.7;
  transition: opacity 0.5s ease;
}

/* Variante 2 - Cartão Flutuante */
.variant2-card {
  position: relative;
  overflow: hidden;
  background: white;
  border-radius: 16px;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.1),
    0 1px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.5s ease;
  animation: variant2-float 6s ease-in-out infinite;
}

@keyframes variant2-float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    box-shadow:
      0 10px 30px rgba(0, 0, 0, 0.1),
      0 1px 8px rgba(0, 0, 0, 0.05);
  }
  25% {
    transform: translateY(-5px) rotate(0.5deg);
    box-shadow:
      0 15px 35px rgba(0, 0, 0, 0.12),
      0 2px 10px rgba(0, 0, 0, 0.06);
  }
  50% {
    transform: translateY(-8px) rotate(-0.5deg);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.14),
      0 3px 12px rgba(0, 0, 0, 0.07);
  }
  75% {
    transform: translateY(-5px) rotate(0.25deg);
    box-shadow:
      0 15px 35px rgba(0, 0, 0, 0.12),
      0 2px 10px rgba(0, 0, 0, 0.06);
  }
}

.variant2-shadow-pulse {
  animation: variant2-shadow-pulse 4s ease-in-out infinite;
}

@keyframes variant2-shadow-pulse {
  0%, 100% {
    box-shadow: 0 0 0 rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

/* Variante 3 - Glassmorphism */
.variant3-card {
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.5s ease;
}

.variant3-card::before {
  content: "";
  position: absolute;
  inset: 0;
  background: radial-gradient(
    circle at 50% 0%,
    rgba(255, 255, 255, 0.3),
    transparent 70%
  );
  opacity: 0.6;
}

.variant3-glass-effect {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  overflow: hidden;
}

.variant3-glass-effect::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 100%
  );
  z-index: 0;
}

.variant3-glow {
  position: absolute;
  width: 150px;
  height: 150px;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.3) 0%,
    transparent 70%
  );
  border-radius: 100%;
  filter: blur(20px);
  animation: variant3-glow-move 8s infinite alternate ease-in-out;
  opacity: 0.5;
}

@keyframes variant3-glow-move {
  0% {
    top: -50px;
    left: -50px;
    background: radial-gradient(
      circle,
      rgba(59, 130, 246, 0.3) 0%,
      transparent 70%
    );
  }
  50% {
    top: 50%;
    left: 50%;
    background: radial-gradient(
      circle,
      rgba(139, 92, 246, 0.3) 0%,
      transparent 70%
    );
  }
  100% {
    top: 80%;
    right: -50px;
    background: radial-gradient(
      circle,
      rgba(236, 72, 153, 0.3) 0%,
      transparent 70%
    );
  }
}

/* Variante 4 - Neomorfismo */
.variant4-card {
  position: relative;
  overflow: hidden;
  background: #f0f4f8;
  border-radius: 16px;
  box-shadow:
    12px 12px 24px rgba(166, 180, 200, 0.7),
    -12px -12px 24px rgba(255, 255, 255, 0.8);
  transition: all 0.5s ease;
}

.variant4-neomorph-inset {
  background: #f0f4f8;
  border-radius: 12px;
  box-shadow:
    inset 5px 5px 10px rgba(166, 180, 200, 0.5),
    inset -5px -5px 10px rgba(255, 255, 255, 0.8);
}

.variant4-neomorph-button {
  background: #f0f4f8;
  border-radius: 50%;
  box-shadow:
    5px 5px 10px rgba(166, 180, 200, 0.5),
    -5px -5px 10px rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
}

.variant4-neomorph-button:hover {
  box-shadow:
    3px 3px 6px rgba(166, 180, 200, 0.5),
    -3px -3px 6px rgba(255, 255, 255, 0.8);
}

.variant4-neomorph-button:active {
  box-shadow:
    inset 3px 3px 6px rgba(166, 180, 200, 0.5),
    inset -3px -3px 6px rgba(255, 255, 255, 0.8);
}

.variant4-progress-circle {
  transition: stroke-dashoffset 0.5s ease;
}

.variant4-progress-text {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
}

/* Variante 5 - Gradiente Animado */
.variant5-card {
  position: relative;
  overflow: hidden;
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.5s ease;
}

.variant5-gradient-bg {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    -45deg,
    #ee7752,
    #e73c7e,
    #23a6d5,
    #23d5ab
  );
  background-size: 400% 400%;
  opacity: 0.05;
  animation: variant5-gradient 15s ease infinite;
}

@keyframes variant5-gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.variant5-progress-bar {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
}

.variant5-progress-bar::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
  animation: variant5-shimmer 2s infinite;
}

@keyframes variant5-shimmer {
  100% {
    transform: translateX(100%);
  }
}

.variant5-step-item {
  position: relative;
  transition: all 0.3s ease;
}

.variant5-step-item.active {
  transform: translateX(4px);
}

.variant5-step-item.complete {
  opacity: 0.8;
}

.variant5-step-indicator {
  position: relative;
  transition: all 0.3s ease;
}

.variant5-step-indicator::before {
  content: "";
  position: absolute;
  inset: -4px;
  border-radius: 50%;
  opacity: 0;
  background: currentColor;
  transition: opacity 0.3s ease;
}

.variant5-step-indicator.active::before {
  opacity: 0.1;
  animation: variant5-pulse 2s infinite;
}

@keyframes variant5-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.2;
  }
}

/* Variante 6 - Pulso Animado */
.variant6-card {
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.8);
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.08),
    0 1px 6px rgba(0, 0, 0, 0.03);
  transition: all 0.5s ease;
}

.variant6-pulse {
  animation: variant6-pulse 2s infinite;
}

.variant6-pulse-complete {
  animation: variant6-pulse-complete 2s infinite;
}

.variant6-pulse-delay-1 {
  animation-delay: 0.5s;
}

.variant6-pulse-delay-2 {
  animation-delay: 1s;
}

@keyframes variant6-pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
}

@keyframes variant6-pulse-complete {
  0% {
    transform: scale(0.95);
    opacity: 0.5;
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
  }
  50% {
    transform: scale(1.05);
    opacity: 0.2;
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    transform: scale(0.95);
    opacity: 0.5;
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

/* Animação de contorno colorido para o círculo de loading da variante 6 */
@keyframes variant6-contour-spin {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -283; /* Aproximadamente o valor da circunferência */
  }
}

@keyframes variant6-contour-color {
  0% {
    stroke: rgba(59, 130, 246, 0.8); /* Azul translúcido */
  }
  25% {
    stroke: rgba(139, 92, 246, 0.8); /* Roxo translúcido */
  }
  50% {
    stroke: rgba(236, 72, 153, 0.8); /* Rosa translúcido */
  }
  75% {
    stroke: rgba(99, 102, 241, 0.8); /* Índigo translúcido */
  }
  100% {
    stroke: rgba(59, 130, 246, 0.8); /* Volta para azul translúcido */
  }
}

@keyframes variant6-glow-pulse {
  0%, 100% {
    filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.5)) blur(0.5px);
  }
  50% {
    filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.8)) blur(0.2px);
  }
}

.variant6-circle-contour {
  stroke-dasharray: 283;
  stroke-linecap: round;
  stroke-width: 4;
  animation:
    variant6-contour-spin 3s linear infinite,
    variant6-contour-color 6s linear infinite,
    variant6-glow-pulse 3s ease-in-out infinite;
  mix-blend-mode: screen;
}

.variant6-circle-contour-complete {
  stroke-dasharray: 283;
  stroke-linecap: round;
  stroke-width: 4;
  animation:
    variant6-contour-spin 3s linear infinite,
    variant6-glow-pulse 3s ease-in-out infinite;
  stroke: rgba(34, 197, 94, 0.8);
  mix-blend-mode: screen;
}

/* Variante 7 - Tema Escuro */
.variant7-card {
  position: relative;
  overflow: hidden;
  background: #1a1a2e;
  border-radius: 16px;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.3),
    0 1px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.5s ease;
  color: #e6e6e6;
}

.variant7-glow {
  position: absolute;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.15;
  z-index: 0;
}

.variant7-glow-blue {
  background: #4361ee;
  top: -20px;
  left: -20px;
}

.variant7-glow-purple {
  background: #7209b7;
  bottom: -20px;
  right: -20px;
}

.variant7-progress-bar {
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.variant7-progress-fill {
  height: 100%;
  border-radius: 8px;
  background: linear-gradient(90deg, #4361ee, #7209b7);
  position: relative;
  overflow: hidden;
}

.variant7-progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
  animation: variant7-shimmer 2s infinite;
}

@keyframes variant7-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}

.variant7-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.variant7-message-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

/* Variante 8 - Futurista */
.variant8-card {
  position: relative;
  overflow: hidden;
  background: #0f172a;
  border-radius: 16px;
  box-shadow:
    0 0 0 1px rgba(66, 153, 225, 0.1),
    0 10px 30px rgba(0, 0, 0, 0.2),
    0 0 15px rgba(66, 153, 225, 0.15);
  transition: all 0.5s ease;
  color: #e2e8f0;
}

.variant8-card::before {
  content: "";
  position: absolute;
  inset: 0;
  background:
    linear-gradient(90deg, transparent, rgba(66, 153, 225, 0.05), transparent),
    radial-gradient(circle at 50% 0%, rgba(66, 153, 225, 0.1), transparent 70%);
  z-index: 0;
}

.variant8-grid {
  position: absolute;
  inset: 0;
  background-image:
    linear-gradient(rgba(66, 153, 225, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(66, 153, 225, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: -1px -1px;
  -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
  mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
  z-index: 0;
}

.variant8-particles {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: 0;
}

.variant8-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background-color: rgba(66, 153, 225, 0.5);
  box-shadow: 0 0 4px rgba(66, 153, 225, 0.8);
  animation: variant8-particle-float 6s infinite;
}

@keyframes variant8-particle-float {
  0%, 100% {
    transform: translateY(0) translateX(0);
    opacity: 0;
  }
  25% {
    opacity: 1;
  }
  75% {
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) translateX(10px);
  }
}

.variant8-progress-track {
  position: relative;
  height: 4px;
  background: rgba(30, 41, 59, 0.8);
  border-radius: 2px;
  overflow: hidden;
  border: 1px solid rgba(66, 153, 225, 0.2);
}

.variant8-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  position: relative;
  border-radius: 2px;
}

.variant8-progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  height: 100%;
  background: rgba(66, 153, 225, 0.8);
  filter: blur(4px);
  border-radius: 2px;
}

.variant8-message-container {
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(66, 153, 225, 0.2);
  border-radius: 8px;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.variant8-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(66, 153, 225, 0.2);
  position: relative;
  overflow: hidden;
}

.variant8-icon-container::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    rgba(66, 153, 225, 0.3),
    transparent 30%
  );
  animation: variant8-rotate 4s linear infinite;
}

@keyframes variant8-rotate {
  100% {
    transform: rotate(360deg);
  }
}

.variant8-button {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(66, 153, 225, 0.3);
  color: #3b82f6;
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.variant8-button:hover {
  background: rgba(30, 41, 59, 1);
  border-color: rgba(66, 153, 225, 0.5);
  box-shadow: 0 0 8px rgba(66, 153, 225, 0.3);
}

/* Variante 8 Light 1 - Futurista Claro */
.variant8-light1-card {
  position: relative;
  overflow: hidden;
  background: #f8fafc;
  border-radius: 16px;
  box-shadow:
    0 0 0 1px rgba(59, 130, 246, 0.1),
    0 10px 30px rgba(0, 0, 0, 0.1),
    0 0 15px rgba(59, 130, 246, 0.1);
  transition: all 0.5s ease;
  color: #334155;
}

.variant8-light1-card::before {
  content: "";
  position: absolute;
  inset: 0;
  background:
    linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.03), transparent),
    radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.05), transparent 70%);
  z-index: 0;
}

.variant8-light1-grid {
  position: absolute;
  inset: 0;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: -1px -1px;
  -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
  mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
  z-index: 0;
}

.variant8-light1-particles {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: 0;
}

.variant8-light1-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 0 4px rgba(59, 130, 246, 0.5);
  animation: variant8-particle-float 6s infinite;
}

.variant8-light1-progress-track {
  position: relative;
  height: 4px;
  background: rgba(241, 245, 249, 0.8);
  border-radius: 2px;
  overflow: hidden;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.variant8-light1-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  position: relative;
  border-radius: 2px;
}

.variant8-light1-progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  height: 100%;
  background: rgba(59, 130, 246, 0.6);
  filter: blur(4px);
  border-radius: 2px;
}

.variant8-light1-message-container {
  background: rgba(241, 245, 249, 0.6);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.variant8-light1-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(241, 245, 249, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.variant8-light1-icon-container::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    rgba(59, 130, 246, 0.2),
    transparent 30%
  );
  animation: variant8-rotate 4s linear infinite;
}

.variant8-light1-button {
  background: rgba(241, 245, 249, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.variant8-light1-button:hover {
  background: rgba(241, 245, 249, 1);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.2);
}

/* Variante 8 Light 2 - Futurista Claro */
.variant8-light2-card {
  position: relative;
  overflow: hidden;
  background: #ffffff;
  border-radius: 16px;
  box-shadow:
    0 0 0 1px rgba(59, 130, 246, 0.05),
    0 10px 30px rgba(0, 0, 0, 0.08),
    0 0 15px rgba(59, 130, 246, 0.08);
  transition: all 0.5s ease;
  color: #334155;
}

.variant8-light2-grid {
  position: absolute;
  inset: 0;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.02) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: -1px -1px;
  -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
  mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
  z-index: 0;
}

.variant8-light2-particles {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: 0;
}

.variant8-light2-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background-color: rgba(59, 130, 246, 0.2);
  box-shadow: 0 0 4px rgba(59, 130, 246, 0.4);
  animation: variant8-particle-float 6s infinite;
}

.variant8-light2-progress-track {
  position: relative;
  height: 4px;
  background: rgba(241, 245, 249, 0.8);
  border-radius: 2px;
  overflow: hidden;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.variant8-light2-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  position: relative;
  border-radius: 2px;
}

.variant8-light2-progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  height: 100%;
  background: rgba(59, 130, 246, 0.6);
  filter: blur(4px);
  border-radius: 2px;
}

.variant8-light2-message-container {
  background: rgba(241, 245, 249, 0.5);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.variant8-light2-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(241, 245, 249, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.variant8-light2-icon-container::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    rgba(59, 130, 246, 0.15),
    transparent 30%
  );
  animation: variant8-rotate 4s linear infinite;
}

.variant8-light2-button {
  background: rgba(241, 245, 249, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.variant8-light2-button:hover {
  background: rgba(241, 245, 249, 1);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.2);
}

/* Variante 8 Light 3 - Futurista Claro */
.variant8-light3-card {
  position: relative;
  overflow: hidden;
  background: #f0f9ff;
  border-radius: 16px;
  box-shadow:
    0 0 0 1px rgba(59, 130, 246, 0.1),
    0 10px 30px rgba(0, 0, 0, 0.08),
    0 0 15px rgba(59, 130, 246, 0.1);
  transition: all 0.5s ease;
  color: #334155;
}

.variant8-light3-grid {
  position: absolute;
  inset: 0;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: -1px -1px;
  -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
  mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
  z-index: 0;
}

.variant8-light3-particles {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: 0;
}

.variant8-light3-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 0 4px rgba(59, 130, 246, 0.5);
  animation: variant8-particle-float 6s infinite;
}

.variant8-light3-progress-track {
  position: relative;
  height: 4px;
  background: rgba(241, 245, 249, 0.8);
  border-radius: 2px;
  overflow: hidden;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.variant8-light3-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0ea5e9, #3b82f6);
  position: relative;
  border-radius: 2px;
}

.variant8-light3-progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  height: 100%;
  background: rgba(59, 130, 246, 0.6);
  filter: blur(4px);
  border-radius: 2px;
}

.variant8-light3-message-container {
  background: rgba(241, 245, 249, 0.6);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.variant8-light3-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(241, 245, 249, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.variant8-light3-icon-container::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    rgba(59, 130, 246, 0.2),
    transparent 30%
  );
  animation: variant8-rotate 4s linear infinite;
}

.variant8-light3-button {
  background: rgba(241, 245, 249, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.variant8-light3-button:hover {
  background: rgba(241, 245, 249, 1);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.2);
}

/* Estilos para os componentes híbridos */

/* Híbrido 1 - Cartão de Vidro Flutuante */
.hybrid1-card {
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.7);
  border-radius: 16px;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.1),
    0 1px 8px rgba(0, 0, 0, 0.05);
  animation: hybrid1-float 4s ease-in-out infinite;
}

.hybrid1-card::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(
    90deg,
    rgba(59, 130, 246, 0.6),
    rgba(147, 51, 234, 0.6),
    rgba(59, 130, 246, 0.6)
  );
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  animation: hybrid1-border-glow 3s linear infinite;
}

/* Variante 8 Light 4 - Futurista Claro */
.variant8-light4-card {
  position: relative;
  overflow: hidden;
  background: #f8fafc;
  border-radius: 16px;
  box-shadow:
    0 0 0 1px rgba(99, 102, 241, 0.1),
    0 10px 30px rgba(0, 0, 0, 0.08),
    0 0 15px rgba(99, 102, 241, 0.1);
  transition: all 0.5s ease;
  color: #334155;
}

.variant8-light4-grid {
  position: absolute;
  inset: 0;
  background-image:
    linear-gradient(rgba(99, 102, 241, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: -1px -1px;
  -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
  mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
  z-index: 0;
}

.variant8-light4-particles {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: 0;
}

.variant8-light4-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background-color: rgba(99, 102, 241, 0.3);
  box-shadow: 0 0 4px rgba(99, 102, 241, 0.5);
  animation: variant8-particle-float 6s infinite;
}

.variant8-light4-progress-track {
  position: relative;
  height: 4px;
  background: rgba(241, 245, 249, 0.8);
  border-radius: 2px;
  overflow: hidden;
  border: 1px solid rgba(99, 102, 241, 0.1);
}

.variant8-light4-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  position: relative;
  border-radius: 2px;
}

.variant8-light4-progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  height: 100%;
  background: rgba(99, 102, 241, 0.6);
  filter: blur(4px);
  border-radius: 2px;
}

.variant8-light4-message-container {
  background: rgba(241, 245, 249, 0.6);
  border: 1px solid rgba(99, 102, 241, 0.1);
  border-radius: 8px;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.variant8-light4-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(241, 245, 249, 0.8);
  border: 1px solid rgba(99, 102, 241, 0.1);
  position: relative;
  overflow: hidden;
}

.variant8-light4-icon-container::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    rgba(99, 102, 241, 0.2),
    transparent 30%
  );
  animation: variant8-rotate 4s linear infinite;
}

.variant8-light4-button {
  background: rgba(241, 245, 249, 0.8);
  border: 1px solid rgba(99, 102, 241, 0.2);
  color: #6366f1;
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.variant8-light4-button:hover {
  background: rgba(241, 245, 249, 1);
  border-color: rgba(99, 102, 241, 0.3);
  box-shadow: 0 0 8px rgba(99, 102, 241, 0.2);
}

/* Variante 8 Light 5 - Futurista Claro */
.variant8-light5-card {
  position: relative;
  overflow: hidden;
  background: #fafafa;
  border-radius: 16px;
  box-shadow:
    0 0 0 1px rgba(14, 165, 233, 0.1),
    0 10px 30px rgba(0, 0, 0, 0.08),
    0 0 15px rgba(14, 165, 233, 0.1);
  transition: all 0.5s ease;
  color: #334155;
}

.variant8-light5-grid {
  position: absolute;
  inset: 0;
  background-image:
    linear-gradient(rgba(14, 165, 233, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(14, 165, 233, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: -1px -1px;
  -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
  mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
  z-index: 0;
}

.variant8-light5-particles {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: 0;
}

.variant8-light5-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background-color: rgba(14, 165, 233, 0.3);
  box-shadow: 0 0 4px rgba(14, 165, 233, 0.5);
  animation: variant8-particle-float 6s infinite;
}

.variant8-light5-progress-track {
  position: relative;
  height: 4px;
  background: rgba(241, 245, 249, 0.8);
  border-radius: 2px;
  overflow: hidden;
  border: 1px solid rgba(14, 165, 233, 0.1);
}

.variant8-light5-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0ea5e9, #38bdf8);
  position: relative;
  border-radius: 2px;
}

.variant8-light5-progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  height: 100%;
  background: rgba(14, 165, 233, 0.6);
  filter: blur(4px);
  border-radius: 2px;
}

.variant8-light5-message-container {
  background: rgba(241, 245, 249, 0.6);
  border: 1px solid rgba(14, 165, 233, 0.1);
  border-radius: 8px;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.variant8-light5-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(241, 245, 249, 0.8);
  border: 1px solid rgba(14, 165, 233, 0.1);
  position: relative;
  overflow: hidden;
}

.variant8-light5-icon-container::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    rgba(14, 165, 233, 0.2),
    transparent 30%
  );
  animation: variant8-rotate 4s linear infinite;
}

.variant8-light5-button {
  background: rgba(241, 245, 249, 0.8);
  border: 1px solid rgba(14, 165, 233, 0.2);
  color: #0ea5e9;
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.variant8-light5-button:hover {
  background: rgba(241, 245, 249, 1);
  border-color: rgba(14, 165, 233, 0.3);
  box-shadow: 0 0 8px rgba(14, 165, 233, 0.2);
}

/* Variante 8 Light 6 - Futurista Claro */
.variant8-light6-card {
  position: relative;
  overflow: hidden;
  background: #f8f9fa;
  border-radius: 16px;
  box-shadow:
    0 0 0 1px rgba(79, 70, 229, 0.1),
    0 10px 30px rgba(0, 0, 0, 0.08),
    0 0 15px rgba(79, 70, 229, 0.1);
  transition: all 0.5s ease;
  color: #334155;
}

.variant8-light6-grid {
  position: absolute;
  inset: 0;
  background-image:
    linear-gradient(rgba(79, 70, 229, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(79, 70, 229, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: -1px -1px;
  -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
  mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
  z-index: 0;
}

.variant8-light6-particles {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: 0;
}

.variant8-light6-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background-color: rgba(79, 70, 229, 0.3);
  box-shadow: 0 0 4px rgba(79, 70, 229, 0.5);
  animation: variant8-particle-float 6s infinite;
}

.variant8-light6-progress-track {
  position: relative;
  height: 4px;
  background: rgba(241, 245, 249, 0.8);
  border-radius: 2px;
  overflow: hidden;
  border: 1px solid rgba(79, 70, 229, 0.1);
}

.variant8-light6-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #8b5cf6);
  position: relative;
  border-radius: 2px;
}

.variant8-light6-progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  height: 100%;
  background: rgba(79, 70, 229, 0.6);
  filter: blur(4px);
  border-radius: 2px;
}

.variant8-light6-message-container {
  background: rgba(241, 245, 249, 0.6);
  border: 1px solid rgba(79, 70, 229, 0.1);
  border-radius: 8px;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.variant8-light6-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(241, 245, 249, 0.8);
  border: 1px solid rgba(79, 70, 229, 0.1);
  position: relative;
  overflow: hidden;
}

.variant8-light6-icon-container::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    rgba(79, 70, 229, 0.2),
    transparent 30%
  );
  animation: variant8-rotate 4s linear infinite;
}

.variant8-light6-button {
  background: rgba(241, 245, 249, 0.8);
  border: 1px solid rgba(79, 70, 229, 0.2);
  color: #4f46e5;
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.variant8-light6-button:hover {
  background: rgba(241, 245, 249, 1);
  border-color: rgba(79, 70, 229, 0.3);
  box-shadow: 0 0 8px rgba(79, 70, 229, 0.2);
}

@keyframes hybrid1-float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes hybrid1-border-glow {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

/* Híbrido 2 - Painel de Controle de Vidro */
.hybrid2-panel {
  position: relative;
  background: rgba(15, 23, 42, 0.8);
  border-radius: 12px;
  -webkit-backdrop-filter: blur(12px);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.hybrid2-panel::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.2),
    rgba(0, 0, 0, 0),
    rgba(147, 51, 234, 0.2)
  );
  pointer-events: none;
}

.hybrid2-circle-progress {
  position: relative;
  width: 60px;
  height: 60px;
}

.hybrid2-circle-progress svg {
  transform: rotate(-90deg);
}

.hybrid2-circle-progress circle {
  transition: stroke-dashoffset 0.5s ease;
}

/* Híbrido 3 - Cartão Lateral com Efeito de Vidro */
.hybrid3-card {
  position: relative;
  background: rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(0, 0, 0, 0.02);
}

.hybrid3-step {
  position: relative;
  padding-left: 24px;
}

.hybrid3-step::before {
  content: "";
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #e5e7eb;
}

.hybrid3-step-marker {
  position: absolute;
  left: 4px;
  top: 2px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #e5e7eb;
  z-index: 1;
}

.hybrid3-step-marker.active {
  background-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.hybrid3-step-marker.complete {
  background-color: #10b981;
}

/* Híbrido 4 - Cartão Minimalista com Efeito de Vidro */
.hybrid4-card {
  position: relative;
  background: rgba(255, 255, 255, 0.5);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.06),
    0 2px 6px rgba(0, 0, 0, 0.03);
  overflow: hidden;
}

.hybrid4-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.3),
    rgba(255, 255, 255, 0)
  );
  animation: hybrid4-shimmer 3s infinite;
}

.hybrid4-progress-bar {
  height: 2px;
  background: #f1f5f9;
  border-radius: 1px;
  overflow: hidden;
}

.hybrid4-progress-value {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 1px;
  transition: width 0.3s ease;
}

@keyframes hybrid4-shimmer {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(200%);
  }
}

.progress-highlight {
  filter: blur(4px);
  transform: skewX(-15deg);
}

.progress-bar {
  transition: width 700ms ease-out;
}

/* Classe para definir a largura dinamicamente via CSS vars */
.progress-width {
  width: var(--progress-width);
}

/* Animações para o DrawerDropzone */
@keyframes pulse-border-drawer {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.4);
  }
}

@keyframes pulse-icon-drawer {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.animate-pulse-border-drawer {
  animation: pulse-border-drawer 1.5s infinite;
}

.animate-pulse-icon-drawer {
  animation: pulse-icon-drawer 1.5s infinite ease-in-out;
}

