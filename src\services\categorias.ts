'use client';

import { Database } from "@/types/supabase"; // Importa os tipos gerais
import { supabase } from '@/lib/supabase/client'; // Importa o cliente supabase compartilhado

// Define os tipos específicos baseado nos tipos gerados
type Categoria = Database["public"]["Tables"]["categorias"]["Row"];
type CategoriaInsert = Database["public"]["Tables"]["categorias"]["Insert"];
// type CategoriaUpdate = Database["public"]["Tables"]["categorias"]["Update"]; // Descomentar se for usar

export function useCategoriasService() {
  return {
    /**
     * Busca todas as categorias cadastradas, ordenadas por nome.
     * @returns {Promise<Categoria[]>} Uma promessa que resolve para um array de categorias.
     */
    async listarCategorias(): Promise<Categoria[]> {
      const { data, error } = await supabase
        .from('categorias')
        .select('id, nome') // Seleciona apenas id e nome
        .order('nome');

      if (error) {
        
        throw new Error(error.message || "Erro ao buscar categorias");
      }
      return data || []; // Retorna um array vazio se data for null
    },

    /**
     * Cria uma nova categoria.
     * @param {Pick<CategoriaInsert, 'nome'>} categoriaData - Objeto contendo o nome da categoria.
     * @returns {Promise<Categoria | null>} Uma promessa que resolve para a categoria criada ou null em caso de erro.
     */
    async criarCategoria(categoriaData: Pick<CategoriaInsert, 'nome'>): Promise<Categoria | null> {
      const { data, error } = await supabase
        .from('categorias')
        .insert(categoriaData)
        .select()
        .single();
      
      if (error) {
        
        throw new Error(error.message || "Erro ao criar categoria");
      }
      return data;
    }

    // TODO: Adicionar outras funções CRUD (buscarPorId, atualizar, excluir) se necessário no futuro.
  };
}