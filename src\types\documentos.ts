import { Database } from "@/types/supabase";

// Tipo de status do documento
export type StatusDocumento = 'pendente' | 'aprovado' | 'rejeitado';

// Tipo para anexos adicionais de documentos
export type DocumentoAnexo = {
  id: string;
  documento_id: string;
  nome: string;
  arquivo_url: string;
  arquivo_path: string;
  tipo_arquivo: string;
  created_at: string;
  updated_at: string;
};

// Tipo básico do documento
export type Documento = {
  id: string;
  nome: string;
  arquivo_url: string;
  arquivo_path: string;
  tipo_arquivo: string;
  status: StatusDocumento;
  fornecedor: string | null;
  valor_total: number | null;
  data_vencimento: string | null;
  motivo_rejeicao: string | null;
  dados_extraidos: DadosExtraidos | null;
  user_id: string;
  lancamento_id: string | null;
  contato_id: string | null; // ID do contato associado
  obra_id: string | null; // ID da obra associada
  descricao: string | null; // Descrição do documento
  created_at: string;
  updated_at: string;
  anexos?: DocumentoAnexo[]; // Anexos adicionais
  quantidade_anexos?: number; // Quantidade de anexos (usado na listagem)
};

// Tipo para os dados extraídos pela IA
export type DadosExtraidos = {
  fornecedor?: string;
  valor_total?: number;
  data_vencimento?: string;
  tipo_documento?: 'boleto' | 'comprovante' | 'nota_fiscal' | 'outros';
  status_pagamento?: 'pago' | 'pendente' | 'vencido';
  data_pagamento?: string; // Data em que o pagamento foi realizado (apenas para comprovantes)
  forma_pagamento_realizada?: string; // Forma como o pagamento foi realizado (apenas para comprovantes)
  parcelas?: ParcelaExtraida[];
  confianca?: number;
  // Dados do contato/fornecedor detectado pela IA (não salvos no banco até aprovação)
  contato_detectado?: ContatoDetectado;
};

// Tipo para parcela extraída pela IA
export type ParcelaExtraida = {
  numero: number;
  valor: number;
  vencimento: string;
};

// Tipo para estatísticas de documentos
export type EstatisticasDocumentos = {
  pendentes: {
    quantidade: number;
    valor_total: number;
  };
  vencidos: {
    quantidade: number;
  };
  proximos_vencimentos: {
    quantidade: number;
  };
};

// Tipo para o formulário de documento
export type FormularioDocumento = {
  fornecedor: string;
  valor_total: string;
  data_vencimento: Date;
  parcelas: ParcelaExtraida[];
};

// Tipo para o formulário de rejeição
export type FormularioRejeicao = {
  motivo: string;
};

// Tipos para documentos agrupados por lançamento
export type DocumentosAgrupados = {
  id: string | null; // null para documentos sem lançamento
  tipo: 'lancamento' | 'sem_lancamento';
  lancamento?: {
    id: string;
    descricao: string;
    valor_total: number;
    data_competencia?: string;
    data_pagamento?: string | null;
    tipo_lancamento?: 'receita' | 'despesa';
    status?: 'Em aberto' | 'Pago' | 'Cancelado';
    forma_pagamento?: 'dinheiro' | 'pix' | 'cartao_credito' | 'cartao_debito' | 'cheque' | 'transferencia' | 'boleto';
    contato_id?: string;
    obra_id?: string;
  };
  documentos: Documento[];
  quantidade_documentos: number;
};

// Tipo para contato detectado pela IA
export type ContatoDetectado = {
  nome_empresa?: string;
  nome_razao?: string;
  nome_contato?: string;
  email?: string;
  telefone?: string;
  cpf_cnpj?: string;
  tipo_pessoa?: 'fisica' | 'juridica';
  chave_pix?: string;
  endereco?: string;
  cidade?: string;
  uf?: string;
  cep?: string;
  confianca?: number; // Nível de confiança da detecção (0-1)
};