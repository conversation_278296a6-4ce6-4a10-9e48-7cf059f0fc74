import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Criar cliente Supabase com a chave de serviço para contornar o RLS
const createServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    
    return null;
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

// POST /api/documentos/atualizar-obra - Atualiza apenas o campo obra_id de um documento
export async function POST(request: NextRequest) {
  try {
    // Obter parâmetros da URL
    const url = new URL(request.url);
    const id = url.searchParams.get('id');
    const obra_id = url.searchParams.get('obra_id');

    if (!id) {
      return NextResponse.json({ error: 'ID do documento não fornecido' }, { status: 400 });
    }// Criar cliente Supabase com a chave de serviço
    const supabase = createServiceClient();
    if (!supabase) {
      return NextResponse.json({ error: 'Erro ao criar cliente Supabase' }, { status: 500 });
    }

    // Verificar se o documento existe
    const { data: documento, error: getError } = await supabase
      .from('documentos')
      .select('id, obra_id')
      .eq('id', id)
      .single();

    if (getError) {
      
      return NextResponse.json({ error: 'Documento não encontrado' }, { status: 404 });
    }// Atualizar apenas o campo obra_id
    const { data, error } = await supabase
      .from('documentos')
      .update({ 
        obra_id: obra_id === 'null' ? null : obra_id,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select();

    if (error) {
      
      return NextResponse.json({ error: error.message }, { status: 500 });
    }// Verificar se a atualização foi aplicada
    const { data: verificacao, error: verificacaoError } = await supabase
      .from('documentos')
      .select('id, obra_id')
      .eq('id', id)
      .single();

    if (verificacaoError) {
      
    } else {}

    return NextResponse.json(data);
  } catch (error: any) {
    
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}