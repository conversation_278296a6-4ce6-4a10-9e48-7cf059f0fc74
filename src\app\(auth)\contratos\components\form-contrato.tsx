'use client';

import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';
import { Loader2 } from 'lucide-react';

const formSchema = z.object({
  numero: z.string().min(1, 'O número é obrigatório'),
  descricao: z.string().min(1, 'A descrição é obrigatória'),
  valor: z.coerce.number().min(0, 'O valor é obrigatório'),
  data_inicio: z.string().min(1, 'A data de início é obrigatória'),
  data_fim: z.string().min(1, 'A data de fim é obrigatória'),
  status: z.enum(['ativo', 'encerrado', 'cancelado'], {
    required_error: 'O status é obrigatório',
  }),
});

type FormValues = z.infer<typeof formSchema>;

interface FormContratoProps {
  contrato?: any;
  onSuccess: () => void;
  onCancel: () => void;
}

export function FormContrato({ contrato, onSuccess, onCancel }: FormContratoProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      numero: '',
      descricao: '',
      valor: 0,
      data_inicio: '',
      data_fim: '',
      status: 'ativo',
    },
  });

  useEffect(() => {
    if (contrato) {
      form.reset({
        numero: contrato.numero,
        descricao: contrato.descricao,
        valor: contrato.valor,
        data_inicio: contrato.data_inicio,
        data_fim: contrato.data_fim,
        status: contrato.status,
      });
    }
  }, [contrato, form]);

  async function onSubmit(data: FormValues) {
    try {
      if (contrato) {
        const { error } = await supabase
          .from('contratos')
          .update(data)
          .eq('id', contrato.id);

        if (error) throw error;

        toast.success('Contrato atualizado com sucesso');
      } else {
        const { error } = await supabase
          .from('contratos')
          .insert(data);

        if (error) throw error;

        toast.success('Contrato criado com sucesso');
      }

      onSuccess();
    } catch (error: any) {
      toast.error(`${contrato ? 'Erro ao atualizar' : 'Erro ao criar'} contrato: ${error.message}`);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="numero"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Número</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="descricao"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Descrição</FormLabel>
              <FormControl>
                <Textarea {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="valor"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Valor</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="number"
                  step="0.01"
                  min="0"
                  onChange={(e) => field.onChange(e.target.valueAsNumber)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="data_inicio"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Data de Início</FormLabel>
                <FormControl>
                  <Input {...field} type="date" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="data_fim"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Data de Fim</FormLabel>
                <FormControl>
                  <Input {...field} type="date" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Status</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o status" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="ativo">Ativo</SelectItem>
                  <SelectItem value="encerrado">Encerrado</SelectItem>
                  <SelectItem value="cancelado">Cancelado</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={form.formState.isSubmitting}
          >
            Cancelar
          </Button>
          <Button
            type="submit"
            disabled={form.formState.isSubmitting}
          >
            {form.formState.isSubmitting ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            {contrato ? 'Salvar' : 'Criar'}
          </Button>
        </div>
      </form>
    </Form>
  );
}