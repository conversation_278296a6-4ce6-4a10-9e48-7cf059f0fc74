import { createClient } from '@supabase/supabase-js';

// Configuração para testes
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

// Criar cliente Supabase para testes
const supabase = createClient(supabaseUrl, supabaseKey);

// Função para criar um lançamento de teste
async function createTestLancamento() {
  // Criar um lançamento de teste
  const { data: obra } = await supabase
    .from('obras')
    .select('id')
    .limit(1)
    .single();

  if (!obra) {
    throw new Error('Nenhuma obra encontrada para teste');
  }

  // Criar lançamento
  const { data: lancamento, error } = await supabase
    .from('lancamentos')
    .insert({
      obra_id: obra.id,
      tipo_lancamento: 'terceiros',
      valor_total: 100,
      data_competencia: new Date().toISOString().split('T')[0],
      forma_pagamento: 'pix',
      descricao: 'Lançamento de teste para exclusão',
      status: 'Em aberto'
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Erro ao criar lançamento de teste: ${error.message}`);
  }

  // Criar parcela
  const { error: parcelaError } = await supabase
    .from('parcelas')
    .insert({
      lancamento_id: lancamento.id,
      numero: 1,
      valor: 100,
      vencimento: new Date().toISOString().split('T')[0],
      status: 'pendente'
    });

  if (parcelaError) {
    throw new Error(`Erro ao criar parcela de teste: ${parcelaError.message}`);
  }

  return lancamento.id;
}

// Função para testar a exclusão de um lançamento
async function testDeleteLancamento() {
  try {
    // Criar um lançamento de teste
    const lancamentoId = await createTestLancamento();
    console.log(`Lançamento de teste criado com ID: ${lancamentoId}`);

    // Verificar se o lançamento foi criado
    const { data: lancamento, error: getLancamentoError } = await supabase
      .from('lancamentos')
      .select('*')
      .eq('id', lancamentoId)
      .single();

    if (getLancamentoError) {
      throw new Error(`Erro ao verificar lançamento: ${getLancamentoError.message}`);
    }

    console.log('Lançamento criado com sucesso:', lancamento);

    // Verificar se a parcela foi criada
    const { data: parcelas, error: getParcelasError } = await supabase
      .from('parcelas')
      .select('*')
      .eq('lancamento_id', lancamentoId);

    if (getParcelasError) {
      throw new Error(`Erro ao verificar parcelas: ${getParcelasError.message}`);
    }

    console.log(`${parcelas.length} parcelas encontradas para o lançamento`);

    // Testar a função RPC para excluir o lançamento
    console.log('Testando exclusão via RPC...');
    const { data: deleteResult, error: deleteError } = await supabase.rpc(
      'delete_lancamento_with_parcelas',
      { p_id: lancamentoId }
    );

    if (deleteError) {
      throw new Error(`Erro ao excluir lançamento via RPC: ${deleteError.message}`);
    }

    console.log('Resultado da exclusão:', deleteResult);

    // Verificar se o lançamento foi excluído
    const { data: lancamentoAposExclusao, error: checkError } = await supabase
      .from('lancamentos')
      .select('*')
      .eq('id', lancamentoId);

    if (checkError) {
      throw new Error(`Erro ao verificar exclusão: ${checkError.message}`);
    }

    if (lancamentoAposExclusao && lancamentoAposExclusao.length > 0) {
      throw new Error('Lançamento não foi excluído corretamente');
    }

    console.log('Lançamento excluído com sucesso!');

    // Verificar se as parcelas foram excluídas
    const { data: parcelasAposExclusao, error: checkParcelasError } = await supabase
      .from('parcelas')
      .select('*')
      .eq('lancamento_id', lancamentoId);

    if (checkParcelasError) {
      throw new Error(`Erro ao verificar exclusão de parcelas: ${checkParcelasError.message}`);
    }

    if (parcelasAposExclusao && parcelasAposExclusao.length > 0) {
      throw new Error('Parcelas não foram excluídas corretamente');
    }

    console.log('Parcelas excluídas com sucesso!');
    
    return { success: true, message: 'Teste de exclusão concluído com sucesso' };
  } catch (error: any) {
    console.error('Erro no teste de exclusão:', error);
    return { success: false, message: error.message };
  }
}

// Executar o teste
testDeleteLancamento().then(result => {
  console.log('Resultado do teste:', result);
});
