'use client'

import { X } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { cn, overlayStyles } from '@/lib/utils'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { useState, useEffect } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { DatePicker } from '@/components/ui/date-picker'
import { Button } from '@/components/ui/button'
import { Check } from 'lucide-react'
import { SearchableSelect } from '@/components/ui/searchable-select'

interface FiltrosAtivosProps {
  filtros: any
  projetos: string[]
  fornecedores: string[]
  statusOptions: { value: string; label: string }[]
  onChange: (filtros: any) => void
}

export default function FiltrosAtivos({
  filtros,
  projetos,
  fornecedores,
  statusOptions,
  onChange
}: FiltrosAtivosProps) {
  // Estado para controlar os popovers
  const [projetoPopoverOpen, setProjetoPopoverOpen] = useState(false)
  const [fornecedorPopoverOpen, setFornecedorPopoverOpen] = useState(false)
  const [dataPopoverOpen, setDataPopoverOpen] = useState(false)
  const [statusPopoverOpen, setStatusPopoverOpen] = useState(false)

  // Estados temporários para os filtros
  const [tempFornecedorFilter, setTempFornecedorFilter] = useState<string | string[]>(
    filtros.fornecedor || []
  )
  const [tempDataInicioFilter, setTempDataInicioFilter] = useState<Date | undefined>(
    filtros.dataInicio
  )
  const [tempDataFimFilter, setTempDataFimFilter] = useState<Date | undefined>(
    filtros.dataFim
  )
  const [tempStatusFilter, setTempStatusFilter] = useState<string[]>(
    Array.isArray(filtros.status) ? filtros.status : []
  )

  // Função para remover um filtro
  const removeFilter = (key: string) => {
    const newFiltros = { ...filtros }
    delete newFiltros[key]
    onChange(newFiltros)
  }

  // Função para atualizar um filtro
  const updateFilter = (key: string, value: any) => {
    onChange({ ...filtros, [key]: value })
  }

  // Função para aplicar o filtro de fornecedor
  const applyFornecedorFilter = () => {
    updateFilter('fornecedor', tempFornecedorFilter)
    setFornecedorPopoverOpen(false)
  }

  // Função para aplicar o filtro de data
  const applyDataFilter = () => {
    const newFiltros = { ...filtros }
    if (tempDataInicioFilter) {
      newFiltros.dataInicio = tempDataInicioFilter
    } else {
      delete newFiltros.dataInicio
    }

    if (tempDataFimFilter) {
      newFiltros.dataFim = tempDataFimFilter
    } else {
      delete newFiltros.dataFim
    }

    onChange(newFiltros)
    setDataPopoverOpen(false)
  }

  // Função para aplicar o filtro de status
  const applyStatusFilter = () => {
    updateFilter('status', tempStatusFilter)
    setStatusPopoverOpen(false)
  }

  // Verificar se há alterações pendentes
  const hasFornecedorPendingChanges = JSON.stringify(tempFornecedorFilter) !== JSON.stringify(filtros.fornecedor)
  const hasDataPendingChanges = (tempDataInicioFilter !== filtros.dataInicio) || (tempDataFimFilter !== filtros.dataFim)
  const hasStatusPendingChanges = JSON.stringify(tempStatusFilter) !== JSON.stringify(filtros.status)

  // Atualizar estados temporários quando os filtros mudarem
  useEffect(() => {
    setTempFornecedorFilter(filtros.fornecedor || [])
    setTempDataInicioFilter(filtros.dataInicio)
    setTempDataFimFilter(filtros.dataFim)
    setTempStatusFilter(Array.isArray(filtros.status) ? filtros.status : [])
  }, [filtros])

  // Preparar dados para exibição
  const fornecedoresSelecionados = Array.isArray(tempFornecedorFilter)
    ? tempFornecedorFilter
    : tempFornecedorFilter
    ? [tempFornecedorFilter]
    : []
  const todosFornecedoresSelecionados = fornecedoresSelecionados.length === fornecedores.length

  const statusSelecionados = Array.isArray(tempStatusFilter) ? tempStatusFilter : []
  const todosStatusSelecionados = statusSelecionados.length === statusOptions.length

  // Mapear os valores de status para seus rótulos
  const getStatusLabels = () => {
    if (!filtros.status || !Array.isArray(filtros.status) || filtros.status.length === 0) return null

    return statusOptions
      .filter(option => filtros.status.includes(option.value))
      .map(option => option.label)
  }

  const statusLabels = getStatusLabels()

  return (
    <div className="flex flex-wrap gap-2">
      {/* Filtro de Projeto */}
      {filtros.projeto ? (
        <Popover open={projetoPopoverOpen} onOpenChange={setProjetoPopoverOpen}>
          <PopoverTrigger asChild>
            <div className="flex items-center gap-1 px-3 py-1 rounded-md bg-white border border-gray-200 cursor-pointer">
              <span className="text-sm">Projeto: {filtros.projeto}</span>
              <X
                className="h-3.5 w-3.5 cursor-pointer text-gray-500 hover:text-gray-700"
                onClick={(e) => {
                  e.stopPropagation()
                  removeFilter('projeto')
                }}
              />
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-4 bg-white" align="start">
            <h3 className="text-base font-medium mb-4">Selecione o projeto</h3>
            <SearchableSelect
              options={[
                { value: 'todos', label: 'Todos os projetos' },
                ...projetos.map(projeto => ({ value: projeto, label: projeto }))
              ]}
              value={filtros.projeto || 'todos'}
              onValueChange={(value) => {
                if (value === 'todos') {
                  removeFilter('projeto')
                } else {
                  updateFilter('projeto', value)
                }
                setProjetoPopoverOpen(false)
              }}
              placeholder="Selecione um projeto"
              searchPlaceholder="Buscar projeto..."
              emptyMessage="Nenhum projeto encontrado"
              contentClassName="bg-white"
            />
          </PopoverContent>
        </Popover>
      ) : (
        <SearchableSelect
          options={[
            { value: 'todos', label: 'Todos os projetos' },
            ...projetos.map(projeto => ({ value: projeto, label: projeto }))
          ]}
          value={filtros.projeto || 'todos'}
          onValueChange={(value) => updateFilter('projeto', value === 'todos' ? undefined : value)}
          placeholder="Todos os projetos"
          searchPlaceholder="Buscar projeto..."
          emptyMessage="Nenhum projeto encontrado"
          triggerClassName="h-9 w-[220px] border border-gray-300 bg-white"
          contentClassName="bg-white"
        />
      )}

      {/* Filtro de Fornecedor */}
      {filtros.fornecedor ? (
        <Popover open={fornecedorPopoverOpen} onOpenChange={setFornecedorPopoverOpen}>
          <PopoverTrigger asChild>
            <div className="flex items-center gap-1 px-3 py-1 rounded-md bg-white border border-gray-200 cursor-pointer">
              <span className="text-sm">Fornecedor: {typeof filtros.fornecedor === 'string' ? filtros.fornecedor : filtros.fornecedor?.join(', ')}</span>
              <X
                className="h-3.5 w-3.5 cursor-pointer text-gray-500 hover:text-gray-700"
                onClick={(e) => {
                  e.stopPropagation()
                  removeFilter('fornecedor')
                }}
              />
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-4 bg-white" align="start">
            <h3 className="text-base font-medium mb-4">Selecione o fornecedor</h3>
            <SearchableSelect
              options={[
                { value: 'todos', label: 'Todos os fornecedores' },
                ...fornecedores.map(fornecedor => ({ value: fornecedor, label: fornecedor }))
              ]}
              value={filtros.fornecedor || 'todos'}
              onValueChange={(value) => {
                if (value === 'todos') {
                  removeFilter('fornecedor')
                } else {
                  updateFilter('fornecedor', value)
                }
                setFornecedorPopoverOpen(false)
              }}
              placeholder="Selecione um fornecedor"
              searchPlaceholder="Buscar fornecedor..."
              emptyMessage="Nenhum fornecedor encontrado"
              contentClassName="bg-white"
            />
          </PopoverContent>
        </Popover>
      ) : (
        <SearchableSelect
          options={[
            { value: 'todos', label: 'Todos os fornecedores' },
            ...fornecedores.map(fornecedor => ({ value: fornecedor, label: fornecedor }))
          ]}
          value={filtros.fornecedor || 'todos'}
          onValueChange={(value) => updateFilter('fornecedor', value === 'todos' ? undefined : value)}
          placeholder="Todos os fornecedores"
          searchPlaceholder="Buscar fornecedor..."
          emptyMessage="Nenhum fornecedor encontrado"
          triggerClassName="h-9 w-[220px] border border-gray-300 bg-white"
          contentClassName="bg-white"
        />
      )}

      {/* Filtro de Status */}
      {filtros.status && Array.isArray(filtros.status) && filtros.status.length > 0 && (
        <Popover open={statusPopoverOpen} onOpenChange={setStatusPopoverOpen}>
          <PopoverTrigger asChild>
            <div className="flex items-center gap-1 px-3 py-1 rounded-md bg-white border border-gray-200 cursor-pointer">
              <span className="text-sm">Status: {statusLabels ? `${statusLabels.length} selecionado(s)` : '0 selecionado(s)'}</span>
              <X
                className="h-3.5 w-3.5 cursor-pointer text-gray-500 hover:text-gray-700"
                onClick={(e) => {
                  e.stopPropagation()
                  removeFilter('status')
                }}
              />
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-4 bg-white" align="start">
            <h3 className="text-base font-medium mb-4">Status do lançamento</h3>
            <div className="flex justify-between mb-2">
              <button
                type="button"
                className="text-blue-600 text-sm underline cursor-pointer font-normal"
                onClick={() => {
                  if (todosStatusSelecionados) {
                    setTempStatusFilter([])
                  } else {
                    setTempStatusFilter(statusOptions.map(o => o.value))
                  }
                }}
              >
                {todosStatusSelecionados ? 'Limpar seleção' : 'Selecionar todos'}
              </button>
              <span className="text-sm text-gray-500">
                {statusSelecionados.length} selecionado(s)
              </span>
            </div>
            <div className="border border-gray-100 rounded-md p-3 mb-3 bg-white">
              <div className="space-y-2">
                {statusOptions.map(option => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-popup-${option.value}`}
                      checked={statusSelecionados.includes(option.value)}
                      onCheckedChange={(checked) => {
                        const currentStatus = [...statusSelecionados]
                        if (checked) {
                          setTempStatusFilter([...currentStatus, option.value])
                        } else {
                          setTempStatusFilter(
                            currentStatus.filter(s => s !== option.value)
                          )
                        }
                      }}
                    />
                    <label
                      htmlFor={`status-popup-${option.value}`}
                      className="text-sm font-normal cursor-pointer flex-1"
                    >
                      {option.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            <div className="mt-4 flex justify-end">
              <Button
                size="sm"
                className="gap-1 bg-blue-600 hover:bg-blue-700 text-white"
                disabled={!hasStatusPendingChanges}
                onClick={applyStatusFilter}
              >
                <Check className="h-4 w-4" />
                <span>Aplicar filtro</span>
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      )}

      {/* Filtro de Data */}
      {(filtros.dataInicio || filtros.dataFim) && (
        <Popover open={dataPopoverOpen} onOpenChange={setDataPopoverOpen}>
          <PopoverTrigger asChild>
            <div className="flex items-center gap-1 px-3 py-1 rounded-md bg-white border border-gray-200 cursor-pointer">
              <span className="text-sm">
                {filtros.dataInicio && `De: ${format(new Date(filtros.dataInicio), 'dd/MM/yy')}`}
                {filtros.dataInicio && filtros.dataFim && ' '}
                {filtros.dataFim && `Até: ${format(new Date(filtros.dataFim), 'dd/MM/yy')}`}
              </span>
              <X
                className="h-3.5 w-3.5 cursor-pointer text-gray-500 hover:text-gray-700"
                onClick={(e) => {
                  e.stopPropagation()
                  removeFilter('dataInicio')
                  removeFilter('dataFim')
                }}
              />
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-4 bg-white" align="start">
            <h3 className="text-base font-medium mb-4">Período</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium mb-1">De</p>
                <DatePicker
                  date={tempDataInicioFilter}
                  setDate={(date) => setTempDataInicioFilter(date)}
                  placeholder="DD/MM/AAAA"
                  className="w-full border border-gray-300 bg-white"
                />
              </div>
              <div>
                <p className="text-sm font-medium mb-1">Até</p>
                <DatePicker
                  date={tempDataFimFilter}
                  setDate={(date) => setTempDataFimFilter(date)}
                  placeholder="DD/MM/AAAA"
                  className="w-full border border-gray-300 bg-white"
                />
              </div>
            </div>
            <div className="mt-4 flex justify-end">
              <Button
                size="sm"
                className="gap-1 bg-blue-600 hover:bg-blue-700 text-white"
                disabled={!hasDataPendingChanges}
                onClick={applyDataFilter}
              >
                <Check className="h-4 w-4" />
                <span>Aplicar filtro</span>
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      )}
    </div>
  )
}