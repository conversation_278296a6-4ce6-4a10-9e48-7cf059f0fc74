-- Criação da tabela de visualizações
CREATE TABLE IF NOT EXISTS public.visualizacoes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  contexto TEXT NOT NULL,
  nome TEXT NOT NULL,
  descricao TEXT,
  filtros_json JSONB NOT NULL DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Comentários nas colunas
COMMENT ON TABLE public.visualizacoes IS 'Armazena visualizações salvas pelos usuários';
COMMENT ON COLUMN public.visualizacoes.id IS 'ID único da visualização';
COMMENT ON COLUMN public.visualizacoes.user_id IS 'ID do usuário que criou a visualização';
COMMENT ON COLUMN public.visualizacoes.contexto IS 'Contexto da visualização (ex: lancamentos, contatos)';
COMMENT ON COLUMN public.visualizacoes.nome IS 'Nome da visualização';
COMMENT ON COLUMN public.visualizacoes.descricao IS 'Descrição opcional da visualização';
COMMENT ON COLUMN public.visualizacoes.filtros_json IS 'Filtros salvos em formato JSON';
COMMENT ON COLUMN public.visualizacoes.created_at IS 'Data de criação';
COMMENT ON COLUMN public.visualizacoes.updated_at IS 'Data da última atualização';

-- Índices
CREATE INDEX IF NOT EXISTS visualizacoes_user_id_idx ON public.visualizacoes(user_id);
CREATE INDEX IF NOT EXISTS visualizacoes_contexto_idx ON public.visualizacoes(contexto);
CREATE INDEX IF NOT EXISTS visualizacoes_user_contexto_idx ON public.visualizacoes(user_id, contexto);

-- Trigger para atualizar o updated_at
CREATE OR REPLACE FUNCTION set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS set_visualizacoes_updated_at ON public.visualizacoes;
CREATE TRIGGER set_visualizacoes_updated_at
BEFORE UPDATE ON public.visualizacoes
FOR EACH ROW
EXECUTE FUNCTION set_updated_at();

-- Política de segurança: qualquer usuário pode selecionar suas próprias visualizações
DROP POLICY IF EXISTS visualizacoes_select_policy ON public.visualizacoes;
CREATE POLICY visualizacoes_select_policy ON public.visualizacoes
  FOR SELECT USING (auth.uid() = user_id);

-- Política de segurança: qualquer usuário pode inserir visualizações vinculadas a si mesmo
DROP POLICY IF EXISTS visualizacoes_insert_policy ON public.visualizacoes;
CREATE POLICY visualizacoes_insert_policy ON public.visualizacoes
  FOR INSERT WITH CHECK (auth.uid() = user_id OR auth.role() = 'service_role');

-- Política de segurança: usuários só podem atualizar suas próprias visualizações
DROP POLICY IF EXISTS visualizacoes_update_policy ON public.visualizacoes;
CREATE POLICY visualizacoes_update_policy ON public.visualizacoes
  FOR UPDATE USING (auth.uid() = user_id);

-- Política de segurança: usuários só podem excluir suas próprias visualizações
DROP POLICY IF EXISTS visualizacoes_delete_policy ON public.visualizacoes;
CREATE POLICY visualizacoes_delete_policy ON public.visualizacoes
  FOR DELETE USING (auth.uid() = user_id);

-- Habilitar RLS na tabela
ALTER TABLE public.visualizacoes ENABLE ROW LEVEL SECURITY; 