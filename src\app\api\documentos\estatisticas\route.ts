import { NextRequest, NextResponse } from 'next/server';
import { verificarAutenticacao } from '@/lib/auth-helpers';
import { createServiceClient } from '@/lib/supabase/service-client';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';

export const dynamic = 'force-dynamic';

// GET /api/documentos/estatisticas - Obter estatísticas dos documentos
export async function GET(request: NextRequest) {
  try {
    // Verificar se devemos forçar o uso do cliente de serviço
    const url = new URL(request.url);
    const forceServiceRole = url.searchParams.get('forceServiceRole') === 'true';

    let clientToUse;
    let useServiceClient = false;

    // Verificar se devemos usar o cliente de serviço diretamente
    if (forceServiceRole) {
      const serviceClient = createServiceClient();

      if (!serviceClient) {
        return NextResponse.json({ error: 'Erro ao criar cliente de serviço' }, { status: 500 });
      }

      clientToUse = serviceClient;
      useServiceClient = true;
    } else {
      try {
        // Tentar criar cliente de autenticação usando o utilitário assíncrono
        const supabase = await getSupabaseRouteClient();
        
        // Tentar com autenticação normal primeiro
        const auth = await verificarAutenticacao(supabase);

        // Se não estiver autenticado, tentar com cliente de serviço como fallback
        if (!auth.autenticado) {
          const serviceClient = createServiceClient();

          if (!serviceClient) {
            return NextResponse.json({ error: 'Erro ao criar cliente de serviço após falha de autenticação' }, { status: 500 });
          }

          clientToUse = serviceClient;
          useServiceClient = true;
        } else {
          clientToUse = supabase;
        }
      } catch (cookieError) {
        // Se houver erro de parsing de cookies, usar cliente de serviço como fallback
        console.warn('Erro ao processar cookies, usando cliente de serviço:', cookieError);
        
        const serviceClient = createServiceClient();
        if (!serviceClient) {
          return NextResponse.json({ error: 'Erro ao criar cliente de serviço após erro de cookies' }, { status: 500 });
        }

        clientToUse = serviceClient;
        useServiceClient = true;
      }
    }

    // Obter documentos pendentes
    const { data: pendentes, error: pendentesError } = await clientToUse
      .from('documentos')
      .select('id, valor_total')
      .eq('status', 'pendente');

    if (pendentesError) {
      return NextResponse.json(
        { error: 'Erro ao buscar documentos pendentes: ' + pendentesError.message },
        { status: 500 }
      );
    }

    // Calcular valor total dos pendentes
    const valorTotalPendentes = pendentes.reduce((total, doc) => {
      return total + (doc.valor_total || 0);
    }, 0);

    // Obter documentos vencidos (pendentes com data_vencimento no passado)
    const hoje = new Date().toISOString().split('T')[0];
    const { data: vencidos, error: vencidosError } = await clientToUse
      .from('documentos')
      .select('id')
      .eq('status', 'pendente')
      .lt('data_vencimento', hoje);

    if (vencidosError) {
      return NextResponse.json(
        { error: 'Erro ao buscar documentos vencidos: ' + vencidosError.message },
        { status: 500 }
      );
    }

    // Obter documentos com vencimento nos próximos 7 dias
    const dataFutura = new Date();
    dataFutura.setDate(dataFutura.getDate() + 7);
    const dataFuturaStr = dataFutura.toISOString().split('T')[0];

    const { data: proximosVencimentos, error: proximosError } = await clientToUse
      .from('documentos')
      .select('id')
      .eq('status', 'pendente')
      .gte('data_vencimento', hoje)
      .lte('data_vencimento', dataFuturaStr);

    if (proximosError) {
      return NextResponse.json(
        { error: 'Erro ao buscar próximos vencimentos: ' + proximosError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      pendentes: {
        quantidade: pendentes.length,
        valor_total: valorTotalPendentes
      },
      vencidos: {
        quantidade: vencidos.length
      },
      proximos_vencimentos: {
        quantidade: proximosVencimentos.length
      }
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Erro ao obter estatísticas: ' + error.message },
      { status: 500 }
    );
  }
}