'use client';

import { useState, useEffect, useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Eye,
  MoreHorizontal,
  Trash2,
  FileText,
  Download,
  Clock,
  UserCheck,
  UserX,
  Paperclip,
  Sparkles
} from 'lucide-react';
import { getDocumentos } from '@/services/documentos';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useContatosService } from '@/services/contatos';
import { Contato } from '@/types/contatos';
import { Documento } from '@/types/documentos';

interface DocumentosTableProps {
  status?: string;
  documentos?: Documento[];
  loading?: boolean;
  filtros?: {
    termo?: string;
    obraId?: string;
    contatoId?: string;
    dataInicio?: Date;
    dataFim?: Date;
  };
  onSelectDocumento?: (documento: Documento) => void;
  onDocumentosChange?: () => void;
  onDocumentClick?: (documento: Documento) => void;
  onDocumentDeleted?: () => void;
}

export function DocumentosTable({ 
  status,
  documentos: documentosExternos,
  loading: loadingExterno,
  filtros, 
  onSelectDocumento,
  onDocumentosChange,
  onDocumentClick, 
  onDocumentDeleted 
}: DocumentosTableProps) {
  const [documentos, setDocumentos] = useState<Documento[]>([]);
  const [loading, setLoading] = useState(true);
  const [contatos, setContatos] = useState<Contato[]>([]);
  const contatosService = useContatosService();

  // Se documentos externos são fornecidos, usá-los diretamente
  const documentosParaExibir = documentosExternos || documentos;
  const isLoading = loadingExterno !== undefined ? loadingExterno : loading;

  useEffect(() => {
    // Só carregar dados se não temos documentos externos
    if (!documentosExternos) {
      loadDocumentos();
    }
    loadContatos();
  }, [status, filtros, documentosExternos]);

  const loadContatos = async () => {
    try {
      const data = await contatosService.listarContatos();
      setContatos(data);
    } catch (error) {
      
    }
  };

  const getContatoNome = (contatoId?: string | null) => {
    if (!contatoId) return 'N/A';
    const contato = contatos.find(c => c.id === contatoId);
    return contato?.nome_empresa || 'N/A';
  };

  const loadDocumentos = async () => {
    try {
      setLoading(true);
      let docs = await getDocumentos();
      
      // Filtrar por status apenas se status for fornecido
      if (status) {
        docs = docs.filter(doc => doc.status === status);
      }
      
      // Aplicar filtros adicionais
      if (filtros?.termo) {
        docs = docs.filter(doc => 
          doc.nome.toLowerCase().includes(filtros.termo!.toLowerCase())
        );
      }
      
      if (filtros?.obraId) {
        docs = docs.filter(doc => doc.obra_id === filtros.obraId);
      }
      
      if (filtros?.contatoId) {
        docs = docs.filter(doc => doc.contato_id === filtros.contatoId);
      }
      
      setDocumentos(docs);
    } catch (error) {
      
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (documento: Documento) => {
    if (!documento.arquivo_url) return;
    
    try {
      // Criar link para download
      const link = document.createElement('a');
      link.href = documento.arquivo_url;
      link.download = documento.nome;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      
    }
  };

  const handleDelete = async (id: string) => {
    try {
      // TODO: Implementar exclusão de documento
  
      onDocumentDeleted?.();
      onDocumentosChange?.();
    } catch (error) {
      
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'pendente': { variant: 'secondary' as const, icon: Clock },
      'aprovado': { variant: 'default' as const, icon: UserCheck },
      'rejeitado': { variant: 'destructive' as const, icon: UserX }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    if (!config) return null;

    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const documentosFiltrados = useMemo(() => {
    return documentosParaExibir; // Já filtrados na loadDocumentos ou vindos de fora
  }, [documentosParaExibir]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="border rounded-md">
        <Table>
          <TableHeader className="bg-muted">
            <TableRow>
              <TableHead>Documento</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Contato</TableHead>
              <TableHead>Valor</TableHead>
              <TableHead>Data de Vencimento</TableHead>
              <TableHead>Anexos</TableHead>
              <TableHead className="w-[100px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {documentosFiltrados.map((documento) => (
              <TableRow 
                key={documento.id}
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => (onSelectDocumento || onDocumentClick)?.(documento)}
              >
                <TableCell>
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <FileText className="h-4 w-4 text-gray-600" />
                    </div>
                    <div>
                      <div className="font-medium">{documento.nome}</div>
                      <div className="text-sm text-gray-500">{documento.tipo_arquivo}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(documento.status)}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <span>{getContatoNome(documento.contato_id)}</span>
                    {!documento.contato_id && documento.dados_extraidos?.contato_detectado && (
                      <div className="flex items-center gap-1 text-yellow-600">
                        <Sparkles className="h-3 w-3" />
                        <span className="text-xs">
                          {documento.dados_extraidos.contato_detectado.nome_empresa || 
                           documento.dados_extraidos.fornecedor ||
                           'Detectado'}
                        </span>
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {documento.valor_total ? (
                    new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(documento.valor_total)
                  ) : (
                    <span className="text-xs text-muted-foreground">-</span>
                  )}
                </TableCell>
                <TableCell>
                  {documento.data_vencimento ? (
                    format(new Date(documento.data_vencimento), 'dd/MM/yyyy', { locale: ptBR })
                  ) : (
                    <span className="text-xs text-muted-foreground">-</span>
                  )}
                </TableCell>
                <TableCell>
                  {documento.quantidade_anexos && documento.quantidade_anexos > 0 ? (
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Paperclip className="h-4 w-4" />
                      <span>{documento.quantidade_anexos}</span>
                    </div>
                  ) : (
                    <span className="text-xs text-muted-foreground">-</span>
                  )}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-7 w-7">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem 
                        onClick={(e) => {
                          e.stopPropagation();
                          (onSelectDocumento || onDocumentClick)?.(documento);
                        }}
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        Ver detalhes
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDownload(documento);
                        }}
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(documento.id);
                        }}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Excluir
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {documentosFiltrados.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          Nenhum documento encontrado
        </div>
      )}
    </div>
  );
}