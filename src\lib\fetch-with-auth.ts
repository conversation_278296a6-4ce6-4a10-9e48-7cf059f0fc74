/**
 * Utilitário para fazer requisições fetch com autenticação
 * Garante que os cookies de autenticação sejam enviados com a requisição
 */

type FetchOptions = RequestInit & {
  headers?: Record<string, string>;
};

/**
 * Função para fazer requisições fetch com autenticação
 * @param url URL da requisição
 * @param options Opções do fetch
 * @returns Promise com a resposta
 */
export async function fetchWithAuth(url: string, options: FetchOptions = {}) {
  // Garantir que credentials seja 'include' para enviar cookies
  const fetchOptions: FetchOptions = {
    ...options,
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  };

  // Fazer a requisição
  return fetch(url, fetchOptions);
}

/**
 * Função para fazer requisições GET com autenticação
 * @param url URL da requisição
 * @param options Opções adicionais do fetch
 * @returns Promise com a resposta
 */
export async function getWithAuth<T = any>(url: string, options: FetchOptions = {}): Promise<T> {
  const response = await fetchWithAuth(url, {
    method: 'GET',
    ...options,
  });

  if (!response.ok) {
    // Se for erro de autenticação, tratar especificamente
    if (response.status === 401) {
      throw new Error('Erro de autenticação');
    }
    
    // Tentar obter detalhes do erro
    try {
      const errorData = await response.json();
      throw new Error(errorData.error || `Erro na requisição: ${response.status}`);
    } catch (e) {
      throw new Error(`Erro na requisição: ${response.status}`);
    }
  }

  return response.json();
}

/**
 * Função para fazer requisições POST com autenticação
 * @param url URL da requisição
 * @param data Dados a serem enviados no corpo da requisição
 * @param options Opções adicionais do fetch
 * @returns Promise com a resposta
 */
export async function postWithAuth<T = any>(url: string, data: any, options: FetchOptions = {}): Promise<T> {
  const response = await fetchWithAuth(url, {
    method: 'POST',
    body: JSON.stringify(data),
    ...options,
  });

  if (!response.ok) {
    // Se for erro de autenticação, tratar especificamente
    if (response.status === 401) {
      throw new Error('Erro de autenticação');
    }
    
    // Tentar obter detalhes do erro
    try {
      const errorData = await response.json();
      throw new Error(errorData.error || `Erro na requisição: ${response.status}`);
    } catch (e) {
      throw new Error(`Erro na requisição: ${response.status}`);
    }
  }

  return response.json();
}

/**
 * Função para fazer requisições PATCH com autenticação
 * @param url URL da requisição
 * @param data Dados a serem enviados no corpo da requisição
 * @param options Opções adicionais do fetch
 * @returns Promise com a resposta
 */
export async function patchWithAuth<T = any>(url: string, data: any, options: FetchOptions = {}): Promise<T> {
  const response = await fetchWithAuth(url, {
    method: 'PATCH',
    body: JSON.stringify(data),
    ...options,
  });

  if (!response.ok) {
    // Se for erro de autenticação, tratar especificamente
    if (response.status === 401) {
      throw new Error('Erro de autenticação');
    }
    
    // Tentar obter detalhes do erro
    try {
      const errorData = await response.json();
      throw new Error(errorData.error || `Erro na requisição: ${response.status}`);
    } catch (e) {
      throw new Error(`Erro na requisição: ${response.status}`);
    }
  }

  return response.json();
}

/**
 * Função para fazer requisições DELETE com autenticação
 * @param url URL da requisição
 * @param options Opções adicionais do fetch
 * @returns Promise com a resposta
 */
export async function deleteWithAuth<T = any>(url: string, options: FetchOptions = {}): Promise<T> {
  const response = await fetchWithAuth(url, {
    method: 'DELETE',
    ...options,
  });

  if (!response.ok) {
    // Se for erro de autenticação, tratar especificamente
    if (response.status === 401) {
      throw new Error('Erro de autenticação');
    }
    
    // Tentar obter detalhes do erro
    try {
      const errorData = await response.json();
      throw new Error(errorData.error || `Erro na requisição: ${response.status}`);
    } catch (e) {
      throw new Error(`Erro na requisição: ${response.status}`);
    }
  }

  return response.json();
}