'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { DatePicker } from '@/components/ui/date-picker';
import { Documento, ParcelaExtraida } from '@/types/documentos';
import { Loader2, Sparkles, Plus, Trash2, Calendar as CalendarIcon, ChevronsUpDown, MoreVertical, Pencil, X, Check, PlusCircle, Edit2 } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker as CustomDatePicker } from './custom-date-picker';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { createDateFromISOString, formatDateToISOString } from '@/lib/date-utils';
import { cn } from '@/lib/utils';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription } from '@/components/ui/sheet';
import { FormContato } from '@/app/(auth)/contatos/components/form-contato';
import { useContatosService } from '@/services/contatos';

// Schema de validação do formulário
const formSchema = z.object({
  fornecedor: z.string().min(1, 'Fornecedor é obrigatório'),
  valor_total: z.string().min(1, 'Valor é obrigatório'),
  data_vencimento: z.date({
    required_error: 'Data de vencimento é obrigatória',
  }),
  forma_pagamento: z.string().min(1, 'Forma de pagamento é obrigatória'),
  tipo_lancamento: z.string().min(1, 'Tipo de lançamento é obrigatório'),
  descricao: z.string().min(1, 'Descrição é obrigatória'), // Mantido para o lançamento, não para o documento
  obra_id: z.string().optional(),
  contato_id: z.string().optional(),
  // Campos de status de pagamento
  status_pagamento: z.enum(['pendente', 'pago']),
  data_pagamento: z.date().optional(),
  forma_pagamento_realizada: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface FormDocumentoProps {
  documento: Documento;
  onSubmit: (data: any) => void;
  onSalvar?: (data: any) => void; // Opcional para compatibilidade com componentes existentes
  onRejeitar: () => void;
  isSubmitting: boolean;
}

export function FormDocumento({ documento, onSubmit, onSalvar, onRejeitar, isSubmitting }: FormDocumentoProps) {
  const [parcelas, setParcelas] = useState<ParcelaExtraida[]>([]);
  const [obras, setObras] = useState<any[]>([]);
  const [contatos, setContatos] = useState<any[]>([]);
  const [usarParcelas, setUsarParcelas] = useState(false);

  // Estados para o componente de contato
  const [openContatoPopover, setOpenContatoPopover] = useState(false);
  const [searchTermContato, setSearchTermContato] = useState('');
  const [creatingContato, setCreatingContato] = useState(false);
  const [loadingContato, setLoadingContato] = useState(false);
  const [contatoDrawerOpen, setContatoDrawerOpen] = useState(false);
  const [editContatoId, setEditContatoId] = useState<string | null>(null);

  // Usar o serviço de contatos
  const contatosService = useContatosService();

  // Função para formatar valor monetário
  const formatarValorMonetario = (valor: number | null | undefined): string => {
    if (valor === null || valor === undefined) return '';

    // Garantir que o valor seja tratado como número
    const valorNumerico = typeof valor === 'string'
      ? parseFloat(String(valor).replace(/[^\d,.-]/g, '').replace(',', '.'))
      : valor;

    if (isNaN(valorNumerico)) return '';

    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(valorNumerico);
  };

  // Função para mapear valores da IA para valores do Select
  const mapearFormaPagamento = (formaPagamentoIA: string | undefined): string | undefined => {
    if (!formaPagamentoIA) return undefined;
    
    const mapeamento: Record<string, string> = {
      'PIX': 'pix',
      'TED': 'transferencia',
      'DOC': 'transferencia', 
      'Transferência': 'transferencia',
      'Transferencia': 'transferencia',
      'Cartão': 'cartao',
      'Cartao': 'cartao',
      'Cartão de Débito': 'cartao',
      'Cartão de Crédito': 'cartao',
      'Dinheiro': 'dinheiro',
      'Boleto': 'boleto',
      'Outros': 'transferencia' // Mapear "Outros" para transferência como padrão
    };
    
    return mapeamento[formaPagamentoIA] || formaPagamentoIA.toLowerCase();
  };

  // Função para determinar a forma de pagamento do lançamento baseada no tipo de documento
  const determinarFormaPagamento = (dadosExtraidos: any): string => {
    const tipoDocumento = dadosExtraidos?.tipo_documento;
    
    // Se for boleto, a forma de pagamento é boleto
    if (tipoDocumento === 'boleto') {
      return 'boleto';
    }
    
    // Se for comprovante e tiver forma de pagamento realizada, usar ela
    if (tipoDocumento === 'comprovante' && dadosExtraidos?.forma_pagamento_realizada) {
      return mapearFormaPagamento(dadosExtraidos.forma_pagamento_realizada) || 'pix';
    }
    
    // Para outros casos, usar anexo como padrão
    return 'anexo';
  };

  // Inicializar o formulário
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fornecedor: documento.fornecedor || documento.dados_extraidos?.fornecedor || '',
      valor_total: documento.valor_total
        ? formatarValorMonetario(documento.valor_total)
        : documento.dados_extraidos?.valor_total
          ? formatarValorMonetario(documento.dados_extraidos.valor_total)
          : '',
      data_vencimento: documento.data_vencimento
        ? createDateFromISOString(documento.data_vencimento) || new Date()
        : documento.dados_extraidos?.data_vencimento
          ? createDateFromISOString(documento.dados_extraidos.data_vencimento) || new Date()
          : new Date(),
      forma_pagamento: determinarFormaPagamento(documento.dados_extraidos),
      tipo_lancamento: 'despesa',
      descricao: documento.descricao || (documento.fornecedor
        ? `Pagamento ${documento.fornecedor}`
        : documento.dados_extraidos?.fornecedor
          ? `Pagamento ${documento.dados_extraidos.fornecedor}`
          : 'Pagamento'),
      obra_id: documento.obra_id || undefined,
      contato_id: documento.contato_id || undefined,
      // Valores padrão para status de pagamento
      status_pagamento: (documento.dados_extraidos?.status_pagamento as 'pendente' | 'pago') || 'pendente',
      data_pagamento: documento.dados_extraidos?.data_pagamento 
        ? createDateFromISOString(documento.dados_extraidos.data_pagamento) 
        : undefined,
      forma_pagamento_realizada: mapearFormaPagamento(documento.dados_extraidos?.forma_pagamento_realizada),
    },
  });

  // Carregar obras e contatos
  useEffect(() => {
    const fetchObras = async () => {
      try {
        const response = await fetch('/api/obras');
        if (response.ok) {
          const result = await response.json();
          // A API retorna { data: [...] }, então precisamos acessar result.data
          setObras(Array.isArray(result.data) ? result.data : []);
        }
      } catch (error) {
        
        setObras([]); // Garantir que obras seja sempre um array
      }
    };

    const carregarDados = async () => {
      await fetchContatos();
      await fetchObras();

      // Verificar se há dados de contato detectado pela IA para sugerir criação
      if (!documento.contato_id && documento.dados_extraidos?.contato_detectado) {
        // Primeiro, tentar encontrar um contato existente correspondente
        const fornecedorDetectado = documento.dados_extraidos.contato_detectado.nome_empresa || 
                                   documento.dados_extraidos.fornecedor;
        
        if (fornecedorDetectado) {
          try {
            const contatoEncontrado = await contatosService.buscarContatoPorNome(fornecedorDetectado);
            if (contatoEncontrado) {
              // Preencher o campo de contato automaticamente
              form.setValue('contato_id', contatoEncontrado.id);
              toast.success(`Contato "${contatoEncontrado.nome_empresa}" encontrado automaticamente!`, {
                duration: 3000
              });
            } else {
              // Mostrar notificação sobre dados detectados sem criar automaticamente
              toast.info(`Dados de contato detectados: "${fornecedorDetectado}". Será criado automaticamente ao aprovar o documento.`, {
                duration: 5000
              });
            }
          } catch (error) {
            
          }
        }
      }
    };

    carregarDados();
  }, [documento]);

  // Atualizar formulário quando documento mudar (incluindo após re-análise)
  useEffect(() => {


    // Reinicializar todo o formulário com os dados atualizados do documento
    const defaultValues = {
      fornecedor: documento.fornecedor || documento.dados_extraidos?.fornecedor || '',
      valor_total: formatarValorMonetario(documento.valor_total || documento.dados_extraidos?.valor_total),
      data_vencimento: documento.data_vencimento
        ? createDateFromISOString(documento.data_vencimento)
        : documento.dados_extraidos?.data_vencimento
          ? createDateFromISOString(documento.dados_extraidos.data_vencimento)
          : new Date(),
      forma_pagamento: determinarFormaPagamento(documento.dados_extraidos),
       tipo_lancamento: 'despesa',
      descricao: documento.descricao || (documento.fornecedor
        ? `Pagamento ${documento.fornecedor}`
        : documento.dados_extraidos?.fornecedor
          ? `Pagamento ${documento.dados_extraidos.fornecedor}`
          : 'Pagamento'),
      obra_id: documento.obra_id || undefined,
      contato_id: documento.contato_id || undefined,
      // Valores de status de pagamento
      status_pagamento: (documento.dados_extraidos?.status_pagamento as 'pendente' | 'pago') || 'pendente',
      data_pagamento: documento.dados_extraidos?.data_pagamento 
        ? createDateFromISOString(documento.dados_extraidos.data_pagamento) 
        : undefined,
            forma_pagamento_realizada: mapearFormaPagamento(documento.dados_extraidos?.forma_pagamento_realizada),
    };



    // Resetar o formulário com os novos valores
    form.reset(defaultValues);
  }, [documento.id, documento.dados_extraidos?.status_pagamento, form]);

  // Função para carregar contatos
  const fetchContatos = async () => {
    try {
      setLoadingContato(true);
      const data = await contatosService.listarContatos();
      setContatos(data);
    } catch (error) {
      
      toast.error("Erro ao carregar contatos");
    } finally {
      setLoadingContato(false);
    }
  };

  // Função para criar um novo contato
  const handleCreateContato = async (nomeContato: string) => {
    if (!nomeContato || creatingContato) return;
    setCreatingContato(true);
    try {
      const novoContato = await contatosService.criarContato({
        nome_empresa: nomeContato,
        tipo: 'fornecedor'
      });

      if (novoContato) {
        setContatos((prev) => [...prev, novoContato]);
        form.setValue('contato_id', novoContato.id);
        toast.success(`Contato "${novoContato.nome_empresa}" criado com sucesso!`);
        setSearchTermContato("");
        setOpenContatoPopover(false);
      } else {
        throw new Error("Falha ao criar contato, resposta vazia.");
      }
    } catch (error: any) {
      
      toast.error(error.message || "Erro ao criar contato");
    } finally {
      setCreatingContato(false);
    }
  };

  // Função para editar um contato
  const handleEditContato = (id: string | null | undefined) => {
    if (id) {
      setEditContatoId(id);
      requestAnimationFrame(() => {
        setContatoDrawerOpen(true);
      });
    }
  };

  // Função para limpar a seleção de contato
  const limparContato = () => {
    form.setValue("contato_id", undefined);
    form.trigger("contato_id");
  };

  // Função para lidar com o sucesso da edição/criação de contato
  const handleContatoSuccess = async (): Promise<void> => {
    await fetchContatos();
    setContatoDrawerOpen(false);
  };

  // Função para controlar o fechamento do drawer com animação
  const handleDrawerOpenChange = (open: boolean) => {
    setContatoDrawerOpen(open);
    if (!open) {
      setTimeout(() => {
        setEditContatoId(null);
      }, 300);
    }
  };

  // Filtrar contatos com base no termo de busca
  const filteredContatos = searchTermContato
    ? contatos.filter(cont => {
        const searchTerm = searchTermContato.toLowerCase();
        return (
          (cont.nome_empresa && cont.nome_empresa.toLowerCase().includes(searchTerm)) ||
          (cont.nome_razao && cont.nome_razao.toLowerCase().includes(searchTerm)) ||
          (cont.nome_contato && cont.nome_contato.toLowerCase().includes(searchTerm))
        );
      })
    : contatos;

  // Inicializar parcelas a partir dos dados extraídos
  useEffect(() => {
    if (documento.dados_extraidos?.parcelas && documento.dados_extraidos.parcelas.length > 0) {
      // Processar parcelas extraídas pela IA

      // Criar um mapa para rastrear números de parcelas já usados
      const numerosUsados = new Set();

      // Garantir que os valores das parcelas estejam formatados corretamente
      // e que cada parcela tenha um número único
      const parcelasFormatadas = documento.dados_extraidos.parcelas.map((parcela, index) => {
        // Garantir que o valor seja positivo e não seja dividido por 1000
        // Verificar se o valor parece estar dividido por 1000 (comparando com o valor total)
        let valorCorrigido = Math.abs(parcela.valor);

        // Se o valor total do documento estiver disponível, usar como referência
        if (documento.valor_total && documento.valor_total > 0) {
          // Se o valor da parcela for muito menor que o valor total (menos de 1%),
          // provavelmente está com escala errada
          const valorEsperado = documento.valor_total / documento.dados_extraidos!.parcelas!.length;
          if (valorCorrigido < valorEsperado * 0.01) {
            // Multiplicar por 1000 para corrigir a escala
            valorCorrigido = valorCorrigido * 1000;
            // Corrigir escala da parcela
          }
        }

        // Garantir que cada parcela tenha um número único
        let numero = parcela.numero || index + 1;
        while (numerosUsados.has(numero)) {
          numero++;
        }
        numerosUsados.add(numero);

        return {
          ...parcela,
          valor: valorCorrigido,
          numero: numero
        };
      });

      // Atualizar estado com parcelas processadas
      setParcelas(parcelasFormatadas);
      setUsarParcelas(true);
    }
  }, [documento]);

  // Extrair valor numérico de um valor formatado
  const extrairValorNumerico = (valorFormatado: string): number => {
    // Verificar se o valor está vazio
    if (!valorFormatado) return 0;

    // Remover todos os caracteres não numéricos, exceto vírgula e ponto
    // Primeiro, remover o símbolo de moeda e espaços
    let valorLimpo = valorFormatado.replace(/[R$\s]/g, '');

    // Tratar valores com pontos de milhar e vírgulas decimais (formato brasileiro)
    // Ex: "1.234,56" -> 1234.56
    if (valorLimpo.includes('.') && valorLimpo.includes(',')) {
      // Remover pontos de milhar e substituir vírgula por ponto
      valorLimpo = valorLimpo.replace(/\./g, '').replace(',', '.');
    } else if (valorLimpo.includes(',')) {
      // Apenas substituir vírgula por ponto
      valorLimpo = valorLimpo.replace(',', '.');
    }

    // Converter para número
    const valorNumerico = parseFloat(valorLimpo);

    // Verificar se o valor é válido
    if (isNaN(valorNumerico)) {
      return 0;
    }
    return valorNumerico;
  };

  // Adicionar parcela
  const adicionarParcela = () => {
    // Obter o valor total do formulário
    const valorFormatado = form.getValues('valor_total');

    // Extrair o valor numérico
    const valorTotal = extrairValorNumerico(valorFormatado);

    // Obter a data de vencimento
    const dataVencimento = form.getValues('data_vencimento');

    // Calcular valor da parcela (dividir o valor total pelo número de parcelas)
    const numParcelas = parcelas.length + 1;
    const valorParcela = valorTotal / numParcelas;

    // Criar nova parcela
    const novaParcela: ParcelaExtraida = {
      numero: numParcelas,
      valor: valorParcela,
      vencimento: formatDateToISOString(dataVencimento) || format(dataVencimento, 'yyyy-MM-dd'),
    };

    // Recalcular valores das parcelas existentes para distribuir igualmente
    const novasParcelas = [...parcelas].map(p => ({
      ...p,
      valor: valorParcela
    }));

    // Adicionar a nova parcela à lista
    const parcelasAtualizadas = [...novasParcelas, novaParcela];
    setParcelas(parcelasAtualizadas);
  };

  // Remover parcela
  const removerParcela = (index: number) => {
    // Se só tiver uma parcela, não permitir remover
    if (parcelas.length <= 1) {
      return;
    }

    const novasParcelas = [...parcelas];
    novasParcelas.splice(index, 1);

    // Extrair valor total do formulário
    const valorFormatado = form.getValues('valor_total');
    const valorTotal = extrairValorNumerico(valorFormatado);

    // Recalcular valores das parcelas para distribuir igualmente
    const valorParcela = valorTotal / novasParcelas.length;

    // Renumerar parcelas e atualizar valores
    novasParcelas.forEach((parcela, i) => {
      parcela.numero = i + 1;
      parcela.valor = valorParcela;
    });

    // Atualizar estado com parcelas recalculadas
    setParcelas(novasParcelas);
  };

  // Atualizar parcela
  const atualizarParcela = (index: number, campo: keyof ParcelaExtraida, valor: any) => {
    const novasParcelas = [...parcelas];

    // Se o campo for valor, garantir que seja um número
    if (campo === 'valor' && typeof valor === 'string') {
      valor = extrairValorNumerico(valor);
    }

    novasParcelas[index] = {
      ...novasParcelas[index],
      [campo]: valor,
    };

    // Se o campo for valor, recalcular os valores das outras parcelas
    if (campo === 'valor') {
      // Obter o valor total
      const valorTotal = extrairValorNumerico(form.getValues('valor_total'));

      // Calcular a soma dos valores das parcelas, exceto a que está sendo editada
      const somaOutrasParcelas = novasParcelas.reduce((sum, p, i) =>
        i === index ? sum : sum + p.valor, 0);

      // Verificar se a soma ultrapassa o valor total
      if (somaOutrasParcelas + valor > valorTotal) {
        // Ajustar o valor da parcela para não ultrapassar o valor total
        novasParcelas[index].valor = valorTotal - somaOutrasParcelas;
      }
    }

    // Atualizar estado com parcelas modificadas
    setParcelas(novasParcelas);
  };

  // Formatar valor para exibição
  const formatarValor = (valor: string) => {
    const numero = valor.replace(/\D/g, '');
    const valorFormatado = (parseFloat(numero) / 100).toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    });
    return valorFormatado;
  };

  // Handler de submit
  const handleSubmit = (data: FormValues) => {
    // Converter valores usando a função de extração
    const valorTotal = extrairValorNumerico(data.valor_total);

    // Verificar se há parcelas e se os valores estão corretos
    let parcelasFinais = [];

    if (usarParcelas && parcelas.length > 0) {
      // Usar as parcelas definidas pelo usuário
      parcelasFinais = parcelas.map((p, index) => ({
        numero: p.numero || index + 1, // Garantir que cada parcela tenha um número
        valor: p.valor,
        vencimento: p.vencimento,
        status: 'pendente'
      }));

      // Verificar se a soma das parcelas está próxima do valor total
      const somaParcelas = parcelasFinais.reduce((sum, p) => sum + p.valor, 0);

      // Se houver uma diferença significativa, ajustar as parcelas proporcionalmente
      if (Math.abs(somaParcelas - valorTotal) > 0.1) {

        // Calcular o fator de ajuste
        const fatorAjuste = valorTotal / somaParcelas;

        // Ajustar todas as parcelas proporcionalmente
        let somaAjustada = 0;
        for (let i = 0; i < parcelasFinais.length - 1; i++) {
          // Ajustar o valor da parcela
          parcelasFinais[i].valor = Math.round((parcelasFinais[i].valor * fatorAjuste) * 100) / 100;
          somaAjustada += parcelasFinais[i].valor;
        }

        // Ajustar a última parcela para garantir que a soma seja exatamente igual ao valor total
        const valorUltimaParcela = valorTotal - somaAjustada;

        // Garantir que o valor da última parcela seja positivo
        if (valorUltimaParcela <= 0) {
          // Se o valor calculado for negativo ou zero, redistribuir igualmente
          const valorPorParcela = valorTotal / parcelasFinais.length;
          parcelasFinais.forEach(p => {
            p.valor = valorPorParcela;
          });
        } else {
          parcelasFinais[parcelasFinais.length - 1].valor = valorUltimaParcela;
        }

        // Parcelas ajustadas para corresponder ao valor total
      }
    } else {
      // Criar uma única parcela com o valor total
      parcelasFinais = [{
        numero: 1,
        valor: valorTotal,
        vencimento: formatDateToISOString(data.data_vencimento) || format(data.data_vencimento, 'yyyy-MM-dd'),
        status: 'pendente'
      }];
    }

    // Preparar dados para criar lançamento
    const dadosLancamento = {
      descricao: data.descricao,
      valor_total: valorTotal,
      data_competencia: formatDateToISOString(data.data_vencimento) || format(data.data_vencimento, 'yyyy-MM-dd'),
      forma_pagamento: data.forma_pagamento,
      tipo_lancamento: data.tipo_lancamento,
      status: data.status_pagamento === 'pago' ? 'Pago' : 'Em aberto',
      obra_id: data.obra_id || null,
      contato_id: data.contato_id || null,
      // Campos de status de pagamento
      data_pagamento: data.status_pagamento === 'pago' && data.data_pagamento 
        ? formatDateToISOString(data.data_pagamento) || format(data.data_pagamento, 'yyyy-MM-dd')
        : null,
      forma_pagamento_realizada: data.status_pagamento === 'pago' ? data.forma_pagamento_realizada : null,
      parcelas: parcelasFinais
    };

    // Enviar lançamento com parcelas processadas

    onSubmit(dadosLancamento);
  };

  return (
    <>
      <Form {...form}>
        <form 
          onSubmit={form.handleSubmit(handleSubmit)} 
          className="space-y-6"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
            control={form.control}
            name="fornecedor"
            render={({ field }) => (
              <FormItem className="flex flex-col justify-end">
                <FormLabel className="flex items-center">
                  Fornecedor
                  {documento.dados_extraidos?.fornecedor && (
                    <Sparkles className="h-4 w-4 ml-1 text-yellow-500" />
                  )}
                </FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    className="h-9 !pointer-events-auto !cursor-text relative" 
                    type="text"
                    placeholder="Digite o nome do fornecedor"
                    autoComplete="off"
                    onClick={(e) => {
                      e.stopPropagation();
                      e.currentTarget.focus();
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="valor_total"
            render={({ field }) => (
              <FormItem className="flex flex-col justify-end">
                <FormLabel className="flex items-center">
                  Valor Total
                  {documento.dados_extraidos?.valor_total && (
                    <Sparkles className="h-4 w-4 ml-1 text-yellow-500" />
                  )}
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    className="h-9 !pointer-events-auto !cursor-text relative"
                    type="text"
                    placeholder="R$ 0,00"
                    autoComplete="off"
                    onClick={(e) => {
                      e.stopPropagation();
                      e.currentTarget.focus();
                    }}
                    onChange={(e) => {
                      const valor = e.target.value.replace(/\D/g, '');
                      if (valor === '') {
                        field.onChange('');
                        return;
                      }
                      const valorFormatado = (parseFloat(valor) / 100).toLocaleString('pt-BR', {
                        style: 'currency',
                        currency: 'BRL',
                      });
                      field.onChange(valorFormatado);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="data_vencimento"
            render={({ field }) => (
              <FormItem className="flex flex-col justify-end">
                <FormLabel className="flex items-center">
                  Data de Vencimento
                  {documento.dados_extraidos?.data_vencimento && (
                    <Sparkles className="h-4 w-4 ml-1 text-yellow-500" />
                  )}
                </FormLabel>
                <FormControl>
                  <div className="relative pointer-events-auto z-[1000]">
                    <CustomDatePicker
                      value={field.value}
                      onChange={(date: Date | null) => field.onChange(date)}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="forma_pagamento"
            render={({ field }) => (
              <FormItem className="flex flex-col justify-end">
                <FormLabel>Forma de Pagamento</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="h-9">
                      <SelectValue placeholder="Selecione" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="bg-background text-foreground z-[100]">
                    <SelectItem value="boleto">Boleto</SelectItem>
                    <SelectItem value="pix">PIX</SelectItem>
                    <SelectItem value="cartao">Cartão</SelectItem>
                    <SelectItem value="dinheiro">Dinheiro</SelectItem>
                    <SelectItem value="transferencia">Transferência</SelectItem>
                    <SelectItem value="anexo">Anexo</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="tipo_lancamento"
            render={({ field }) => (
              <FormItem className="flex flex-col justify-end">
                <FormLabel>Tipo de Lançamento</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="h-9">
                      <SelectValue placeholder="Selecione" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="bg-background text-foreground z-[100]">
                    <SelectItem value="despesa">Despesa</SelectItem>
                    <SelectItem value="receita">Receita</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="descricao"
            render={({ field }) => (
              <FormItem className="flex flex-col justify-end">
                <FormLabel>Descrição</FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    className="h-9 !pointer-events-auto !cursor-text relative" 
                    placeholder="Digite a descrição"
                    onClick={(e) => {
                      e.stopPropagation();
                      e.currentTarget.focus();
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="obra_id"
            render={({ field }) => (
              <FormItem className="flex flex-col justify-end">
                <FormLabel>Obra</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="h-9">
                      <SelectValue placeholder="Selecione uma obra" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="bg-background text-foreground z-[100]">
                    {Array.isArray(obras) && obras.map((obra) => (
                      <SelectItem key={obra.id} value={obra.id}>
                        {obra.nome}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="contato_id"
            render={({ field }) => (
              <FormItem className="flex flex-col justify-end">
                <FormLabel className="flex items-center">
                  Contato
                  {documento.dados_extraidos?.contato_detectado && (
                    <div className="relative group">
                      <Sparkles className="h-4 w-4 ml-1 text-yellow-500" />
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity bg-black text-white text-xs rounded py-1 px-2 whitespace-nowrap z-10">
                        Contato detectado pela IA
                      </div>
                    </div>
                  )}
                </FormLabel>
                <div className="flex gap-2 items-stretch">
                  <div className="flex-1 min-w-0">
                    <div className="relative group">
                      <Popover open={openContatoPopover} onOpenChange={setOpenContatoPopover}>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              role="combobox"
                              aria-expanded={openContatoPopover}
                              className={cn(
                                "w-full justify-between h-9", 
                                !field.value && !documento.dados_extraidos?.contato_detectado && "text-muted-foreground",
                                documento.dados_extraidos?.contato_detectado && !field.value && "bg-yellow-50/70 border-yellow-300/70 hover:bg-yellow-50"
                              )}
                              disabled={loadingContato || creatingContato}
                            >
                              <div className="flex items-center justify-between w-full">
                                <div className="text-left truncate max-w-[calc(100%-30px)]">
                                  {field.value ? (
                                    // Contato selecionado
                                    (() => {
                                      const contato = contatos.find(c => c.id === field.value);
                                      return contato?.nome_empresa || contato?.nome_razao || contato?.nome_contato || "Contato selecionado";
                                    })()
                                                                  ) : documento.dados_extraidos?.contato_detectado ? (
                                  // Dados detectados pela IA
                                  <span className="font-medium text-yellow-800">
                                    {documento.dados_extraidos.contato_detectado.nome_empresa || "Contato Detectado"}
                                  </span>
                                ) : (
                                    // Estado padrão
                                    "Selecione o contato"
                                  )}
                                </div>
                                <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50 ml-1" />
                              </div>
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        
                        {/* Tooltip detalhado para dados detectados */}
                        {documento.dados_extraidos?.contato_detectado && !field.value && (
                          <div className="absolute top-full right-0 mt-2 w-80 max-w-[calc(100vw-1rem)] p-3 bg-yellow-50 border border-yellow-200 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-[9999]">
                            <h4 className="text-sm font-medium text-yellow-800 mb-2">Dados detectados pela IA:</h4>
                            <div className="space-y-1 text-xs text-yellow-700">
                              {documento.dados_extraidos.contato_detectado.nome_empresa && (
                                <div><span className="font-medium">Empresa:</span> {documento.dados_extraidos.contato_detectado.nome_empresa}</div>
                              )}
                              {documento.dados_extraidos.contato_detectado.cpf_cnpj && (
                                <div><span className="font-medium">CNPJ/CPF:</span> {documento.dados_extraidos.contato_detectado.cpf_cnpj}</div>
                              )}
                              {documento.dados_extraidos.contato_detectado.email && (
                                <div><span className="font-medium">Email:</span> {documento.dados_extraidos.contato_detectado.email}</div>
                              )}
                              {documento.dados_extraidos.contato_detectado.telefone && (
                                <div><span className="font-medium">Telefone:</span> {documento.dados_extraidos.contato_detectado.telefone}</div>
                              )}
                              {documento.dados_extraidos.contato_detectado.endereco && (
                                <div><span className="font-medium">Endereço:</span> {documento.dados_extraidos.contato_detectado.endereco}</div>
                              )}
                              {documento.dados_extraidos.contato_detectado.cidade && documento.dados_extraidos.contato_detectado.uf && (
                                <div><span className="font-medium">Cidade/UF:</span> {documento.dados_extraidos.contato_detectado.cidade}/{documento.dados_extraidos.contato_detectado.uf}</div>
                              )}
                              {documento.dados_extraidos.contato_detectado.cep && (
                                <div><span className="font-medium">CEP:</span> {documento.dados_extraidos.contato_detectado.cep}</div>
                              )}
                            </div>
                            <div className="mt-2 text-xs text-yellow-600 bg-yellow-100 p-2 rounded">
                              💡 Clique no campo para criar este contato automaticamente
                            </div>
                          </div>
                        )}
                      <PopoverContent className={cn("w-[--radix-popover-trigger-width] p-0 z-[100]", "bg-background text-foreground")}>
                        <Command>
                          <CommandInput
                            placeholder="Buscar ou criar contato..."
                            value={searchTermContato}
                            onValueChange={setSearchTermContato}
                          />
                          <CommandList>
                            <CommandEmpty>
                              {searchTermContato ? `Nenhum contato encontrado.` : 'Nenhum contato cadastrado.'}
                            </CommandEmpty>
                            <CommandGroup>
                              {/* Mostrar dados detectados como primeira opção */}
                              {documento.dados_extraidos?.contato_detectado && !field.value && (
                                <div className="border-b mb-2 pb-2">
                                  <div className="px-2 py-1.5 text-xs font-medium text-yellow-700 bg-yellow-50 flex items-center rounded">
                                    <Sparkles className="h-3 w-3 mr-1" />
                                    Dados detectados pela IA
                                  </div>
                                  <div className="relative group">
                                    <CommandItem
                                      key="ai-detected"
                                      value={documento.dados_extraidos?.contato_detectado?.nome_empresa || "contato-detectado"}
                                      onSelect={() => {
                                        handleCreateContato(documento.dados_extraidos?.contato_detectado?.nome_empresa || "Contato Detectado");
                                      }}
                                      className="text-yellow-700 bg-yellow-50/50 hover:bg-yellow-100 border border-yellow-200 rounded my-1"
                                    >
                                      <div className="flex items-center justify-between w-full">
                                        <div className="flex items-center">
                                          <Sparkles className="mr-2 h-4 w-4 text-yellow-500" />
                                          <span className="font-medium">
                                            {documento.dados_extraidos?.contato_detectado?.nome_empresa || "Contato Detectado"}
                                          </span>
                                        </div>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          className="h-6 w-6 p-0 hover:bg-yellow-200"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            // TODO: Abrir modal de edição dos dados detectados
                                            toast.info("Em breve: edição dos dados detectados");
                                          }}
                                        >
                                          <Edit2 className="h-3 w-3" />
                                        </Button>
                                      </div>
                                    </CommandItem>
                                    {/* Tooltip com dados completos */}
                                    <div className="absolute left-full top-0 ml-2 w-80 p-3 bg-yellow-50 border border-yellow-200 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-[200]">
                                      <h4 className="text-sm font-medium text-yellow-800 mb-2">Dados que serão criados:</h4>
                                      <div className="space-y-1 text-xs text-yellow-700">
                                        {documento.dados_extraidos?.contato_detectado?.nome_empresa && (
                                          <div><span className="font-medium">Empresa:</span> {documento.dados_extraidos.contato_detectado.nome_empresa}</div>
                                        )}
                                        {documento.dados_extraidos?.contato_detectado?.cpf_cnpj && (
                                          <div><span className="font-medium">CNPJ/CPF:</span> {documento.dados_extraidos.contato_detectado.cpf_cnpj}</div>
                                        )}
                                        {documento.dados_extraidos?.contato_detectado?.email && (
                                          <div><span className="font-medium">Email:</span> {documento.dados_extraidos.contato_detectado.email}</div>
                                        )}
                                        {documento.dados_extraidos?.contato_detectado?.telefone && (
                                          <div><span className="font-medium">Telefone:</span> {documento.dados_extraidos.contato_detectado.telefone}</div>
                                        )}
                                        {documento.dados_extraidos?.contato_detectado?.endereco && (
                                          <div><span className="font-medium">Endereço:</span> {documento.dados_extraidos.contato_detectado.endereco}</div>
                                        )}
                                        {documento.dados_extraidos?.contato_detectado?.cidade && documento.dados_extraidos?.contato_detectado?.uf && (
                                          <div><span className="font-medium">Cidade/UF:</span> {documento.dados_extraidos.contato_detectado.cidade}/{documento.dados_extraidos.contato_detectado.uf}</div>
                                        )}
                                        {documento.dados_extraidos?.contato_detectado?.cep && (
                                          <div><span className="font-medium">CEP:</span> {documento.dados_extraidos.contato_detectado.cep}</div>
                                        )}
                                      </div>
                                      <div className="mt-2 text-xs text-yellow-600 bg-yellow-100 p-2 rounded">
                                        💡 Será criado automaticamente ao aprovar
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )}
                              
                              {filteredContatos.map((cont) => (
                                <CommandItem
                                  key={cont.id}
                                  value={cont.nome_empresa || cont.nome_razao || cont.nome_contato || ""}
                                  onSelect={() => {
                                    form.setValue("contato_id", cont.id);
                                    setOpenContatoPopover(false);
                                    setSearchTermContato("");
                                  }}
                                >
                                  <Check className={cn("mr-2 h-4 w-4", field.value === cont.id ? "opacity-100" : "opacity-0")}/>
                                  {cont.nome_empresa || cont.nome_razao || cont.nome_contato || "Contato"}
                                </CommandItem>
                              ))}
                              {searchTermContato && !filteredContatos.some(c =>
                                (c.nome_empresa && c.nome_empresa.toLowerCase() === searchTermContato.toLowerCase()) ||
                                (c.nome_razao && c.nome_razao.toLowerCase() === searchTermContato.toLowerCase()) ||
                                (c.nome_contato && c.nome_contato.toLowerCase() === searchTermContato.toLowerCase())
                              ) && (
                                <CommandItem
                                  key="create"
                                  value={searchTermContato}
                                  onSelect={() => handleCreateContato(searchTermContato)}
                                  disabled={creatingContato}
                                  className="text-muted-foreground italic"
                                >
                                  <PlusCircle className={cn("mr-2 h-4 w-4", creatingContato && "animate-spin")} />
                                  {creatingContato ? `Criando "${searchTermContato}"...` : `Criar "${searchTermContato}"` }
                                </CommandItem>
                              )}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    </div>
                  </div>

                  {/* Dropdown menu com ações para o contato */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-9 w-9 flex-shrink-0"
                        disabled={loadingContato}
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-background text-foreground z-[100]">
                      <DropdownMenuItem
                        onClick={() => handleEditContato(field.value)}
                        disabled={!field.value}
                        className={!field.value ? "text-muted-foreground" : ""}
                      >
                        <Pencil className="mr-2 h-4 w-4" />
                        Editar Contato
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => limparContato()}
                        disabled={!field.value}
                        className={!field.value ? "text-muted-foreground" : "text-destructive"}
                      >
                        <X className="mr-2 h-4 w-4" />
                        Limpar seleção
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />


        </div>

        {/* Seção de Status de Pagamento */}
        <Accordion type="single" collapsible defaultValue="status-pagamento">
          <AccordionItem value="status-pagamento">
            <AccordionTrigger className="text-sm font-medium">
              Status de Pagamento
            </AccordionTrigger>
            <AccordionContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="status_pagamento"
                  render={({ field }) => (
                    <FormItem className="flex flex-col justify-end">
                      <FormLabel>Status</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          // Limpar campos relacionados se mudou para pendente
                          if (value === 'pendente') {
                            form.setValue('data_pagamento', undefined);
                            form.setValue('forma_pagamento_realizada', undefined);
                          }
                        }}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="h-9">
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-background text-foreground z-[100]">
                          <SelectItem value="pendente">Pendente</SelectItem>
                          <SelectItem value="pago">Pago</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="data_pagamento"
                  render={({ field }) => (
                    <FormItem className="flex flex-col justify-end">
                      <FormLabel>Data do Pagamento</FormLabel>
                      <FormControl>
                        <div className="relative pointer-events-auto z-[1000]">
                          <CustomDatePicker
                            value={field.value}
                            onChange={(date: Date | null) => field.onChange(date)}
                            disabled={form.watch('status_pagamento') === 'pendente'}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="forma_pagamento_realizada"
                  render={({ field }) => (
                    <FormItem className="flex flex-col justify-end">
                      <FormLabel>Forma de Pagamento Realizada</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={form.watch('status_pagamento') === 'pendente'}
                      >
                        <FormControl>
                          <SelectTrigger className="h-9">
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-background text-foreground z-[100]">
                          <SelectItem value="boleto">Boleto</SelectItem>
                          <SelectItem value="pix">PIX</SelectItem>
                          <SelectItem value="cartao">Cartão</SelectItem>
                          <SelectItem value="dinheiro">Dinheiro</SelectItem>
                          <SelectItem value="transferencia">Transferência</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        {/* Seção de parcelas */}
        <Accordion type="single" collapsible defaultValue="parcelas">
          <AccordionItem value="parcelas">
            <AccordionTrigger className="text-sm font-medium">
              Parcelas
              {documento.dados_extraidos?.parcelas && documento.dados_extraidos.parcelas.length > 0 && (
                <Sparkles className="h-4 w-4 ml-1 text-yellow-500" />
              )}
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="usar-parcelas"
                    checked={usarParcelas}
                    onChange={(e) => setUsarParcelas(e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="usar-parcelas" className="text-sm">
                    Usar parcelamento
                  </label>
                </div>

                {usarParcelas && (
                  <>
                    <div className="border rounded-md overflow-hidden">
                      <table className="w-full">
                        <thead className="bg-muted">
                          <tr>
                            <th className="px-4 py-2 text-left text-xs font-medium">Nº</th>
                            <th className="px-4 py-2 text-left text-xs font-medium">Valor</th>
                            <th className="px-4 py-2 text-left text-xs font-medium">Vencimento</th>
                            <th className="px-4 py-2 text-right text-xs font-medium">Ações</th>
                          </tr>
                        </thead>
                        <tbody>
                          {parcelas.map((parcela, index) => (
                            <tr key={index} className="border-t">
                              <td className="px-4 py-2 text-sm">{parcela.numero}</td>
                              <td className="px-4 py-2 text-sm">
                                <Input
                                  value={formatarValorMonetario(parcela.valor)}
                                  onChange={(e) => {
                                    // Extrair apenas os números do valor formatado
                                    const valorLimpo = e.target.value.replace(/[^\d,.-]/g, '').replace(',', '.');
                                    const valor = parseFloat(valorLimpo);
                                    if (!isNaN(valor)) {
                                      atualizarParcela(index, 'valor', valor);
                                    }
                                  }}
                                  className="h-8 text-sm"
                                />
                              </td>
                              <td className="px-4 py-2 text-sm">
                                <div className="relative">
                                  <Popover>
                                    <PopoverTrigger asChild>
                                      <Button
                                        variant="outline"
                                        className="w-full h-8 justify-start text-left font-normal text-sm"
                                      >
                                        <CalendarIcon className="mr-2 h-3 w-3" />
                                        {parcela.vencimento ?
                                          (createDateFromISOString(parcela.vencimento) ?
                                            format(createDateFromISOString(parcela.vencimento)!, 'dd/MM/yyyy', { locale: ptBR }) :
                                            "DD/MM/AAAA")
                                          : "DD/MM/AAAA"}
                                      </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0 bg-background text-foreground z-50">
                                      <Calendar
                                        mode="single"
                                        selected={parcela.vencimento ? createDateFromISOString(parcela.vencimento) : undefined}
                                        onSelect={(date) => {
                                          if (date) {
                                            const formattedDate = formatDateToISOString(date) || format(date, 'yyyy-MM-dd');
                                            atualizarParcela(index, 'vencimento', formattedDate);
                                          }
                                        }}
                                        initialFocus
                                        locale={ptBR}
                                      />
                                    </PopoverContent>
                                  </Popover>
                                </div>
                              </td>
                              <td className="px-4 py-2 text-right">
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => removerParcela(index)}
                                  className="h-8 w-8"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={adicionarParcela}
                      className="flex items-center"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Adicionar Parcela
                    </Button>
                  </>
                )}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        {/* Buttons moved to fixed footer */}
          <div className="hidden">
            <Button type="submit" id="form-submit-button">
              Aprovar e Criar Lançamento
            </Button>
            {onSalvar && (
              <Button type="button" id="form-save-button" onClick={() => {
                const data = form.getValues();
                onSalvar(data);
              }}>
                Salvar
              </Button>
            )}
          </div>
        </form>
      </Form>

      {/* Drawer para edição de contato */}
      <Sheet open={contatoDrawerOpen} onOpenChange={handleDrawerOpenChange}>
        <SheetContent className="sm:max-w-md overflow-y-auto">
          <SheetHeader>
            <SheetTitle>
              {editContatoId ? 'Editar Contato' : 'Novo Contato'}
            </SheetTitle>
            <SheetDescription>
              {editContatoId ? 'Edite os dados do contato' : 'Preencha os dados para criar um novo contato'}
            </SheetDescription>
          </SheetHeader>
          <div className="py-6">
            <FormContato
              contato={contatos.find(c => c.id === editContatoId) || null}
              onSuccess={handleContatoSuccess}
              onCancel={() => setContatoDrawerOpen(false)}
            />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}