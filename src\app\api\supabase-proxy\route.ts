import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';

// Função para criar um cliente Supabase com a chave de serviço
const createServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    
    return null;
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

// Função genérica para processar requisições de qualquer método
async function handleRequest(request: NextRequest, method: string) {
  try {
    // Extrair o caminho da URL
    const url = new URL(request.url);
    const path = url.searchParams.get('path');

    if (!path) {
      
      return NextResponse.json({ error: 'Parâmetro path é obrigatório' }, { status: 400 });
    }

    // Obter o corpo da requisição para métodos que o suportam
    let body = null;
    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
      try {
        // Tentar obter o corpo como JSON, mas não falhar se não houver corpo
        const contentType = request.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const text = await request.text();
          if (text && text.trim()) {
            try {
              body = JSON.parse(text);
            } catch (parseError) {
              return NextResponse.json({ error: 'Corpo da requisição inválido' }, { status: 400 });
            }
          }
        }
      } catch (parseError) {
        // Ignorar erros de parse
      }
    }

    // Criar cliente Supabase com a chave de serviço
    const supabase = createServiceClient();

    if (!supabase) {
      return NextResponse.json({ error: 'Não foi possível criar o cliente Supabase' }, { status: 500 });
    }

    // Configurar a requisição
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const fetchOptions: RequestInit = {
      method,
      headers: {
        'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || '',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json',
        'X-Client-Info': 'supabase-proxy'
      }
    };

    // Adicionar corpo para métodos que o suportam
    if (body && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
      fetchOptions.body = JSON.stringify(body);
      
    } else if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
      // Para métodos que aceitam corpo, mas nenhum foi fornecido, enviar um objeto vazio
      // Isso é especialmente importante para DELETE com filtros in()
      fetchOptions.body = JSON.stringify({});
      
    }

    // Extrair parâmetros de consulta da URL original
    const queryParams = new URLSearchParams();
    for (const [key, value] of url.searchParams.entries()) {
      if (key !== 'path') {
        queryParams.append(key, value);
      }
    }

    // Construir a URL completa com parâmetros de consulta
    let fullUrl = `${supabaseUrl}${path}`;
    const queryString = queryParams.toString();
    if (queryString) {
      fullUrl += `?${queryString}`;
    }

    // Fazer a requisição ao Supabase

    const response = await fetch(fullUrl, fetchOptions);

    // Tentar obter o conteúdo da resposta independentemente do content-length
    try {
      const text = await response.text();
      let data;

      // Tentar analisar como JSON se houver conteúdo
      if (text && text.trim()) {
        try {
          data = JSON.parse(text);
  
        } catch (jsonError) {

// Retornar o texto bruto se não for JSON válido
          data = { text };
        }
      } else {
        // Resposta vazia
        data = {};

      }

      // Retornar os dados com o status HTTP correto
      return NextResponse.json(data, { status: response.status });
    } catch (responseError) {
      
      return NextResponse.json({ error: 'Erro ao processar resposta do Supabase' }, { status: 500 });
    }
  } catch (error: any) {
    
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

// Implementar handlers para cada método HTTP
export async function GET(request: NextRequest) {
  return handleRequest(request, 'GET');
}

export async function POST(request: NextRequest) {
  return handleRequest(request, 'POST');
}

export async function PUT(request: NextRequest) {
  return handleRequest(request, 'PUT');
}

export async function PATCH(request: NextRequest) {
  return handleRequest(request, 'PATCH');
}

export async function DELETE(request: NextRequest) {
  return handleRequest(request, 'DELETE');
}