# Guia de Componentes de Sobreposição (Overlays)

Este guia explica como trabalhar com componentes de sobreposição como Popover, DropdownMenu, Select, etc., garantindo que eles funcionem corretamente em todos os contextos do aplicativo.

## Problema Comum: Fundos Transparentes

Um problema frequente é a aparência de fundos transparentes em componentes como Popover, DropdownMenu e Select, especialmente quando:

1. Estão aninhados dentro de outros componentes
2. Estão dentro de componentes de overlay como Sheet, Dialog, Drawer
3. Precisam aparecer na frente de outros elementos

Isso resulta em:
- Conteúdo abaixo sendo visível através do componente
- Problema de legibilidade
- Experiência do usuário prejudicada

## Solução: Função `overlayStyles()`

Para resolver este problema, criamos a função utilitária `overlayStyles()` em `src/lib/utils.ts`:

```tsx
import { overlayStyles } from '@/lib/utils'

// Uso no componente
<PopoverContent className={overlayStyles()}>
  Conteúdo do Popover
</PopoverContent>
```

### Parâmetros da função

A função aceita um parâmetro opcional que determina o contexto do overlay:

- `'default'` (padrão): Para componentes independentes (z-index: 50)
- `'nested'`: Para componentes aninhados dentro de outros overlays (z-index: 100)
- `'inside-sheet'`: Para componentes dentro de Sheet ou Dialog (z-index: 200)

## Como Usar em Diferentes Componentes

### Popover

```tsx
import { overlayStyles } from '@/lib/utils'

<Popover>
  <PopoverTrigger>Clique aqui</PopoverTrigger>
  <PopoverContent className={overlayStyles()}>
    Conteúdo do Popover
  </PopoverContent>
</Popover>
```

### DropdownMenu

```tsx
<DropdownMenu>
  <DropdownMenuTrigger>Abrir menu</DropdownMenuTrigger>
  <DropdownMenuContent className={overlayStyles()}>
    <DropdownMenuItem>Item 1</DropdownMenuItem>
    <DropdownMenuItem>Item 2</DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
```

### Select

```tsx
<Select>
  <SelectTrigger>Selecione uma opção</SelectTrigger>
  <SelectContent className={overlayStyles()}>
    <SelectItem value="1">Opção 1</SelectItem>
    <SelectItem value="2">Opção 2</SelectItem>
  </SelectContent>
</Select>
```

### Componentes aninhados (um dentro do outro)

```tsx
<Popover>
  <PopoverTrigger>Abrir popover</PopoverTrigger>
  <PopoverContent className={overlayStyles()}>
    <DropdownMenu>
      <DropdownMenuTrigger>Abrir submenu</DropdownMenuTrigger>
      <DropdownMenuContent className={overlayStyles('nested')}>
        <DropdownMenuItem>Submenu item</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </PopoverContent>
</Popover>
```

### Dentro de um Sheet ou Dialog

```tsx
<Sheet>
  <SheetTrigger>Abrir painel</SheetTrigger>
  <SheetContent>
    <Select>
      <SelectTrigger>Selecione</SelectTrigger>
      <SelectContent className={overlayStyles('inside-sheet')}>
        <SelectItem value="option">Opção</SelectItem>
      </SelectContent>
    </Select>
  </SheetContent>
</Sheet>
```

## Solução Alternativa: Classes Explícitas

Se preferir não usar a função utilitária, você pode aplicar estas classes diretamente:

```tsx
<PopoverContent className="bg-background text-foreground z-50">
  Conteúdo
</PopoverContent>

<DropdownMenuContent className="bg-background text-foreground z-[100]">
  Conteúdo
</DropdownMenuContent>
```

## Checklist de Verificação

Ao implementar componentes de sobreposição:

- [ ] Aplicar a função `overlayStyles()` ou as classes manuais necessárias
- [ ] Verificar o comportamento quando o componente está dentro de outro overlay
- [ ] Testar em diferentes contextos (page, modal, sheet, etc.)
- [ ] Testar diferentes temas (claro/escuro) se aplicável

Seguindo estas diretrizes, os componentes de sobreposição funcionarão consistentemente em todo o aplicativo. 