import React, { useCallback } from 'react';
import { useDropzone, Accept, FileRejection } from 'react-dropzone';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface DropzoneProps {
  onDrop: (files: File[]) => void;
  accept?: Accept;
  multiple?: boolean;
  className?: string;
  children?: React.ReactNode;
  disabled?: boolean;
}

export function Dropzone({
  onDrop,
  accept,
  multiple = true,
  className,
  children,
  disabled = false
}: DropzoneProps) {

  const handleDrop = useCallback((acceptedFiles: File[], fileRejections: FileRejection[]) => {
    // Processar arquivos rejeitados primeiro
    if (fileRejections.length > 0) {
      fileRejections.forEach(rejection => {
        const { file, errors } = rejection;
        const errorMessages = errors.map(e => e.message).join(', ');
        toast.error(`Erro no arquivo ${file.name}: ${errorMessages}`);
      });
    }

    // Processar arquivos aceitos
    if (acceptedFiles.length > 0) {
      onDrop(acceptedFiles);
    }
  }, [onDrop]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: handleDrop,
    accept,
    multiple,
    disabled,
    noClick: disabled,
    noDrag: disabled,
    noKeyboard: disabled,
    // Aumentar o tamanho máximo para 10MB
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  return (
    <div
      {...getRootProps()}
      className={cn(
        'flex flex-col items-center justify-center border-2 border-dashed rounded-md p-6 transition-colors',
        isDragActive ? 'border-primary bg-accent/30' : 'border-muted bg-muted/50',
        disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer',
        className
      )}
    >
      <input {...getInputProps()} />
      {children ? (
        children
      ) : (
        <>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-muted-foreground mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M7 10l5-5m0 0l5 5m-5-5v12" /></svg>
          <p className="text-sm text-muted-foreground">Clique ou arraste o arquivo aqui</p>
          {!disabled && <p className="text-xs text-muted-foreground mt-1">Tamanho máximo: 10MB</p>}
        </>
      )}
    </div>
  );
}