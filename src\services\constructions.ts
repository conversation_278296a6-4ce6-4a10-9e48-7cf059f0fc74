import { supabase } from '@/lib/supabase/client';
import { Construction } from '@/types';

export const constructionsService = {
  async list() {
    const { data, error } = await supabase
      .from('constructions')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;

    return data.map((construction) => ({
      ...construction,
      startDate: new Date(construction.start_date),
      expectedEndDate: construction.expected_end_date ? new Date(construction.expected_end_date) : undefined,
      createdAt: new Date(construction.created_at),
      updatedAt: new Date(construction.updated_at),
    })) as Construction[];
  },

  async getById(id: string) {
    const { data, error } = await supabase
      .from('constructions')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;

    return {
      ...data,
      startDate: new Date(data.start_date),
      expectedEndDate: data.expected_end_date ? new Date(data.expected_end_date) : undefined,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    } as Construction;
  },

  async create(construction: Omit<Construction, 'id' | 'createdAt' | 'updatedAt'>) {
    const { data, error } = await supabase.from('constructions').insert({
      name: construction.name,
      description: construction.description,
      client_id: construction.clientId,
      budget: construction.budget,
      start_date: construction.startDate.toISOString(),
      expected_end_date: construction.expectedEndDate?.toISOString(),
      status: construction.status,
    }).select().single();

    if (error) throw error;

    return {
      ...data,
      startDate: new Date(data.start_date),
      expectedEndDate: data.expected_end_date ? new Date(data.expected_end_date) : undefined,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    } as Construction;
  },

  async update(id: string, construction: Partial<Omit<Construction, 'id' | 'createdAt' | 'updatedAt'>>) {
    const { data, error } = await supabase
      .from('constructions')
      .update({
        name: construction.name,
        description: construction.description,
        client_id: construction.clientId,
        budget: construction.budget,
        start_date: construction.startDate?.toISOString(),
        expected_end_date: construction.expectedEndDate?.toISOString(),
        status: construction.status,
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    return {
      ...data,
      startDate: new Date(data.start_date),
      expectedEndDate: data.expected_end_date ? new Date(data.expected_end_date) : undefined,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    } as Construction;
  },

  async delete(id: string) {
    const { error } = await supabase.from('constructions').delete().eq('id', id);
    if (error) throw error;
  },
};