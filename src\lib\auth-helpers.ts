import { NextResponse } from 'next/server';
import { SupabaseClient } from '@supabase/supabase-js';

/**
 * Verifica a autenticação do usuário de forma robusta
 * @param supabase Cliente Supabase inicializado
 * @returns Um objeto com o ID do usuário se autenticado, ou uma resposta de erro se não autenticado
 */
export async function verificarAutenticacao(supabase: SupabaseClient) {
  try {
    // Primeiro, tenta obter a sessão
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      
      return {
        autenticado: false,
        userId: null,
        resposta: NextResponse.json(
          { error: 'Erro de autenticação: ' + sessionError.message },
          { status: 401 }
        )
      };
    }
    
    // Verifica se a sessão existe
    if (!sessionData.session) {

      // Tenta obter o usuário diretamente
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError) {
        
        return {
          autenticado: false,
          userId: null,
          resposta: NextResponse.json(
            { error: 'Erro de autenticação: ' + userError.message },
            { status: 401 }
          )
        };
      }
      
      if (!userData.user) {
  
        return {
          autenticado: false,
          userId: null,
          resposta: NextResponse.json(
            { error: 'Não autorizado - Usuário não autenticado' },
            { status: 401 }
          )
        };
      }

      return {
        autenticado: true,
        userId: userData.user.id,
        email: userData.user.email,
        resposta: null
      };
    } else {

      return {
        autenticado: true,
        userId: sessionData.session.user.id,
        email: sessionData.session.user.email,
        resposta: null
      };
    }
  } catch (authError) {
    
    return {
      autenticado: false,
      userId: null,
      resposta: NextResponse.json(
        { error: 'Erro de autenticação inesperado' },
        { status: 401 }
      )
    };
  }
}