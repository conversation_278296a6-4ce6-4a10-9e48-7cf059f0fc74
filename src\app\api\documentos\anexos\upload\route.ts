import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';
import { cookies } from 'next/headers';
import { createServiceClient } from '@/lib/supabase/service-client';
import { verificarAutenticacao } from '@/lib/auth-helpers';

// Tamanho máximo de arquivo: 10MB
const MAX_FILE_SIZE = 10 * 1024 * 1024;

// Tipos de arquivo permitidos
const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'image/png',
  'image/jpeg',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // Excel
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // Word
  'application/vnd.ms-excel', // Excel antigo
  'application/msword', // Word antigo
  'text/csv' // CSV
];

// POST /api/documentos/anexos/upload - Upload de anexo para documento
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    // Inicializar o cliente do Supabase
    const supabase = await getSupabaseRouteClient();

    // Verificar a autenticação
    const auth = await verificarAutenticacao(supabase);
    if (!auth.autenticado) {
      return auth.resposta || NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Obter o FormData da requisição
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const documentoId = formData.get('documentoId') as string;

    // Validações básicas
    if (!file) {
      return NextResponse.json({ error: 'Arquivo não fornecido' }, { status: 400 });
    }

    if (!documentoId) {
      return NextResponse.json({ error: 'ID do documento não fornecido' }, { status: 400 });
    }

    // Extrair informações do arquivo
    const fileName = formData.get('fileName') as string || file.name;
    const fileType = formData.get('fileType') as string || file.type;
    const fileSize = file.size;

    // Validar tamanho do arquivo
    if (fileSize > MAX_FILE_SIZE) {
      return NextResponse.json({
        error: `Arquivo muito grande. O tamanho máximo permitido é ${MAX_FILE_SIZE / (1024 * 1024)}MB`
      }, { status: 400 });
    }

    // Validar tipo de arquivo
    if (!ALLOWED_FILE_TYPES.includes(fileType)) {
      return NextResponse.json({
        error: 'Tipo de arquivo não suportado. Apenas PDF, imagens, Excel, Word e CSV são aceitos.'
      }, { status: 400 });
    }

    // Criar cliente de serviço para contornar RLS
    const serviceClient = createServiceClient();
    if (!serviceClient) {
      return NextResponse.json(
        { error: 'Erro ao criar cliente de serviço' },
        { status: 500 }
      );
    }

    // Gerar nome único para o arquivo
    const fileExt = fileName.split('.').pop()?.toLowerCase() || 'pdf';
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 8);
    const uniqueFileName = `documento_anexos/${timestamp}-${randomString}.${fileExt}`;

    // Upload do arquivo para o Storage usando o cliente de serviço
    const { data: uploadData, error: uploadError } = await serviceClient.storage
      .from('anexos')
      .upload(uniqueFileName, file, {
        cacheControl: '3600',
        upsert: false,
        contentType: fileType
      });

    if (uploadError) {
      // Mensagens de erro mais amigáveis
      let errorMessage = 'Erro ao fazer upload do arquivo';

      if (uploadError.message.includes('duplicate')) {
        errorMessage = 'Já existe um arquivo com este nome. Tente novamente.';
      } else if (uploadError.message.includes('permission')) {
        errorMessage = 'Sem permissão para fazer upload. Tente fazer login novamente.';
      } else if (uploadError.message.includes('storage')) {
        errorMessage = 'Problema no serviço de armazenamento. Tente novamente mais tarde.';
      } else {
        errorMessage = `Erro ao fazer upload: ${uploadError.message}`;
      }

      return NextResponse.json({ error: errorMessage }, { status: 500 });
    }

    // Obter a URL pública do arquivo usando o cliente de serviço
    const { data: publicUrlData } = serviceClient.storage
      .from('anexos')
      .getPublicUrl(uniqueFileName);

    const fileUrl = publicUrlData.publicUrl;

    // Inserir registro no banco de dados usando o cliente de serviço
    const { data: anexoData, error: insertError } = await serviceClient
      .from('documento_anexos')
      .insert({
        documento_id: documentoId,
        nome: fileName,
        arquivo_url: fileUrl,
        arquivo_path: uniqueFileName,
        tipo_arquivo: fileType,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (insertError) {
      // Se falhar ao inserir no banco, tentar remover o arquivo do storage
      try {
        await serviceClient.storage.from('anexos').remove([uniqueFileName]);
      } catch (cleanupError) {
        // Silently handle error
        
      }

      return NextResponse.json(
        { error: 'Erro ao criar registro do anexo: ' + insertError.message },
        { status: 500 }
      );
    }

    return NextResponse.json(anexoData, { status: 201 });
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Erro interno do servidor: ' + error.message },
      { status: 500 }
    );
  }
}