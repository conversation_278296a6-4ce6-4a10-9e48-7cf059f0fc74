import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';

export const dynamic = 'force-dynamic';

export async function GET() {
  const supabase = await getSupabaseRouteClient();

  const { data, error } = await supabase
    .from('lancamentos')
    .select(`
      *,
      contato:contatos(nome_empresa),
      obra:obras(nome),
      parcelas(*)
    `)
    .order('data_competencia', { ascending: false });

  if (error) {
    console.error('Erro ao buscar lançamentos:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json(data);
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Extrair parcelas e dados do lançamento
    const { parcelas = [], ...dadosLancamento } = body;
    
    // Validações básicas
    if (!dadosLancamento.descricao || !dadosLancamento.valor_total) {
      return NextResponse.json(
        { error: 'Descrição e valor total são obrigatórios' },
        { status: 400 }
      );
    }

    // Garantir que o valor seja positivo
    if (dadosLancamento.valor_total <= 0) {
      return NextResponse.json(
        { error: 'Valor total deve ser maior que zero' },
        { status: 400 }
      );
    }

    const supabase = await getSupabaseRouteClient();

    // Criar o lançamento
    const { data: lancamento, error: lancamentoError } = await supabase
      .from('lancamentos')
      .insert(dadosLancamento)
      .select()
      .single();

    if (lancamentoError) {
      console.error('Erro ao criar lançamento:', lancamentoError);
      return NextResponse.json({ error: lancamentoError.message }, { status: 500 });
    }

    // Se há parcelas, criá-las
    if (parcelas.length > 0) {
      const parcelasComLancamentoId = parcelas.map((parcela: any) => ({
        ...parcela,
        lancamento_id: lancamento.id
      }));

      const { error: parcelasError } = await supabase
        .from('parcelas')
        .insert(parcelasComLancamentoId);

      if (parcelasError) {
        console.error('Erro ao criar parcelas:', parcelasError);
        // Se as parcelas falharam, excluir o lançamento
        await supabase.from('lancamentos').delete().eq('id', lancamento.id);
        return NextResponse.json({ error: parcelasError.message }, { status: 500 });
      }
    }

    return NextResponse.json(lancamento);
  } catch (error: any) {
    console.error('Erro interno:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}