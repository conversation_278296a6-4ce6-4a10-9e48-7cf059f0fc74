'use client';

import React, { useState, useEffect, useRef } from 'react';
import { X, FileText, <PERSON>rk<PERSON>, CheckCircle2, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export type ProcessingStep = {
  message: string;
  commercialMessage?: string;
  progress?: number;
  icon?: React.ReactNode;
};

export const DOCUMENT_PROCESSING_STEPS: ProcessingStep[] = [
  {
    message: "Iniciando análise do documento...",
    commercialMessage: "Preparando seu documento para análise inteligente",
    progress: 5,
    icon: <Zap className="h-5 w-5 text-blue-600" />
  },
  {
    message: "Extraindo texto via OCR...",
    commercialMessage: "Convertendo seu documento em dados estruturados",
    progress: 15,
    icon: <FileText className="h-5 w-5 text-blue-600" />
  },
  {
    message: "Identificando fornecedor...",
    commercialMessage: "Reconhecendo automaticamente seus parceiros comerciais",
    progress: 30,
    icon: <svg className="h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
    </svg>
  },
  {
    message: "Extraindo valores...",
    commercialMessage: "Capturando valores e dados financeiros com precisão",
    progress: 45,
    icon: <svg className="h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  },
  {
    message: "Extraindo datas de vencimento...",
    commercialMessage: "Organizando seu fluxo de caixa com datas inteligentes",
    progress: 60,
    icon: <svg className="h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
  },
  {
    message: "Classificando tipo de documento...",
    commercialMessage: "Categorizando automaticamente para sua gestão financeira",
    progress: 75,
    icon: <svg className="h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
    </svg>
  },
  {
    message: "Verificando duplicidade...",
    commercialMessage: "Garantindo a integridade dos seus dados financeiros",
    progress: 85,
    icon: <svg className="h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
    </svg>
  },
  {
    message: "Finalizando análise...",
    commercialMessage: "Concluindo o processamento do seu documento",
    progress: 95,
    icon: <CheckCircle2 className="h-5 w-5 text-blue-600" />
  },
];

// Todas as animações foram movidas para o arquivo globals.css

interface DocumentProcessingNotificationProps {
  fileName: string;
  onClose?: () => void;
  currentStep?: number;
  isVisible?: boolean;
  isComplete?: boolean;
}

export function DocumentProcessingNotification({
  fileName,
  onClose,
  currentStep = 0,
  isVisible = true,
  isComplete = false,
}: DocumentProcessingNotificationProps) {
  const [visible, setVisible] = useState(isVisible);
  const [step, setStep] = useState(currentStep);
  const [fadeOut, setFadeOut] = useState(false);
  const [messageVisible, setMessageVisible] = useState(true);
  const [showConfetti, setShowConfetti] = useState(false);
  const [typingEffect, setTypingEffect] = useState(false);

  // Referência para o elemento principal para animações
  const notificationRef = useRef<HTMLDivElement>(null);

  // Não precisamos mais injetar estilos CSS, pois todas as animações estão em globals.css

  // Handle step changes
  useEffect(() => {
    setStep(currentStep);

    // Ativar efeito de digitação a cada mudança de passo
    setTypingEffect(true);
    const typingTimer = setTimeout(() => {
      setTypingEffect(false);
    }, 1000);

    // Auto-hide when complete
    if (isComplete) {
      // Mostrar confetti quando completo
      setShowConfetti(true);

      const timer = setTimeout(() => {
        setFadeOut(true);

        // After fade out animation, set visible to false
        setTimeout(() => {
          setVisible(false);
          if (onClose) onClose();
        }, 800); // Match the fade-out animation duration
      }, 2500); // Wait longer before starting to fade out for a better experience

      return () => {
        clearTimeout(timer);
        clearTimeout(typingTimer);
      };
    }

    return () => clearTimeout(typingTimer);
  }, [currentStep, isComplete, onClose]);

  // Handle visibility changes
  useEffect(() => {
    if (isVisible) {
      setVisible(true);
      setFadeOut(false);
    }
  }, [isVisible]);

  // Atualizar a mensagem com efeito de fade
  useEffect(() => {
    if (step > 0) {
      setMessageVisible(false);
      const timer = setTimeout(() => {
        setMessageVisible(true);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [step]);

  if (!visible) return null;

  const currentProgress = DOCUMENT_PROCESSING_STEPS[step]?.progress || 0;
  const currentMessage = DOCUMENT_PROCESSING_STEPS[step]?.commercialMessage ||
                         DOCUMENT_PROCESSING_STEPS[step]?.message ||
                         "Processando seu documento...";
  const currentIcon = DOCUMENT_PROCESSING_STEPS[step]?.icon;

  // Função para renderizar efeito de conclusão
  const renderCompletionEffect = () => {
    if (!isComplete) return null;

    return (
      <div className="absolute inset-0 z-20 pointer-events-none overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-blue-50/30 to-transparent animate-fade-in"></div>

        {/* Efeito de brilho pulsante no topo */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-32 bg-blue-400/20 rounded-full filter blur-xl animate-pulse"></div>

        {/* Linhas de sucesso animadas */}
        <div className="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-400/40 to-transparent transform -translate-y-8 animate-shimmer"></div>
        <div className="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-green-400/40 to-transparent transform translate-y-8 animate-shimmer delay-500"></div>
      </div>
    );
  };

  return (
    <div
      ref={notificationRef}
      className={cn(
        "fixed bottom-8 right-8 w-[340px] bg-white rounded-lg z-[9999] overflow-hidden",
        "animate-fade-in transition-opacity duration-500",
        fadeOut && "opacity-0",
        "shadow-lg border border-gray-200 document-notification-shadow",
        !isComplete && "processing-border" // Aplicar a borda animada apenas quando estiver processando
      )}
    >

      {/* Efeito de conclusão */}
      {renderCompletionEffect()}

      {/* Header mais discreto e profissional */}
      <div className="relative">
        <div className="flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200 relative z-10">
          <div className="flex items-center gap-2">
            {isComplete ? (
              <CheckCircle2 className="h-4 w-4 text-green-600" />
            ) : (
              <FileText className="h-4 w-4 text-blue-600" />
            )}
            <span className="text-gray-800 font-medium text-sm">
              {isComplete ? "Documento processado" : "Processando documento"}
            </span>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-sm p-0 transition-colors"
            onClick={() => {
              setFadeOut(true);
              setTimeout(() => {
                setVisible(false);
                if (onClose) onClose();
              }, 500);
            }}
          >
            <X className="h-3.5 w-3.5" />
            <span className="sr-only">Fechar</span>
          </Button>
        </div>
      </div>

      {/* Content com design mais clean e profissional */}
      <div className="p-4 relative z-10 bg-white">
        <div className="mb-3">
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium truncate max-w-[80%]" title={fileName}>
              {fileName}
            </p>
            <div className="text-gray-500 text-xs">
              {isComplete ? (
                <span className="text-green-600 font-medium">Concluído</span>
              ) : (
                <span>{currentProgress}% concluído</span>
              )}
            </div>
          </div>
        </div>

        {/* Progress bar mais clean */}
        <div className="w-full bg-gray-100 rounded-full h-1.5 mb-4 relative overflow-hidden">
          <div
            className="bg-blue-500 h-1.5 rounded-full absolute top-0 left-0 progress-width"
            style={{ "--progress-width": `${currentProgress}%` } as React.CSSProperties}
          />
        </div>

        {/* Current step message mais clean e mais compacto */}
        <div className="flex items-start gap-3 min-h-[60px] py-2">
          <div className="h-4 w-4 flex-shrink-0 mt-0.5">
            {currentIcon ? (
              <div className="text-blue-600 scale-75">{currentIcon}</div>
            ) : (
              <svg className="animate-spin text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
          </div>
          <div className="overflow-hidden flex-1">
            <span
              className={cn(
                "block transition-opacity duration-300 ease-in-out text-gray-700 text-xs leading-relaxed",
                messageVisible ? "opacity-100" : "opacity-0"
              )}
            >
              {currentMessage}
            </span>
          </div>
        </div>

        {/* Mensagem de conclusão */}
        {isComplete && (
          <div className="mt-3 pt-3 border-t border-gray-100">
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0">
                <div className="h-6 w-6 bg-green-50 rounded-full flex items-center justify-center">
                  <CheckCircle2 className="h-3.5 w-3.5 text-green-600" />
                </div>
              </div>
              <div>
                <p className="text-xs font-medium text-gray-900">
                  Processamento concluído
                </p>
                <p className="text-xs text-gray-600 mt-0.5">
                  Documento pronto para revisão
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}