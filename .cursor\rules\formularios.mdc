---
description: 
globs: 
alwaysApply: false
---
# Convenções para Formulários

## Estrutura de Formulários

Os formulários no projeto seguem estas convenções:

- Utilizam o `react-hook-form` com validação via `zod`
- Os componentes `FormField` agrupam label, input e mensagens de erro
- A maioria dos formulários tem botões de Cancelar e Salvar/Criar no final
- Formulários dentro de drawers usam o componente `Form` de shadcn/ui
- Formulários em diálogos podem usar abordagens com estado local

## Dropdowns (Select)

Para dropdowns, sempre aplicar:

```tsx
<SelectContent className="bg-background text-foreground z-50">
  <SelectItem value="valor">Opção</SelectItem>
</SelectContent>
```

As classes são essenciais para que os dropdowns renderizem corretamente quando usados dentro de componentes overlay como Sheet, Dialog e Drawer.

## Campos Dependentes

Quando um campo depende de outro (ex: CPF/CNPJ depende do tipo de pessoa):

1. O campo dependente deve ser desativado até que o campo principal seja preenchido
2. Limpar o valor do campo dependente quando o campo principal mudar
3. Atualizar o placeholder e label do campo dependente para refletir o estado atual

Exemplo:
```tsx
// No formulário com react-hook-form
<FormField
  control={form.control}
  name="tipo_pessoa"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Tipo de Pessoa</FormLabel>
      <Select 
        value={field.value || ''} 
        onValueChange={(value) => {
          field.onChange(value);
          form.setValue('cpf_cnpj', ''); // Limpar campo dependente
        }}
      >
        <FormControl>
          <SelectTrigger>
            <SelectValue placeholder="Selecione o tipo de pessoa" />
          </SelectTrigger>
        </FormControl>
        <SelectContent className="bg-background text-foreground z-50">
          <SelectItem value="fisica">Pessoa Física</SelectItem>
          <SelectItem value="juridica">Pessoa Jurídica</SelectItem>
        </SelectContent>
      </Select>
      <FormMessage />
    </FormItem>
  )}
/>
```

