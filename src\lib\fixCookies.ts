/**
 * Utilitários para manipulação de cookies problemáticos do Supabase
 */

/**
 * Remove todos os cookies relacionados ao Supabase que possam estar corrompidos
 */
export function clearSupabaseCookies(): boolean {
  try {
    // Lista de prefixos de cookies do Supabase que podem causar problemas
    const supabaseCookiePrefixes = ['sb-', 'supabase-', 'sb.', 'supabase.'];

    document.cookie.split(';').forEach(cookie => {
      const [name] = cookie.trim().split('=');

      // Verifica se o cookie é do Supabase baseado no prefixo
      const isSupabaseCookie = supabaseCookiePrefixes.some(prefix =>
        name.toLowerCase().trim().startsWith(prefix.toLowerCase())
      );

      if (isSupabaseCookie) {
        // Remove o cookie definindo uma data no passado
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
      }
    });

    // Verifica cookies de autenticação específicos
    const authCookies = ['access-token', 'refresh-token', 'auth-token'];
    authCookies.forEach(cookieName => {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
    });

    // Limpar localStorage também
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('supabase') || key.includes('sb-'))) {
          localStorage.removeItem(key);
        }
      }
    } catch (e) {
      // Ignorar erros de localStorage
    }

    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Recarrega a página após limpar os cookies para forçar uma nova sessão
 */
export function resetSession(): void {
  clearSupabaseCookies();
  // Pequena pausa para garantir que os cookies foram removidos
  setTimeout(() => {
    window.location.href = '/login';
  }, 500);
}

/**
 * Verifica se há cookies malformados do Supabase
 * Retorna true se encontrar algum problema
 */
export function checkForBrokenCookies(): boolean {
  try {
    const hasBrokenCookie = document.cookie.split(';').some(cookie => {
      const [name, value] = cookie.trim().split('=');

      // Verifica cookies do Supabase
      if (name.includes('sb-') || name.includes('supabase')) {
        // Tenta fazer parse do JSON se o valor começar com algo que pareça JSON
        if (value && (value.startsWith('{') || value.startsWith('['))) {
          try {
            JSON.parse(value);
            return false; // Cookie parece válido
          } catch (e) {
            return true; // Cookie está quebrado
          }
        }
      }
      return false;
    });

    return hasBrokenCookie;
  } catch (error) {
    return true; // Assume que há problemas se não conseguir verificar
  }
}

/**
 * Executa correção emergencial dos cookies, chamando tanto a limpeza quanto o redirecionamento
 */
export function emergencyFix(): void {
  // Limpar todo o sessionStorage
  try {
    sessionStorage.clear();
  } catch (e) {
    // Ignorar erros
  }

  // Limpar todo o localStorage
  try {
    localStorage.clear();
  } catch (e) {
    // Ignorar erros
  }

  // Limpar todos os cookies, não apenas os do Supabase
  try {
    document.cookie.split(';').forEach(cookie => {
      const [name] = cookie.trim().split('=');
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
    });
  } catch (e) {
    // Ignorar erros
  }

  // Redirecionar para login
  setTimeout(() => {
    window.location.href = '/login';
  }, 500);
}