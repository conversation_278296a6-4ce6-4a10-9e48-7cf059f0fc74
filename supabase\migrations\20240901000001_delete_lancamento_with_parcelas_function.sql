-- Função para excluir um lançamento e suas parcelas em uma única transação
CREATE OR REPLACE FUNCTION delete_lancamento_with_parcelas(p_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Atualizar documentos para remover referência ao lançamento
  UPDATE documentos SET lancamento_id = NULL WHERE lancamento_id = p_id;
  
  -- Excluir parcelas primeiro
  DELETE FROM parcelas WHERE lancamento_id = p_id;
  
  -- Excluir o lançamento
  DELETE FROM lancamentos WHERE id = p_id;
  
  -- Verificar se o lançamento foi excluído
  IF EXISTS (SELECT 1 FROM lancamentos WHERE id = p_id) THEN
    RETURN FALSE;
  ELSE
    RETURN TRUE;
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Erro ao excluir lançamento %: %', p_id, SQLERRM;
    RETURN FALSE;
END;
$$;

-- Conceder permissões para usuários autenticados
GRANT EXECUTE ON FUNCTION delete_lancamento_with_parcelas(UUID) TO authenticated;

COMMENT ON FUNCTION delete_lancamento_with_parcelas(UUID) IS 'Função para excluir um lançamento e suas parcelas em uma única transação.';
