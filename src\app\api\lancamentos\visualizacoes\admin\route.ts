import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';
import { cookies } from 'next/headers';

// PATCH /api/lancamentos/visualizacoes/admin?id=<id> - Atualiza uma visualização como administrador
export const dynamic = 'force-dynamic';

export async function PATCH(request: NextRequest) {
  try {
    // Obter ID da visualização da query string
    const url = new URL(request.url);
    const id = url.searchParams.get('id');if (!id) {
      return NextResponse.json({ error: 'ID da visualização não fornecido' }, { status: 400 });
    }

    // Obter o body da requisição
    const body = await request.json();// Criar cliente Supabase
    const supabase = await getSupabaseRouteClient();

    // Validações básicas
    const { nome, descricao, filtros_json } = body;

    if (nome !== undefined && (typeof nome !== 'string' || nome.trim().length === 0)) {
      return NextResponse.json({ error: 'Nome é obrigatório' }, { status: 400 });
    }

    if (filtros_json !== undefined && typeof filtros_json !== 'object') {
      return NextResponse.json({ error: 'Filtros inválidos' }, { status: 400 });
    }

    let filtrosLimpos;
    if (filtros_json) {
      filtrosLimpos = Object.fromEntries(
        Object.entries(filtros_json).filter(([_, valor]) =>
          valor !== undefined && valor !== null && valor !== ''
        )
      );
    }

    // Verificar se a visualização existe
    const { data: visualizacao, error: checkError } = await supabase
      .from('visualizacoes')
      .select('id, nome')
      .eq('id', id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        
        return NextResponse.json({ error: 'Visualização não encontrada' }, { status: 404 });
      }
      
      return NextResponse.json({ error: 'Erro ao verificar visualização: ' + checkError.message }, { status: 500 });
    }

    // Usar a função RPC de administrador para atualizar a visualização
    const { data, error } = await supabase.rpc(
      'admin_update_visualizacao',
      {
        p_id: id,
        p_nome: nome,
        p_descricao: descricao,
        p_filtros_json: filtrosLimpos
      }
    );

    if (error) {
      
      return NextResponse.json({ error: 'Erro ao atualizar visualização: ' + error.message }, { status: 500 });
    }return NextResponse.json(data || { id: id, nome: nome, descricao: descricao });
  } catch (error: any) {
    
    return NextResponse.json({ error: 'Erro interno do servidor: ' + (error?.message || 'desconhecido') }, { status: 500 });
  }
}

// DELETE /api/lancamentos/visualizacoes/admin?id=<id> - Remove uma visualização como administrador
export async function DELETE(request: NextRequest) {
  try {
    // Obter ID da visualização da query string
    const url = new URL(request.url);
    const id = url.searchParams.get('id');if (!id) {
      return NextResponse.json({ error: 'ID da visualização não fornecido' }, { status: 400 });
    }

    // Criar cliente Supabase
    const supabase = await getSupabaseRouteClient();

    // Verificar se a visualização existe
    const { data: visualizacao, error: checkError } = await supabase
      .from('visualizacoes')
      .select('id, nome')
      .eq('id', id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Visualização não encontrada' }, { status: 404 });
      }
      return NextResponse.json({ error: 'Erro ao verificar visualização: ' + checkError.message }, { status: 500 });
    }

    // Excluir a visualização usando a função RPC de administrador
    const { data: deleted, error } = await supabase.rpc(
      'admin_delete_visualizacao',
      { p_id: id }
    );

    if (error) {
      return NextResponse.json({ error: 'Erro ao excluir visualização: ' + error.message }, { status: 500 });
    }

    return NextResponse.json({ message: 'Visualização excluída com sucesso', id: id });
  } catch (error: any) {
    return NextResponse.json({ error: 'Erro interno do servidor: ' + (error?.message || 'desconhecido') }, { status: 500 });
  }
}