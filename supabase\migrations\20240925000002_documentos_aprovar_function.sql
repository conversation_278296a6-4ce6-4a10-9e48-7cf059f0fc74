-- Função para aprovar um documento e criar um lançamento
CREATE OR REPLACE FUNCTION aprovar_documento_e_criar_lancamento(
  p_documento_id UUID,
  p_obra_id UUID,
  p_contato_id UUID,
  p_descricao TEXT,
  p_forma_pagamento TEXT,
  p_categoria_id UUID DEFAULT NULL,
  p_observacoes TEXT DEFAULT NULL,
  p_user_id UUID DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_documento documentos;
  v_lancamento_id UUID;
  v_parcela_id UUID;
  v_parcelas JSONB;
  v_parcela JSONB;
  v_resultado JSONB;
  v_valor_total NUMERIC;
  v_data_vencimento DATE;
  v_fornecedor TEXT;
  v_parcelas_array JSONB;
  v_parcela_item JSONB;
  v_i INTEGER;
BEGIN
  -- Buscar o documento
  SELECT * INTO v_documento
  FROM documentos
  WHERE id = p_documento_id;
  
  IF v_documento IS NULL THEN
    RAISE EXCEPTION 'Documento não encontrado';
  END IF;
  
  IF v_documento.status = 'aprovado' THEN
    RAISE EXCEPTION 'Documento já foi aprovado';
  END IF;
  
  -- Extrair valores do documento
  v_valor_total := COALESCE(v_documento.valor_total, 0);
  v_data_vencimento := v_documento.data_vencimento;
  v_fornecedor := v_documento.fornecedor;
  v_parcelas_array := COALESCE(v_documento.dados_extraidos->'parcelas', '[]'::jsonb);
  
  -- Criar o lançamento
  INSERT INTO lancamentos (
    obra_id,
    tipo_lancamento,
    valor_total,
    data_competencia,
    forma_pagamento,
    descricao,
    contato_id,
    observacoes,
    status,
    categoria_id,
    user_id,
    data
  ) VALUES (
    p_obra_id,
    'despesa',
    v_valor_total,
    CURRENT_DATE,
    p_forma_pagamento,
    p_descricao,
    p_contato_id,
    p_observacoes,
    'Em aberto',
    p_categoria_id,
    COALESCE(p_user_id, v_documento.user_id),
    CURRENT_DATE
  )
  RETURNING id INTO v_lancamento_id;
  
  -- Verificar se existem parcelas nos dados extraídos
  IF jsonb_array_length(v_parcelas_array) > 0 THEN
    -- Criar parcelas a partir dos dados extraídos
    FOR v_i IN 0..jsonb_array_length(v_parcelas_array)-1 LOOP
      v_parcela_item := v_parcelas_array->v_i;
      
      INSERT INTO parcelas (
        lancamento_id,
        numero,
        valor,
        vencimento,
        status
      ) VALUES (
        v_lancamento_id,
        (v_parcela_item->>'numero')::INTEGER,
        (v_parcela_item->>'valor')::NUMERIC,
        (v_parcela_item->>'vencimento')::DATE,
        'pendente'
      )
      RETURNING id INTO v_parcela_id;
    END LOOP;
  ELSE
    -- Criar uma única parcela se não houver parcelas nos dados extraídos
    INSERT INTO parcelas (
      lancamento_id,
      numero,
      valor,
      vencimento,
      status
    ) VALUES (
      v_lancamento_id,
      1,
      v_valor_total,
      COALESCE(v_data_vencimento, CURRENT_DATE + INTERVAL '30 days'),
      'pendente'
    )
    RETURNING id INTO v_parcela_id;
  END IF;
  
  -- Atualizar o documento para aprovado e associar ao lançamento
  UPDATE documentos
  SET 
    status = 'aprovado',
    lancamento_id = v_lancamento_id,
    updated_at = CURRENT_TIMESTAMP
  WHERE id = p_documento_id;
  
  -- Construir o resultado
  v_resultado := jsonb_build_object(
    'documento_id', p_documento_id,
    'lancamento_id', v_lancamento_id,
    'status', 'aprovado',
    'mensagem', 'Documento aprovado e lançamento criado com sucesso'
  );
  
  RETURN v_resultado;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Erro ao aprovar documento: %', SQLERRM;
END;
$$;

-- Comentário para a função
COMMENT ON FUNCTION aprovar_documento_e_criar_lancamento IS 'Aprova um documento e cria um lançamento com parcelas baseado nos dados extraídos';

-- Função para rejeitar um documento
CREATE OR REPLACE FUNCTION rejeitar_documento(
  p_documento_id UUID,
  p_motivo_rejeicao TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_documento documentos;
  v_resultado JSONB;
BEGIN
  -- Buscar o documento
  SELECT * INTO v_documento
  FROM documentos
  WHERE id = p_documento_id;
  
  IF v_documento IS NULL THEN
    RAISE EXCEPTION 'Documento não encontrado';
  END IF;
  
  IF v_documento.status = 'rejeitado' THEN
    RAISE EXCEPTION 'Documento já foi rejeitado';
  END IF;
  
  -- Atualizar o documento para rejeitado
  UPDATE documentos
  SET 
    status = 'rejeitado',
    motivo_rejeicao = p_motivo_rejeicao,
    updated_at = CURRENT_TIMESTAMP
  WHERE id = p_documento_id;
  
  -- Construir o resultado
  v_resultado := jsonb_build_object(
    'documento_id', p_documento_id,
    'status', 'rejeitado',
    'motivo_rejeicao', p_motivo_rejeicao,
    'mensagem', 'Documento rejeitado com sucesso'
  );
  
  RETURN v_resultado;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Erro ao rejeitar documento: %', SQLERRM;
END;
$$;

-- Comentário para a função
COMMENT ON FUNCTION rejeitar_documento IS 'Rejeita um documento e registra o motivo da rejeição';
