/**
 * Serviço para gerenciar visualizações
 * Implementação simplificada seguindo o padrão do serviço de lançamentos
 * Com cache e debounce para evitar chamadas excessivas
 */

import { supabase } from '@/lib/supabase/client';
import { createServiceClient } from '@/lib/supabase/service-client';

// Cache para armazenar resultados de visualizações
let visualizacoesCache: {
  data: any[] | null;
  timestamp: number;
  expiresInMs: number;
} = {
  data: null,
  timestamp: 0,
  expiresInMs: 30000 // 30 segundos
};

export interface Visualizacao {
  id: string;
  nome: string;
  descricao?: string;
  filtros_json: any;
  created_at: string;
  updated_at: string;
  user_id: string;
  contexto: string;
  is_default?: boolean;
  _temFiltrosValidos?: boolean;
}

// Verificar se os filtros são válidos
// Agora consideramos visualizações sem filtros como válidas também
const verificarFiltrosValidos = (visualizacao: Visualizacao): Visualizacao => {
  const filtrosValidos = visualizacao.filtros_json &&
                        typeof visualizacao.filtros_json === 'object';
  // Não exigimos mais que tenha filtros: Object.keys(visualizacao.filtros_json).length > 0

  return {
    ...visualizacao,
    _temFiltrosValidos: filtrosValidos
  };
};

/**
 * Buscar todas as visualizações
 * Implementação com cache para evitar chamadas excessivas
 */
export const buscarTodasVisualizacoes = async (): Promise<Visualizacao[]> => {
  try {
    // Verificar se temos dados em cache válidos
    const agora = Date.now();
    if (
      visualizacoesCache.data &&
      visualizacoesCache.timestamp > 0 &&
      (agora - visualizacoesCache.timestamp) < visualizacoesCache.expiresInMs
    ) {
      return visualizacoesCache.data.map(verificarFiltrosValidos);
    }

    // Obter o ID do usuário atual
    const { data: { session } } = await supabase.auth.getSession();
    const userId = session?.user?.id;

    if (!userId) {
      return [];
    }

    // Criar cliente de serviço para contornar problemas de CORS e RLS
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      throw new Error("Não foi possível criar o cliente de serviço");
    }

    // Usar a função RPC para buscar visualizações
    // Adicionar timestamp para evitar cache
    const cacheBust = Date.now();

    // Usar a função RPC para buscar visualizações com o formato correto dos parâmetros
    // Convertendo userId para string para garantir compatibilidade com a função atualizada
    const { data: visualizacoesRPC, error: rpcError } = await serviceClient.rpc(
      'buscar_visualizacoes_com_info',
      {
        p_user_id: String(userId),
        p_contexto: 'lancamentos',
        p_cache_bust: cacheBust
      }
    );

    if (rpcError) {

      // Fallback: buscar diretamente da tabela
      const { data, error } = await serviceClient
        .from('visualizacoes')
        .select('*')
        .eq('contexto', 'lancamentos')
        .eq('user_id', userId)
        .order('nome', { ascending: true });

      if (error) {
        throw new Error(`Erro ao buscar visualizações: ${error.message}`);
      }

      // Atualizar o cache
      if (Array.isArray(data)) {
        visualizacoesCache = {
          data: data,
          timestamp: Date.now(),
          expiresInMs: visualizacoesCache.expiresInMs
        };
      }

      // Verificar filtros válidos para cada visualização
      return Array.isArray(data) ? data.map(verificarFiltrosValidos) : [];
    }

    // Se chegou aqui, a RPC funcionou
    // Atualizar o cache
    if (Array.isArray(visualizacoesRPC)) {
      visualizacoesCache = {
        data: visualizacoesRPC,
        timestamp: Date.now(),
        expiresInMs: visualizacoesCache.expiresInMs
      };
    }

    // Verificar filtros válidos para cada visualização
    return Array.isArray(visualizacoesRPC) ? visualizacoesRPC.map(verificarFiltrosValidos) : [];
  } catch (error) {
    throw error;
  }
};

/**
 * Buscar visualização por ID
 * Implementação simplificada usando diretamente o cliente Supabase
 */
export const buscarVisualizacaoPorId = async (id: string): Promise<Visualizacao | null> => {
  try {
    // Criar cliente de serviço para contornar problemas de CORS e RLS
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      throw new Error("Não foi possível criar o cliente de serviço");
    }

    // Buscar visualização diretamente da tabela
    const { data, error } = await serviceClient
      .from('visualizacoes')
      .select('*')
      .eq('id', id);

    if (error) {
      throw new Error(`Erro ao buscar visualização: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return null;
    }

    return verificarFiltrosValidos(data[0]);
  } catch (error) {
    throw error;
  }
};

/**
 * Atualizar visualização
 * Implementação simplificada usando diretamente o cliente Supabase
 */
export const atualizarVisualizacao = async (
  id: string,
  nome: string,
  descricao?: string,
  filtros_json?: any
): Promise<Visualizacao | null> => {
  try {
    // Validações básicas
    if (!id) {
      throw new Error('ID da visualização não fornecido');
    }

    if (!nome || typeof nome !== 'string' || nome.trim().length === 0) {
      throw new Error('Nome é obrigatório');
    }

    // Criar cliente de serviço para contornar problemas de CORS e RLS
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      throw new Error("Não foi possível criar o cliente de serviço");
    }

    // Atualizar visualização usando a tabela diretamente
    const { data, error } = await serviceClient
      .from('visualizacoes')
      .update({
        nome,
        descricao: descricao || null,
        filtros_json: filtros_json || {},
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Erro ao atualizar visualização: ${error.message}`);
    }

    // Invalidar o cache após atualização
    invalidarCacheVisualizacoes();

    return verificarFiltrosValidos(data);
  } catch (error) {
    throw error;
  }
};

/**
 * Excluir visualização
 * Implementação simplificada usando diretamente o cliente Supabase
 */
export const excluirVisualizacao = async (id: string): Promise<boolean> => {
  try {
    if (!id) {
      throw new Error('ID da visualização não fornecido');
    }

    // Criar cliente de serviço para contornar problemas de CORS e RLS
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      throw new Error("Não foi possível criar o cliente de serviço");
    }

    // Excluir visualização usando a tabela diretamente
    const { error } = await serviceClient
      .from('visualizacoes')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Erro ao excluir visualização: ${error.message}`);
    }

    // Invalidar o cache após exclusão
    invalidarCacheVisualizacoes();

    return true;
  } catch (error) {
    throw error;
  }
};

/**
 * Criar visualização
 * Implementação simplificada usando diretamente o cliente Supabase
 */
export const criarVisualizacao = async (
  nome: string,
  filtros_json: any,
  user_id: string,
  descricao?: string,
  contexto: string = 'lancamentos'
): Promise<Visualizacao | null> => {
  try {
    // Verificar se temos um ID de usuário válido
    if (!user_id) {
      throw new Error('É necessário estar autenticado para criar uma visualização');
    }

    // Validar dados antes de enviar
    if (!nome || typeof nome !== 'string' || nome.trim().length === 0) {
      throw new Error('Nome é obrigatório');
    }

    if (!filtros_json || typeof filtros_json !== 'object') {
      throw new Error('Filtros inválidos');
    }

    // Criar cliente de serviço para contornar problemas de CORS e RLS
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      throw new Error("Não foi possível criar o cliente de serviço");
    }

    // Verificar se a tabela existe antes de inserir
    const { data: tableCheck, error: tableError } = await serviceClient
      .from('visualizacoes')
      .select('id')
      .limit(1);

    if (tableError) {
      if (tableError.code === '42P01') { // Código para tabela não existe
        throw new Error('A tabela de visualizações não existe. Verifique a configuração do banco de dados.');
      }
    }

    // Inserir visualização usando a tabela diretamente
    const { data, error } = await serviceClient
      .from('visualizacoes')
      .insert({
        user_id: user_id,
        contexto: contexto,
        nome: nome,
        descricao: descricao || null,
        filtros_json: filtros_json
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Erro ao criar visualização: ${error.message}`);
    }

    // Invalidar o cache após criação
    invalidarCacheVisualizacoes();

    return verificarFiltrosValidos(data);
  } catch (error) {
    throw error;
  }
};

/**
 * Função para invalidar o cache de visualizações
 * Deve ser chamada após criar, atualizar ou excluir visualizações
 */
export const invalidarCacheVisualizacoes = (): void => {
  visualizacoesCache = {
    data: null,
    timestamp: 0,
    expiresInMs: visualizacoesCache.expiresInMs
  };
};

/**
 * Função para criar funções SQL necessárias para o funcionamento das visualizações
 * Esta função é um stub que sempre retorna true, pois as funções SQL devem ser criadas
 * via migrations no banco de dados
 */
export const criarFuncoesSql = async (): Promise<boolean> => {
  try {
    // Criar cliente de serviço para contornar problemas de CORS e RLS
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      return false;
    }

    // Verificar se a tabela visualizacoes existe
    const { data, error } = await serviceClient
      .from('visualizacoes')
      .select('id')
      .limit(1);

    if (error) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Definir uma visualização como padrão
 * Implementação robusta com múltiplos fallbacks para garantir funcionamento
 */
export const definirVisualizacaoPadrao = async (
  id: string,
  user_id: string
): Promise<boolean> => {
  try {
    if (!id) {
      throw new Error('ID da visualização não fornecido');
    }

    if (!user_id) {
      throw new Error('ID do usuário não fornecido');
    }

    // Verificar primeiro se a visualização existe
    const visualizacao = await buscarVisualizacaoPorId(id);
    if (!visualizacao) {
      throw new Error('Visualização não encontrada');
    }

    // Criar cliente de serviço para contornar problemas de CORS e RLS
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      throw new Error("Não foi possível criar o cliente de serviço");
    }

    // Abordagem 1: Usar a função RPC
    try {
      const { data, error } = await serviceClient.rpc(
        'set_default_visualization',
        {
          p_visualization_id: id,
          p_user_id: user_id
        }
      );

      if (!error) {
        // Função RPC funcionou
        invalidarCacheVisualizacoes();
        return data === true;
      }

      // Se chegou aqui, houve erro na função RPC// Continuar para o fallback
    } catch (rpcError) {// Continuar para o fallback
    }

    // Abordagem 2: Atualização direta em duas etapas
    try {
      // Primeiro, remover o status de padrão de todas as visualizações do usuário no mesmo contexto
      const { error: resetError } = await serviceClient
        .from('visualizacoes')
        .update({ is_default: false })
        .eq('user_id', user_id)
        .eq('contexto', visualizacao.contexto);

      if (resetError) {// Continuar mesmo com erro
      }

      // Depois, definir a visualização selecionada como padrão
      const { data: updateData, error: updateError } = await serviceClient
        .from('visualizacoes')
        .update({ is_default: true })
        .eq('id', id)
        .select();

      if (updateError) {// Tentar uma última abordagem
      } else {
        // Sucesso na atualização direta
        invalidarCacheVisualizacoes();
        return true;
      }
    } catch (directUpdateError) {// Continuar para a última tentativa
    }

    // Abordagem 3: Usar API REST para contornar problemas de permissão
    try {
      const response = await fetch('/api/visualizacoes/set-default', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id,
          user_id
        }),
      });

      if (response.ok) {
        const result = await response.json();
        invalidarCacheVisualizacoes();
        return result.success === true;
      }
    } catch (apiError) {}

    // Se chegou aqui, todas as abordagens falharam
    throw new Error('Não foi possível definir a visualização como padrão após múltiplas tentativas');
  } catch (error: any) {
    
    throw error;
  }
};

/**
 * Buscar a visualização padrão de um usuário em um contexto
 */
export const buscarVisualizacaoPadrao = async (
  user_id: string,
  contexto: string = 'lancamentos'
): Promise<Visualizacao | null> => {
  try {
    if (!user_id) {
      throw new Error('ID do usuário não fornecido');
    }

    // Criar cliente de serviço para contornar problemas de CORS e RLS
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      throw new Error("Não foi possível criar o cliente de serviço");
    }

    // Usar a função RPC para buscar a visualização padrão
    try {
      const { data, error } = await serviceClient.rpc(
        'get_default_visualization',
        {
          p_user_id: user_id,
          p_contexto: contexto
        }
      );

      if (error) {
        // Se a função não existir, buscar diretamente
        if (error.code === 'PGRST116') {
          // Função não existe, buscar diretamente

          // Buscar diretamente da tabela
          const { data: directData, error: directError } = await serviceClient
            .from('visualizacoes')
            .select('*')
            .eq('user_id', user_id)
            .eq('contexto', contexto)
            .eq('is_default', true)
            .limit(1);

          if (directError) {
            throw new Error(`Erro ao buscar visualização padrão: ${directError.message}`);
          }

          if (!directData || directData.length === 0) {
            return null;
          }

          return verificarFiltrosValidos(directData[0]);
        } else {
          throw new Error(`Erro ao buscar visualização padrão: ${error.message}`);
        }
      }

      if (!data || data.length === 0) {
        return null;
      }

      return verificarFiltrosValidos(data[0]);
    } catch (rpcError) {
      // Fallback: buscar diretamente

      // Buscar diretamente da tabela
      const { data: directData, error: directError } = await serviceClient
        .from('visualizacoes')
        .select('*')
        .eq('user_id', user_id)
        .eq('contexto', contexto)
        .eq('is_default', true)
        .limit(1);

      if (directError) {
        throw new Error(`Erro ao buscar visualização padrão: ${directError.message}`);
      }

      if (!directData || directData.length === 0) {
        return null;
      }

      return verificarFiltrosValidos(directData[0]);
    }

  } catch (error) {
    throw error;
  }
};

/**
 * Hook para usar o serviço de visualizações
 * Similar ao useLancamentosService
 */
export function useVisualizacoesService() {
  return {
    buscarTodasVisualizacoes,
    buscarVisualizacaoPorId,
    atualizarVisualizacao,
    excluirVisualizacao,
    criarVisualizacao,
    definirVisualizacaoPadrao,
    buscarVisualizacaoPadrao,
    invalidarCacheVisualizacoes
  };
}