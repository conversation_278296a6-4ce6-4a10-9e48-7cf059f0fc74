'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { supabase } from '@/lib/supabase/client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Spinner } from '@/components/ui/spinner'
import { AlertCircle, CheckCircle2 } from 'lucide-react'
import { toast } from 'sonner'
import { clearSupabaseCookies, resetSession } from '@/lib/fixCookies'

export function AuthDebugger() {
  const [loading, setLoading] = useState(true)
  const [session, setSession] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [cookies, setCookies] = useState<string[]>([])

  const checkAuth = async () => {
    setLoading(true)
    setError(null)

    try {
      // Verificar sessão
      const { data, error: sessionError } = await supabase.auth.getSession()

      setSession(data.session)

      if (sessionError) {
        setError(sessionError.message)
      }

      // Verificar cookies
      const cookieList: string[] = []
      document.cookie.split(';').forEach(cookie => {
        const [name] = cookie.trim().split('=')
        if (name.includes('sb-') || name.includes('supabase')) {
          cookieList.push(name.trim())
        }
      })
      setCookies(cookieList)

    } catch (e: any) {
      setError(e.message || 'Erro desconhecido')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    checkAuth()
  }, [])

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          Status de Autenticação
          {session ? (
            <Badge variant="outline" className="ml-2 bg-green-100 text-green-800">Autenticado</Badge>
          ) : (
            <Badge variant="destructive" className="ml-2">Não Autenticado</Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {loading ? (
          <div className="flex justify-center py-4">
            <Spinner />
          </div>
        ) : (
          <>
            {error && (
              <div className="bg-red-50 border border-red-200 rounded p-3 text-sm text-red-600 flex items-start gap-2">
                <AlertCircle className="h-5 w-5 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="font-medium">Erro detectado:</p>
                  <p className="mt-1">{error}</p>
                </div>
              </div>
            )}

            <div>
              <h3 className="text-sm font-medium mb-2">Status de Sessão:</h3>
              <div className="bg-muted p-2 rounded-md">
                {session ? (
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                    <span>Sessão ativa (usuário: {session.user?.email || session.user?.id})</span>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">Nenhuma sessão ativa</p>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-2">Cookies Supabase ({cookies.length}):</h3>
              {cookies.length > 0 ? (
                <ul className="text-xs bg-muted p-2 rounded-md space-y-1">
                  {cookies.map(cookie => (
                    <li key={cookie}>{cookie}</li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-muted-foreground">Nenhum cookie Supabase encontrado</p>
              )}
            </div>

            <div className="flex flex-col gap-2 pt-2">
              <Button size="sm" onClick={checkAuth} variant="outline">
                Verificar Novamente
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={() => {
                  clearSupabaseCookies()
                  toast.success('Cookies limpos')
                  setTimeout(checkAuth, 500)
                }}
              >
                Limpar Cookies
              </Button>
              <Button
                size="sm"
                variant="default"
                onClick={() => {
                  resetSession()
                  toast.success('Redirecionando para login...')
                }}
              >
                Reiniciar Sessão (vai para Login)
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}