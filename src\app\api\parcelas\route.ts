import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';

export const dynamic = 'force-dynamic';

// Função para converter qualquer valor para número
const converterParaNumero = (valor: any): number => {
  if (typeof valor === 'number') return valor;
  if (typeof valor === 'string') {
    // Remover caracteres não numéricos exceto ponto e vírgula
    const valorLimpo = valor.replace(/[^\d,.]/g, '').replace(',', '.');
    return parseFloat(valorLimpo) || 0;
  }
  return 0;
};

// POST /api/parcelas - Criar uma nova parcela
export async function POST(request: NextRequest) {
  try {
    // Obter os dados da requisição
    const parcela = await request.json();// Validar campos obrigatórios
    if (!parcela.lancamento_id || parcela.valor === undefined || !parcela.vencimento || !parcela.numero) {return NextResponse.json(
        { error: 'Campos obrigatórios não fornecidos' },
        { status: 400 }
      );
    }
    
    // Inicializar o cliente do Supabase
    const supabase = await getSupabaseRouteClient();
    
    // Verificar se o lançamento existe
    const { data: lancamento, error: lancamentoError } = await supabase
      .from('lancamentos')
      .select('id')
      .eq('id', parcela.lancamento_id)
      .single();
      
    if (lancamentoError || !lancamento) {
      
      return NextResponse.json(
        { error: 'Lançamento não encontrado' },
        { status: 404 }
      );
    }
    
    // Converter o valor para número
    const valorParcela = converterParaNumero(parcela.valor);
    
    if (valorParcela <= 0) {
      return NextResponse.json(
        { error: 'Valor da parcela deve ser maior que zero' },
        { status: 400 }
      );
    }
    
    // Inserir a parcela no banco de dados
    const { data, error } = await supabase
      .from('parcelas')
      .insert({
        lancamento_id: parcela.lancamento_id,
        numero: parcela.numero,
        valor: valorParcela,
        vencimento: parcela.vencimento,
        status: parcela.status || 'pendente',
        data_pagamento: parcela.data_pagamento || null,
      })
      .select()
      .single();
    
    if (error) {
      
      return NextResponse.json(
        { error: `Erro ao criar parcela: ${error.message}` },
        { status: 500 }
      );
    }return NextResponse.json(data);
  } catch (error: any) {
    
    return NextResponse.json(
      { error: `Erro interno do servidor: ${error?.message || 'Desconhecido'}` },
      { status: 500 }
    );
  }
}