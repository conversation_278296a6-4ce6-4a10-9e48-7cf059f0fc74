-- Adicionar coluna contato_id à tabela documentos
ALTER TABLE documentos
ADD COLUMN IF NOT EXISTS contato_id UUID REFERENCES contatos(id);

-- Adicionar índice para melhorar a performance
CREATE INDEX IF NOT EXISTS idx_documentos_contato_id ON documentos(contato_id);

-- Adicionar comentário na coluna
COMMENT ON COLUMN documentos.contato_id IS 'ID do contato associado ao documento';

-- Atualizar o tipo na tabela supabase_schema
DO $$
BEGIN
  -- Verificar se a função update_schema_cache existe
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_schema_cache') THEN
    -- Atualizar o cache do schema
    PERFORM update_schema_cache();
  END IF;
END $$;
