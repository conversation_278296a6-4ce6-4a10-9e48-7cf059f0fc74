import { DocumentoAnexo } from '@/types/documentos';
import { supabase } from '@/lib/supabase/client';

/**
 * Função para fazer upload de um anexo adicional para um documento
 * @param file Arquivo a ser enviado
 * @param documentoId ID do documento ao qual o anexo pertence
 * @param userId ID do usuário que está enviando o anexo
 * @returns Promise com o anexo criado
 */
export async function uploadDocumentoAnexo(
  file: File,
  documentoId: string
): Promise<DocumentoAnexo> {
  try {
    // Gerar nome único para o arquivo
    const fileExt = file.name.split('.').pop()?.toLowerCase() || 'pdf';
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 8);
    const uniqueFileName = `documento_anexos/${timestamp}-${randomString}.${fileExt}`;

    // Upload do arquivo para o Storage usando o cliente Supabase
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('anexos')
      .upload(uniqueFileName, file, {
        cacheControl: '3600',
        upsert: false,
        contentType: file.type
      });

    if (uploadError) {
      // Mensagens de erro mais amigáveis
      let errorMessage = 'Erro ao fazer upload do arquivo';

      if (uploadError.message.includes('duplicate')) {
        errorMessage = 'Já existe um arquivo com este nome. Tente novamente.';
      } else if (uploadError.message.includes('permission')) {
        errorMessage = 'Sem permissão para fazer upload. Tente fazer login novamente.';
      } else if (uploadError.message.includes('storage')) {
        errorMessage = 'Problema no serviço de armazenamento. Tente novamente mais tarde.';
      } else {
        errorMessage = `Erro ao fazer upload: ${uploadError.message}`;
      }

      throw new Error(errorMessage);
    }

    // Obter a URL pública do arquivo
    const { data: publicUrlData } = supabase.storage
      .from('anexos')
      .getPublicUrl(uniqueFileName);

    const fileUrl = publicUrlData.publicUrl;

    // Inserir registro no banco de dados
    const { data: anexoData, error: insertError } = await supabase
      .from('documento_anexos')
      .insert({
        documento_id: documentoId,
        nome: file.name,
        arquivo_url: fileUrl,
        arquivo_path: uniqueFileName,
        tipo_arquivo: file.type,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (insertError) {
      // Se falhar ao inserir no banco, tentar remover o arquivo do storage
      try {
        await supabase.storage.from('anexos').remove([uniqueFileName]);
      } catch (cleanupError) {
        
      }

      throw new Error('Erro ao criar registro do anexo: ' + insertError.message);
    }

    return anexoData;
  } catch (error: any) {

// Verificar se é um erro de autenticação
    if (error.message && (
      error.message.includes('JWT') ||
      error.message.includes('token') ||
      error.message.includes('auth') ||
      error.message.includes('session')
    )) {
      throw new Error('Erro de autenticação. Por favor, recarregue a página e tente novamente.');
    }

    throw error;
  }
}

/**
 * Função para obter todos os anexos de um documento
 * @param documentoId ID do documento
 * @returns Promise com a lista de anexos
 */
export async function getDocumentoAnexos(documentoId: string): Promise<DocumentoAnexo[]> {
  try {
    // Usar diretamente o cliente Supabase em vez de fazer requisição HTTP
    const { data, error } = await supabase
      .from('documento_anexos')
      .select('*')
      .eq('documento_id', documentoId)
      .order('created_at', { ascending: false });

    if (error) {
      
      throw new Error(error.message || 'Erro ao buscar anexos');
    }

    return data || [];
  } catch (error: any) {

// Verificar se é um erro de autenticação
    if (error.message && (
      error.message.includes('JWT') ||
      error.message.includes('token') ||
      error.message.includes('auth') ||
      error.message.includes('session')
    )) {
      throw new Error('Erro de autenticação. Por favor, recarregue a página e tente novamente.');
    }

    throw error;
  }
}

/**
 * Função para excluir um anexo de documento
 * @param anexoId ID do anexo a ser excluído
 * @returns Promise vazia
 */
export async function excluirDocumentoAnexo(anexoId: string): Promise<void> {
  try {
    // Primeiro, buscar o anexo para obter o caminho do arquivo
    const { data: anexo, error: fetchError } = await supabase
      .from('documento_anexos')
      .select('arquivo_path')
      .eq('id', anexoId)
      .single();

    if (fetchError) {
      throw new Error(fetchError.message || 'Erro ao buscar anexo');
    }

    if (!anexo) {
      throw new Error('Anexo não encontrado');
    }

    // Excluir o registro do banco de dados
    const { error: deleteError } = await supabase
      .from('documento_anexos')
      .delete()
      .eq('id', anexoId);

    if (deleteError) {
      throw new Error(deleteError.message || 'Erro ao excluir anexo');
    }

    // Excluir o arquivo do storage
    if (anexo.arquivo_path) {
      const { error: storageError } = await supabase.storage
        .from('anexos')
        .remove([anexo.arquivo_path]);

      if (storageError) {
        
        // Não lançar erro aqui, pois o registro já foi removido do banco
      }
    }
  } catch (error: any) {

// Verificar se é um erro de autenticação
    if (error.message && (
      error.message.includes('JWT') ||
      error.message.includes('token') ||
      error.message.includes('auth') ||
      error.message.includes('session')
    )) {
      throw new Error('Erro de autenticação. Por favor, recarregue a página e tente novamente.');
    }

    throw error;
  }
}