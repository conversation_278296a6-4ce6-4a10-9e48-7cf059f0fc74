import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';

export const dynamic = 'force-dynamic';

// GET /api/obras - Lista todas as obras
export async function GET() {
  try {
    const supabase = await getSupabaseRouteClient();

    const { data, error } = await supabase
      .from('obras')
      .select('*')
      .order('nome', { ascending: true });

    if (error) {
      
      return NextResponse.json({ error: 'Erro ao buscar obras' }, { status: 500 });
    }

    return NextResponse.json({ data });
  } catch (error) {
    
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// POST /api/obras - Cria uma nova obra
export async function POST(request: NextRequest) {
  try {
    const supabase = await getSupabaseRouteClient();
    const body = await request.json();

    const { data, error } = await supabase
      .from('obras')
      .insert(body)
      .select()
      .single();

    if (error) {
      
      return NextResponse.json({ error: 'Erro ao criar obra' }, { status: 500 });
    }

    return NextResponse.json({ data });
  } catch (error) {
    
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}