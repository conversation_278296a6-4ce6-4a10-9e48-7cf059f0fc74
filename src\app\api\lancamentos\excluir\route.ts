import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

// Criar cliente Supabase com a chave de serviço para contornar o RLS
const createServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    return null;
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

export async function POST(request: NextRequest) {
  try {
    // Obter o ID do lançamento a ser excluído
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'ID do lançamento não fornecido' },
        { status: 400 }
      );
    }

    // Criar cliente de serviço
    const supabase = createServiceClient();
    if (!supabase) {
      return NextResponse.json(
        { error: 'Não foi possível criar o cliente de serviço' },
        { status: 500 }
      );
    }

    // Atualizar documentos para remover referência ao lançamento
    const { error: updateDocError } = await supabase
      .from('documentos')
      .update({ lancamento_id: null })
      .eq('lancamento_id', id);

    // Continuar mesmo com erro, pois pode não haver documentos

    // Excluir parcelas primeiro
    const { error: deleteParcelasError } = await supabase
      .from('parcelas')
      .delete()
      .eq('lancamento_id', id);

    // Continuar mesmo com erro, pois pode ser que as parcelas já tenham sido excluídas

    // Excluir o lançamento
    const { error: deleteLancamentoError } = await supabase
      .from('lancamentos')
      .delete()
      .eq('id', id);

    if (deleteLancamentoError) {
      return NextResponse.json(
        {
          success: false,
          error: `Erro ao excluir lançamento: ${deleteLancamentoError.message}`
        },
        { status: 500 }
      );
    }

    // Verificar se o lançamento foi realmente excluído
    const { data: checkData } = await supabase
      .from('lancamentos')
      .select('id')
      .eq('id', id);

    if (checkData && checkData.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'O lançamento não foi excluído após a operação'
        },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    return NextResponse.json(
      {
        success: false,
        error: `Erro interno: ${error.message}`
      },
      { status: 500 }
    );
  }
}