# Script para forçar limpeza completa do cache do TypeScript e workspace
Write-Host "🧹 Limpando cache do TypeScript e workspace..." -ForegroundColor Yellow

# Limpar cache do TypeScript
Write-Host "Limpando cache do TypeScript..." -ForegroundColor Blue
npx tsc --build --clean 2>$null

# Remover diretórios de cache
Write-Host "Removendo diretórios de cache..." -ForegroundColor Blue
Remove-Item -Recurse -Force ".next" -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force "node_modules/.cache" -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force ".eslintcache" -ErrorAction SilentlyContinue

# Limpar cache do npm
Write-Host "Limpando cache do npm..." -ForegroundColor Blue
npm cache clean --force 2>$null

# Verificar se não há mais arquivos de testes visuais
Write-Host "Verificando arquivos restantes..." -ForegroundColor Blue
$testFiles = Get-ChildItem -Recurse -Path "src" -Name "*testes-visuais*" -ErrorAction SilentlyContinue
if ($testFiles) {
    Write-Host "⚠️ Ainda existem arquivos de testes visuais:" -ForegroundColor Red
    $testFiles | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
} else {
    Write-Host "✅ Nenhum arquivo de testes visuais encontrado" -ForegroundColor Green
}

Write-Host "`n🔄 Para completar a limpeza:" -ForegroundColor Yellow
Write-Host "1. Feche completamente o VSCode" -ForegroundColor White
Write-Host "2. Reabra o VSCode" -ForegroundColor White
Write-Host "3. Execute Ctrl+Shift+P > 'TypeScript: Restart TS Server'" -ForegroundColor White
Write-Host "4. Execute Ctrl+Shift+P > 'Developer: Reload Window'" -ForegroundColor White

Write-Host "`n✅ Limpeza concluída!" -ForegroundColor Green 