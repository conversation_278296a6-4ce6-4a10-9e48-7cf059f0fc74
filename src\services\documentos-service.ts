'use client';

import { Documento, EstatisticasDocumentos, StatusDocumento, DadosExtraidos } from '@/types/documentos';
import { LancamentoFormData } from '@/types/lancamento';
import * as DocumentosAPI from './documentos';

/**
 * Serviço para gerenciar documentos usando as APIs
 * Este serviço é um wrapper para as funções de API
 */
export const DocumentosService = {
  /**
   * Obtém todos os documentos com filtro por status
   * @param status Status dos documentos a serem buscados
   * @returns Promise com a lista de documentos
   */
  async getDocumentos(status?: StatusDocumento): Promise<Documento[]> {
    try {
      return await DocumentosAPI.getDocumentos(status);
    } catch (error: any) {
      throw error;
    }
  },

  /**
   * Obtém um documento por ID
   * @param id ID do documento
   * @returns Promise com o documento ou null se não encontrado
   */
  async getDocumentoById(id: string): Promise<Documento | null> {
    try {
      return await DocumentosAPI.getDocumentoById(id);
    } catch (error: any) {
      throw error;
    }
  },

  /**
   * Faz upload de um documento
   * @param file Arquivo a ser enviado
   * @param userId ID do usuário que está enviando o documento
   * @returns Promise com o documento criado
   */
  async uploadDocumento(file: File, userId: string): Promise<Documento> {
    try {
      return await DocumentosAPI.uploadDocumento(file, userId);
    } catch (error: any) {
      throw error;
    }
  },

  /**
   * Atualiza os dados extraídos de um documento
   * @param id ID do documento
   * @param dadosExtraidos Dados extraídos pela IA
   * @returns Promise com o documento atualizado
   */
  async atualizarDadosExtraidos(id: string, dadosExtraidos: DadosExtraidos): Promise<Documento> {
    try {
      return await DocumentosAPI.atualizarDadosExtraidos(id, dadosExtraidos);
    } catch (error: any) {
      throw error;
    }
  },

  /**
   * Aprova um documento e cria um lançamento
   * @param id ID do documento
   * @param dadosLancamento Dados para criar o lançamento
   * @returns Promise com o documento atualizado
   */
  async aprovarDocumento(id: string, dadosLancamento: LancamentoFormData): Promise<Documento> {
    try {
      return await DocumentosAPI.aprovarDocumento(id, dadosLancamento);
    } catch (error: any) {
      throw error;
    }
  },

  /**
   * Rejeita um documento
   * @param id ID do documento
   * @param motivo Motivo da rejeição
   * @returns Promise com o documento atualizado
   */
  async rejeitarDocumento(id: string, motivo: string): Promise<Documento> {
    try {
      return await DocumentosAPI.rejeitarDocumento(id, motivo);
    } catch (error: any) {
      throw error;
    }
  },

  /**
   * Salva os dados de um documento sem alterar seu status
   * @param id ID do documento
   * @param dadosDocumento Dados do documento para salvar
   * @returns Promise com o documento atualizado
   */
  async salvarDocumento(id: string, dadosDocumento: any): Promise<Documento> {
    try {
      return await DocumentosAPI.salvarDocumento(id, dadosDocumento);
    } catch (error: any) {
      throw error;
    }
  },

  /**
   * Exclui um documento
   * @param id ID do documento
   * @returns Promise<void>
   */
  async excluirDocumento(id: string): Promise<void> {
    try {
      await DocumentosAPI.excluirDocumento(id);
    } catch (error: any) {
      throw error;
    }
  },

  /**
   * Obtém estatísticas dos documentos
   * @returns Promise com as estatísticas dos documentos
   */
  async getEstatisticasDocumentos(): Promise<EstatisticasDocumentos> {
    try {
      return await DocumentosAPI.getEstatisticasDocumentos();
    } catch (error: any) {
      throw error;
    }
  }
};