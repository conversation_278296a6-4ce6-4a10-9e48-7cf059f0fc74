'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { supabase } from '@/lib/supabase';
import { Obra } from '@/types/obras';
import { toast } from 'sonner';
import { Check, ChevronsUpDown, Loader2, Pencil } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { useObrasService } from '@/services/obras';
import { Spinner } from '@/components/ui/spinner';

const formSchema = z.object({
  nome: z.string().min(1, 'Nome é obrigatório'),
  descricao: z.string().optional(),
  cliente_id: z.string().optional(),
  cliente_nome: z.string().optional(),
  orcamento_previsto: z.string().optional(),
  data_inicio: z.string().optional(),
  data_fim: z.string().optional(),
  status: z.enum(['em_andamento', 'planejada', 'concluida', 'pausada', 'cancelada'], {
    required_error: 'Status é obrigatório'
  })
});

type FormData = z.infer<typeof formSchema>;

interface FormObraProps {
  obra?: Obra;
  onSuccess: () => void;
  onCancel: () => void;
}

interface Cliente {
  id: string;
  nome: string;
  email?: string;
  telefone?: string;
  tipo: 'fornecedor' | 'prestador' | 'cliente';
}

function FormEditarCliente({ cliente, onSave }: { cliente: Cliente; onSave: () => void }) {
  const [loading, setLoading] = useState(false);
  const [nome, setNome] = useState(cliente.nome);
  const [email, setEmail] = useState(cliente.email || '');
  const [telefone, setTelefone] = useState(cliente.telefone || '');
  const [tipo, setTipo] = useState<'fornecedor' | 'prestador' | 'cliente'>(cliente.tipo || 'cliente');

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);

    try {
      await supabase
        .from('contatos')
        .update({ nome, email, telefone, tipo })
        .eq('id', cliente.id);

      toast.success('Cliente atualizado com sucesso!');
      onSave();
    } catch (error) {
      toast.error('Erro ao atualizar cliente');
    } finally {
      setLoading(false);
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <FormLabel>Nome</FormLabel>
        <Input value={nome} onChange={e => setNome(e.target.value)} required />
      </div>
      
      <div className="space-y-2">
        <FormLabel>Tipo</FormLabel>
        <Select 
          value={tipo} 
          onValueChange={(value) => setTipo(value as 'fornecedor' | 'prestador' | 'cliente')}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="cliente">Cliente</SelectItem>
            <SelectItem value="fornecedor">Fornecedor</SelectItem>
            <SelectItem value="prestador">Prestador</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-2">
        <FormLabel>Email</FormLabel>
        <Input type="email" value={email} onChange={e => setEmail(e.target.value)} />
      </div>
      
      <div className="space-y-2">
        <FormLabel>Telefone</FormLabel>
        <Input value={telefone} onChange={e => setTelefone(e.target.value)} />
      </div>

      <div className="flex justify-end">
        <Button type="submit" disabled={loading}>
          {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Salvar'}
        </Button>
      </div>
    </form>
  );
}

export function FormObra({ obra, onSuccess, onCancel }: FormObraProps) {
  const [loading, setLoading] = useState(false);
  const [clientes, setClientes] = useState<Cliente[]>([]);
  const [openCombobox, setOpenCombobox] = useState(false);
  const [clienteSelecionado, setClienteSelecionado] = useState<Cliente | null>(null);
  const [openEditarCliente, setOpenEditarCliente] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const obrasService = useObrasService();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      nome: obra?.nome || '',
      descricao: obra?.descricao || '',
      cliente_id: obra?.cliente_id || '',
      orcamento_previsto: obra?.orcamento_previsto?.toString() || '',
      data_inicio: obra?.data_inicio || '',
      data_fim: obra?.data_fim || '',
      status: obra?.status || 'planejada'
    }
  });

  useEffect(() => {
    loadClientes();
    if (obra?.cliente_id) {
      loadClienteSelecionado(obra.cliente_id);
    }
  }, [obra]);

  async function loadClientes() {
    const { data } = await supabase
      .from('contatos')
      .select('id, nome, email, telefone, tipo')
      .order('nome');
    
    if (data) {
      setClientes(data);
      
      if (obra?.cliente_id) {
        const cliente = data.find(c => c.id === obra.cliente_id);
        if (cliente) {
          setClienteSelecionado(cliente);
        }
      }
    }
  }

  async function loadClienteSelecionado(clienteId: string) {
    const { data } = await supabase
      .from('contatos')
      .select('id, nome, email, telefone, tipo')
      .eq('id', clienteId)
      .single();
    
    if (data) {
      setClienteSelecionado(data);
    }
  }

  async function criarCliente(nome: string) {
    try {
      const { data, error } = await supabase
        .from('contatos')
        .insert([{ 
          nome, 
          tipo: 'cliente' // valor padrão ao criar rapidamente
        }])
        .select()
        .single();

      if (error) throw error;

      if (data) {
        const novoCliente = { 
          id: data.id, 
          nome: data.nome, 
          email: data.email, 
          telefone: data.telefone,
          tipo: data.tipo
        };
        setClientes(prev => [...prev, novoCliente]);
        setClienteSelecionado(novoCliente);
        form.setValue('cliente_id', data.id);
        toast.success('Cliente criado com sucesso!');
      }
    } catch (error) {
      toast.error('Erro ao criar cliente');
    }
  }

  async function onSubmit(data: FormData) {
    try {
      setLoading(true);
      
      // Prepara os dados básicos garantindo valores não nulos
      const baseData = {
        nome: data.nome,
        descricao: data.descricao || '',
        cliente_id: clienteSelecionado?.id || null,
        orcamento_previsto: data.orcamento_previsto ? Number(data.orcamento_previsto) : null,
        data_inicio: data.data_inicio || null,
        data_fim: data.data_fim || null,
        status: data.status
      };

      if (obra?.id) {
        // Em modo de edição, mantém os valores existentes se não houver alteração
        const updateData = {
          nome: data.nome || obra.nome,
          descricao: data.descricao || obra.descricao || '',
          cliente_id: clienteSelecionado?.id || obra.cliente_id,
          orcamento_previsto: data.orcamento_previsto ? Number(data.orcamento_previsto) : obra.orcamento_previsto,
          data_inicio: data.data_inicio || obra.data_inicio,
          data_fim: data.data_fim || obra.data_fim,
          status: data.status || obra.status
        };

        await obrasService.atualizarObra(obra.id, updateData);
      } else {
        await obrasService.criarObra(baseData);
      }

      onSuccess();
    } catch (error: any) {
      
      toast.error(error.message || 'Erro ao salvar obra');
    } finally {
      setLoading(false);
    }
  }

  const handleDelete = async () => {
    try {
      setLoading(true);
      const { error } = await supabase
        .from('obras')
        .delete()
        .eq('id', obra?.id);

      if (error) throw error;

      toast.success('Obra excluída com sucesso');
      onSuccess?.();
    } catch (error: any) {
      toast.error('Erro ao excluir obra');
      
    } finally {
      setLoading(false);
      setShowDeleteDialog(false);
    }
  };

  const filteredClientes = searchTerm === '' 
    ? clientes 
    : clientes.filter(cliente => 
        cliente.nome.toLowerCase().includes(searchTerm.toLowerCase())
      );

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="nome"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nome da Obra</FormLabel>
                <FormControl>
                  <Input placeholder="Digite o nome da obra" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="descricao"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Descrição</FormLabel>
                <FormControl>
                  <Textarea placeholder="Digite uma descrição para a obra" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormItem className="flex flex-col gap-2">
            <FormLabel>Cliente</FormLabel>
            <Popover open={openCombobox} onOpenChange={setOpenCombobox}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={openCombobox}
                  className="justify-between"
                >
                  {clienteSelecionado ? clienteSelecionado.nome : 'Selecione um cliente...'}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="p-0" align="start">
                <Command>
                  <CommandInput 
                    placeholder="Buscar cliente..." 
                    value={searchTerm}
                    onValueChange={setSearchTerm}
                  />
                  <CommandList>
                    <div className="flex flex-col gap-2 p-2">
                      <Button
                        type="button"
                        variant="secondary"
                        className="w-full"
                        onClick={() => criarCliente(searchTerm)}
                      >
                        Criar "{searchTerm}"
                      </Button>
                    </div>
                  </CommandList>
                  <CommandGroup>
                    {clientes.map((cliente) => (
                      <CommandItem
                        key={cliente.id}
                        value={cliente.nome}
                        onSelect={() => {
                          setClienteSelecionado(cliente);
                          form.setValue('cliente_id', cliente.id);
                          setOpenCombobox(false);
                        }}
                      >
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            clienteSelecionado?.id === cliente.id ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <div className="flex-1">{cliente.nome}</div>
                        {cliente.id === clienteSelecionado?.id && (
                          <Dialog open={openEditarCliente} onOpenChange={setOpenEditarCliente}>
                            <DialogTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <Pencil className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Editar Cliente</DialogTitle>
                              </DialogHeader>
                              <FormEditarCliente 
                                cliente={cliente} 
                                onSave={() => {
                                  setOpenEditarCliente(false);
                                  loadClientes();
                                }} 
                              />
                            </DialogContent>
                          </Dialog>
                        )}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </Command>
              </PopoverContent>
            </Popover>
          </FormItem>

          <FormField
            control={form.control}
            name="orcamento_previsto"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Orçamento Previsto</FormLabel>
                <FormControl>
                  <Input type="number" placeholder="Digite o orçamento previsto" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="data_inicio"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Data de Início</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="data_fim"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Data de Conclusão</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="planejada">Planejada</SelectItem>
                    <SelectItem value="em_andamento">Em Andamento</SelectItem>
                    <SelectItem value="concluida">Concluída</SelectItem>
                    <SelectItem value="pausada">Pausada</SelectItem>
                    <SelectItem value="cancelada">Cancelada</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end gap-4 pt-4">
            {obra && (
              <Button
                type="button"
                variant="destructive"
                onClick={() => setShowDeleteDialog(true)}
                disabled={loading}
                size="sm"
                className="h-8 gap-1"
              >
                <span>Excluir</span>
              </Button>
            )}
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel} 
              disabled={loading}
              size="sm"
              className="h-8 gap-1"
            >
              <span>Cancelar</span>
            </Button>
            <Button 
              type="submit" 
              disabled={loading}
              size="sm"
              className="h-8 gap-1"
            >
              {loading && <Spinner className="h-4 w-4" />}
              <span>Salvar</span>
            </Button>
          </div>
        </form>
      </Form>

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Excluir Obra</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir esta obra? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowDeleteDialog(false)} 
              disabled={loading}
              size="sm"
              className="h-8 gap-1"
            >
              <span>Cancelar</span>
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDelete} 
              disabled={loading}
              size="sm"
              className="h-8 gap-1"
            >
              {loading && <Spinner className="h-4 w-4" />}
              <span>Excluir</span>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}