'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase/client';

export function useAuth() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userId, setUserId] = useState<string | null>(null);
  const [userEmail, setUserEmail] = useState<string | null>(null);

  // Função para verificar a autenticação
  const checkAuth = async () => {
    try {
      setIsLoading(true);

      // Verificar a sessão atual
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        
        setIsAuthenticated(false);
        setUserId(null);
        setUserEmail(null);
        return;
      }

      if (session) {
        setIsAuthenticated(true);
        setUserId(session.user.id);
        setUserEmail(session.user.email || null);
      } else {
        // Tentar obter o usuário como alternativa
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError) {
          
          setIsAuthenticated(false);
          setUserId(null);
          setUserEmail(null);
          return;
        }

        if (user) {
          setIsAuthenticated(true);
          setUserId(user.id);
          setUserEmail(user.email || null);
        } else {
          setIsAuthenticated(false);
          setUserId(null);
          setUserEmail(null);
        }
      }
    } catch (error) {
      
      setIsAuthenticated(false);
      setUserId(null);
      setUserEmail(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Função para atualizar a sessão
  const refreshAuth = async () => {
    try {
      setIsLoading(true);

      // Tentar atualizar a sessão
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        
        return false;
      }

      if (data.session) {
        setIsAuthenticated(true);
        setUserId(data.session.user.id);
        setUserEmail(data.session.user.email || null);
        return true;
      } else {
        return false;
      }
    } catch (error) {
      
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Verificar autenticação ao montar o componente
    checkAuth();

    // Configurar listener para mudanças de autenticação
    const { data: authListener } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          setIsAuthenticated(true);
          setUserId(session.user.id);
          setUserEmail(session.user.email || null);
        } else if (event === 'SIGNED_OUT') {
          setIsAuthenticated(false);
          setUserId(null);
          setUserEmail(null);
        } else if (event === 'TOKEN_REFRESHED' && session) {
          setIsAuthenticated(true);
          setUserId(session.user.id);
          setUserEmail(session.user.email || null);
        }
      }
    );

    // Limpar listener ao desmontar
    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  return {
    isAuthenticated,
    isLoading,
    userId,
    userEmail,
    refreshAuth,
    checkAuth
  };
}