'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { BookOpen, Check, ChevronDown, Edit, MoreHorizontal, Trash, Plus, Save, Star, X } from 'lucide-react'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { toast } from 'sonner'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { overlayStyles } from '@/lib/utils'
import SalvarVisualizacaoButton from './salvar-visualizacao-button'
import { supabase } from '@/lib/supabase/client'
import {
  Visualizacao,
  buscarVisualizacaoPorId,
  atualizarVisualizacao,
  excluirVisualizacao,
  definirVisualizacaoPadrao
} from './visualizacoes-service'

interface VisualizacoesDropdownProps {
  onSelect: (filtros: any) => void
  onRefreshNeeded: () => void
  visualizacoes: Visualizacao[]
  isLoading: boolean
  filtrosAtuais: any
  onVisualizacaoCriada: (visualizacao: any) => void
  visualizacaoAtiva?: Visualizacao | null
  setVisualizacaoAtiva?: (v: Visualizacao | null) => void
  setVisualizacaoManualmenteLimpa?: (limpa: boolean) => void
}

export default function VisualizacoesDropdown({
  onSelect,
  onRefreshNeeded,
  visualizacoes,
  isLoading,
  filtrosAtuais,
  onVisualizacaoCriada,
  visualizacaoAtiva,
  setVisualizacaoAtiva,
  setVisualizacaoManualmenteLimpa
}: VisualizacoesDropdownProps) {
  const [open, setOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [selectedView, setSelectedView] = useState<Visualizacao | null>(null)
  const [editNome, setEditNome] = useState('')
  const [editDescricao, setEditDescricao] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [userId, setUserId] = useState<string | null>(null)

  // Encontrar a visualização ativa pelo ID no localStorage se não for fornecida
  useEffect(() => {
    if (!visualizacaoAtiva && visualizacoes.length > 0 && typeof window !== 'undefined') {
      const idSalvo = window.localStorage.getItem('visualizacao_lancamentos_ativa');
      if (idSalvo) {
        const vSalva = visualizacoes.find(v => v.id === idSalvo);
        if (vSalva && setVisualizacaoAtiva) {
          setVisualizacaoAtiva(vSalva);
        }
      }
    }
  }, [visualizacoes, visualizacaoAtiva, setVisualizacaoAtiva]);

  // Não precisamos mais verificar se há filtros ativos
  // Agora permitimos salvar visualizações sem filtros
  const temFiltrosAtivos = true;

  // Obter o ID do usuário ao montar o componente
  useEffect(() => {
    async function getUserId() {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) {
          
          return;
        }

        if (session?.user) {
          setUserId(session.user.id);
        }
      } catch (error) {
        
      }
    }

    getUserId();
  }, []);

  // Abrir diálogo de edição
  const handleEditClick = async (view: Visualizacao) => {
    // Buscar a visualização mais recente por ID para garantir dados atualizados
    try {
      const updatedView = await buscarVisualizacaoPorId(view.id);

      if (!updatedView) {
        // Se não encontrou a visualização, não abrir o diálogo
        toast.warning('Esta visualização não existe mais ou foi removida');
        onRefreshNeeded();
        return;
      }

      setSelectedView(updatedView)
      setEditNome(updatedView.nome)
      setEditDescricao(updatedView.descricao || '')
      setEditDialogOpen(true)
    } catch (error) {
      
      toast.error('Não foi possível carregar os dados da visualização');
    }
  }

  // Abrir diálogo de exclusão
  const handleDeleteClick = async (view: Visualizacao) => {
    // Buscar a visualização mais recente por ID para garantir dados atualizados
    try {
      const updatedView = await buscarVisualizacaoPorId(view.id);

      if (!updatedView) {
        // Se não encontrou a visualização, não abrir o diálogo
        toast.warning('Esta visualização não existe mais ou foi removida');
        onRefreshNeeded();
        return;
      }

      setSelectedView(updatedView)
      setDeleteDialogOpen(true)
    } catch (error) {
      
      toast.error('Não foi possível carregar os dados da visualização');
    }
  }

  // Salvar edição
  const handleSaveEdit = async () => {
    if (!selectedView) return

    setIsSubmitting(true)

    try {
      // Usar o serviço para atualizar a visualização
      const updatedView = await atualizarVisualizacao(
        selectedView.id,
        editNome,
        editDescricao,
        selectedView.filtros_json
      );

      if (!updatedView) {
        toast.warning('A visualização não existe mais ou foi removida');
        setEditDialogOpen(false);
        onRefreshNeeded();
        return;
      }

      toast.success('Visualização atualizada com sucesso')
      setEditDialogOpen(false)
      onRefreshNeeded()
    } catch (error: any) {
      // Verificar se a mensagem de erro indica que a visualização não foi encontrada
      if (error.message && error.message.includes('não encontrada')) {
        toast.warning('A visualização não existe mais ou foi removida');
        onRefreshNeeded();
      } else {
        toast.error(error.message || 'Erro ao atualizar visualização');
      }

      // Fechar o diálogo mesmo com erro
      setEditDialogOpen(false)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Confirmar exclusão
  const handleConfirmDelete = async () => {
    if (!selectedView) return

    setIsSubmitting(true)
    try {
      // Usar o serviço para excluir a visualização
      const success = await excluirVisualizacao(selectedView.id);

      if (!success) {toast.warning('A visualização não existe mais ou foi removida');
        setDeleteDialogOpen(false);
        onRefreshNeeded();
        return;
      }

      toast.success('Visualização excluída com sucesso')
      setDeleteDialogOpen(false)
      onRefreshNeeded()
    } catch (error: any) {

// Verificar se a mensagem de erro indica que a visualização não foi encontrada
      if (error.message && error.message.includes('não encontrada')) {
        toast.warning('A visualização não existe mais ou foi removida');
        onRefreshNeeded();
      } else {
        toast.error(error.message || 'Erro ao excluir visualização');
      }

      // Fechar o diálogo mesmo com erro
      setDeleteDialogOpen(false)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Definir visualização como padrão
  const handleSetDefault = async (view: Visualizacao) => {
    if (!userId) {
      toast.error('Você precisa estar autenticado para definir uma visualização como padrão');
      return;
    }

    // Mostrar toast de carregamento
    const loadingToast = toast.loading('Definindo visualização como padrão...');

    try {
      // Verificar primeiro se a visualização existe
      const visualizacaoAtualizada = await buscarVisualizacaoPorId(view.id);

      if (!visualizacaoAtualizada) {
        toast.dismiss(loadingToast);
        toast.warning('Esta visualização não existe mais ou foi removida');
        onRefreshNeeded();
        return;
      }

      const success = await definirVisualizacaoPadrao(view.id, userId);

      // Remover toast de carregamento
      toast.dismiss(loadingToast);

      if (success) {
        toast.success('Visualização definida como padrão');
        // Recarregar a lista de visualizações para mostrar a atualização
        onRefreshNeeded();
      } else {
        toast.error('Não foi possível definir a visualização como padrão');
        // Tentar recarregar mesmo assim para garantir que a lista esteja atualizada
        onRefreshNeeded();
      }
    } catch (error: any) {
      // Remover toast de carregamento em caso de erro
      toast.dismiss(loadingToast);

toast.error(error.message || 'Erro ao definir visualização como padrão');

      // Tentar recarregar visualizações mesmo em caso de erro
      onRefreshNeeded();
    }
  }

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-9 gap-1 border border-gray-300 rounded-md bg-white"
          >
            <BookOpen className="h-4 w-4" />
            {visualizacaoAtiva ? (
              <span className="flex items-center gap-1">
                {visualizacaoAtiva.is_default && (
                  <Star className="h-3 w-3 text-amber-500 fill-amber-500" />
                )}
                <span className="truncate max-w-[120px]">{visualizacaoAtiva.nome}</span>
              </span>
            ) : (
              <span>Visualizações</span>
            )}
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className={`w-[300px] p-0 ${overlayStyles()} rounded-md border shadow-md bg-white z-50`} align="start">
          <div className="p-3 border-b flex justify-between items-center">
            <h3 className="font-medium">Visualizações salvas</h3>
            <div className="flex gap-1">
              {visualizacaoAtiva && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2 text-xs"
                  onClick={() => {
                    // Limpar a visualização ativa
                    if (setVisualizacaoAtiva) {
                      setVisualizacaoAtiva(null);
                    }

                    // Marcar como manualmente limpa para evitar recarregar a visualização padrão
                    if (setVisualizacaoManualmenteLimpa) {
                      setVisualizacaoManualmenteLimpa(true);
                    }

                    // Limpar os filtros
                    onSelect({});

                    // Remover do localStorage
                    if (typeof window !== 'undefined') {
                      window.localStorage.removeItem('visualizacao_lancamentos_ativa');
                      window.localStorage.removeItem('visualizacao_lancamentos_ativa_when_collapsed');
                    }

                    toast.success('Visualização limpa');
                    setOpen(false);
                  }}
                >
                  <X className="h-3.5 w-3.5 mr-1" />
                  Limpar
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-xs"
                onClick={() => {
                  setCreateDialogOpen(true);
                  setOpen(false);
                }}
              >
                <Plus className="h-3.5 w-3.5 mr-1" />
                Nova
              </Button>
            </div>
          </div>

          {isLoading ? (
            <div className="p-4 space-y-3">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
            </div>
          ) : visualizacoes.length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">
              <p>Nenhuma visualização salva</p>
              <p className="text-sm mt-1">Aplique filtros e clique em &quot;Salvar visualização&quot;</p>
            </div>
          ) : (
            <ScrollArea className="max-h-[300px]">
              <div className="p-2">
                {visualizacoes.map((view) => {
                  // Usar a propriedade _temFiltrosValidos se existir, ou verificar manualmente
                  // Agora consideramos visualizações sem filtros como válidas também
                  const temFiltrosValidos = view._temFiltrosValidos !== undefined
                    ? view._temFiltrosValidos
                    : (view.filtros_json && typeof view.filtros_json === 'object');

                  return (
                    <div
                      key={view.id}
                      className={`flex items-center justify-between p-2 rounded-md hover:bg-gray-50 cursor-pointer group ${!temFiltrosValidos ? 'opacity-70' : ''} ${visualizacaoAtiva?.id === view.id ? 'bg-gray-100' : ''}`}
                    >
                      <div
                        className="flex-1 truncate text-sm font-normal"
                        onClick={() => {
                          if (temFiltrosValidos) {
                            // Apply the visualization
                            onSelect(view.filtros_json || {});

                            // Save the visualization ID in localStorage
                            if (typeof window !== 'undefined') {
                              window.localStorage.setItem('visualizacao_lancamentos_ativa', view.id);

                              // Check if sidebar is collapsed and save the visualization for that state too
                              const isSidebarCollapsed = window.localStorage.getItem('sidebar_collapsed') === 'true';
                              if (isSidebarCollapsed) {
                                window.localStorage.setItem('visualizacao_lancamentos_ativa_when_collapsed', view.id);
                              }
                            }

                            // Atualizar a visualização ativa
                            if (setVisualizacaoAtiva) {
                              setVisualizacaoAtiva(view);
                            }

                            // Resetar a flag de visualização manualmente limpa
                            if (setVisualizacaoManualmenteLimpa) {
                              setVisualizacaoManualmenteLimpa(false);
                            }

                            setOpen(false);
                          } else {
                            toast.warning('Esta visualização contém filtros em formato inválido');
                          }
                        }}
                      >
                        <div className="truncate font-medium flex items-center">
                          {visualizacaoAtiva?.id === view.id && (
                            <Check className="h-3.5 w-3.5 mr-1 text-green-500" />
                          )}
                          {view.is_default && (
                            <Star className="h-3.5 w-3.5 mr-1 text-amber-500 fill-amber-500" />
                          )}
                          {view.nome}
                          {!temFiltrosValidos && (
                            <span className="ml-1 text-xs text-amber-500">(formato inválido)</span>
                          )}
                        </div>
                        {view.descricao && (
                          <div className="text-xs text-muted-foreground truncate">{view.descricao}</div>
                        )}
                      </div>
                      <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        {!view.is_default && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7 text-amber-500 hover:text-amber-600"
                            onClick={e => { e.stopPropagation(); handleSetDefault(view); }}
                            aria-label="Definir como padrão"
                            title="Definir como padrão"
                          >
                            <Star className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7"
                          onClick={e => { e.stopPropagation(); handleEditClick(view); }}
                          aria-label="Editar visualização"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 text-destructive hover:text-destructive"
                          onClick={e => { e.stopPropagation(); handleDeleteClick(view); }}
                          aria-label="Excluir visualização"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                        {visualizacaoAtiva?.id === view.id && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7"
                            onClick={e => {
                              e.stopPropagation();
                              // Limpar a visualização ativa
                              if (setVisualizacaoAtiva) {
                                setVisualizacaoAtiva(null);
                              }

                              // Marcar como manualmente limpa para evitar recarregar a visualização padrão
                              if (setVisualizacaoManualmenteLimpa) {
                                setVisualizacaoManualmenteLimpa(true);
                              }

                              // Limpar os filtros
                              onSelect({});

                              // Remover do localStorage
                              if (typeof window !== 'undefined') {
                                window.localStorage.removeItem('visualizacao_lancamentos_ativa');
                                window.localStorage.removeItem('visualizacao_lancamentos_ativa_when_collapsed');
                              }

                              toast.success('Visualização limpa');
                              setOpen(false);
                            }}
                            aria-label="Limpar visualização"
                            title="Limpar visualização"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  );
                })}
                <div className="mt-2">
                  <SalvarVisualizacaoButton
                    filtrosAtuais={filtrosAtuais}
                    onVisualizacaoCriada={(nova) => {
                      if (typeof onVisualizacaoCriada === 'function') {
                        onVisualizacaoCriada(nova);
                      }
                      onRefreshNeeded();
                    }}
                    disabled={!temFiltrosAtivos}
                  />
                </div>
              </div>
            </ScrollArea>
          )}
        </PopoverContent>
      </Popover>

      {/* Diálogo de edição */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[480px]">
          <DialogHeader>
            <DialogTitle>Editar visualização</DialogTitle>
            <DialogDescription>Altere o nome ou descrição da visualização.</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Nome <span className="text-destructive">*</span></label>
              <Input
                value={editNome}
                onChange={e => setEditNome(e.target.value)}
                maxLength={60}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Descrição <span className="text-muted-foreground">(opcional)</span></label>
              <Textarea
                value={editDescricao}
                onChange={e => setEditDescricao(e.target.value)}
                className="resize-none h-[80px]"
                maxLength={200}
              />
            </div>
            <div className="flex justify-end gap-2 pt-2">
              <Button
                variant="ghost"
                onClick={() => setEditDialogOpen(false)}
                disabled={isSubmitting}
              >
                Cancelar
              </Button>
              <Button
                onClick={handleSaveEdit}
                disabled={isSubmitting || !editNome.trim()}
              >
                {isSubmitting ? 'Salvando...' : 'Salvar alterações'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Diálogo de exclusão */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[480px]">
          <DialogHeader>
            <DialogTitle>Excluir visualização</DialogTitle>
            <DialogDescription>Esta ação não pode ser desfeita.</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <p>
              Tem certeza que deseja excluir a visualização <strong>{selectedView?.nome}</strong>?
            </p>
            <div className="flex justify-end gap-2 pt-2">
              <Button
                variant="ghost"
                onClick={() => setDeleteDialogOpen(false)}
                disabled={isSubmitting}
              >
                Cancelar
              </Button>
              <Button
                variant="destructive"
                onClick={handleConfirmDelete}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Excluindo...' : 'Excluir visualização'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Diálogo de criação */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="sm:max-w-[480px]">
          <DialogHeader>
            <DialogTitle>Nova visualização</DialogTitle>
            <DialogDescription>Salve os filtros atuais para reutilizar depois.</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <SalvarVisualizacaoButton
              filtrosAtuais={filtrosAtuais}
              onVisualizacaoCriada={(nova) => {
                if (typeof onVisualizacaoCriada === 'function') {
                  onVisualizacaoCriada(nova);
                }
                onRefreshNeeded();
                setCreateDialogOpen(false);
              }}
              renderAsForm={true}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}