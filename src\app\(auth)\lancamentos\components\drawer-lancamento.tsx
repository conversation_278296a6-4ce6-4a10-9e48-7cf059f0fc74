'use client';

import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON><PERSON>er,
  Sheet<PERSON>itle,
  SheetDes<PERSON>,
  SheetFooter,
} from '@/components/ui/sheet';
import { FormLancamento } from './form-lancamento';
import { Lancamento, LancamentoComParcelas } from '@/types/lancamentos';
import { Button } from '@/components/ui/button';
import { Loader2, FileText } from 'lucide-react';
import { useEffect, useState, useRef } from 'react';
import { toast } from 'sonner';
import { ListaParcelasDrawer } from './lista-parcelas-drawer';
import { updateParcela } from '@/services/parcelas';
import { format, addDays, parseISO } from 'date-fns';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getSignedUrlAnexo, extrairPathDeUrl } from '@/lib/supabase';
import { FilePreview } from '@/components/FilePreview';

interface DrawerLancamentoProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  lancamento?: LancamentoComParcelas;
  onSubmit?: () => void;
}

export function DrawerLancamento({
  open,
  onOpenChange,
  lancamento,
  onSubmit,
}: DrawerLancamentoProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const formRef = useRef<any>(null);
  const [parcelas, setParcelas] = useState<any[]>([]);
  const [carregandoParcelas, setCarregandoParcelas] = useState(false);

  // Estados para preview de documentos
  const [previewFile, setPreviewFile] = useState<{ url?: string; name?: string; type?: string }>({});
  const [previewOpen, setPreviewOpen] = useState(false);





  // Carregar parcelas do lançamento quando o drawer for aberto
  useEffect(() => {
    // Só carregar se drawer estiver aberto E se tiver um lançamento válido
    if (open && lancamento?.id) {
      // Adicionar um pequeno delay para evitar requisições múltiplas
      const timeoutId = setTimeout(() => {
        carregarParcelas();
      }, 100);
      
      return () => clearTimeout(timeoutId);
    } else {
      setParcelas([]);
    }
  }, [open, lancamento?.id]);

  const carregarParcelas = async () => {
    if (!lancamento?.id) return;

    // Evitar requisições duplicadas se já estiver carregando
    if (carregandoParcelas) return;

    try {
      setCarregandoParcelas(true);

      // Buscar as parcelas do lançamento da API
      const response = await fetch(`/api/lancamentos/${lancamento.id}/parcelas`);

      if (!response.ok) {
        throw new Error('Erro ao carregar parcelas');
      }

      const data = await response.json();
      setParcelas(data);
    } catch (error) {
      toast.error('Não foi possível carregar as parcelas deste lançamento');
    } finally {
      setCarregandoParcelas(false);
    }
  };

  const handleFormSubmit = () => {
    setIsSubmitting(true);
    setTimeout(() => {
      const form = document.getElementById('lancamento-form-id');
      if (form && form instanceof HTMLFormElement) {
        form.requestSubmit();
      } else {
        setIsSubmitting(false);
      }
    }, 0);
  };

  useEffect(() => {
    if (!open) {
      setIsSubmitting(false);
    }
  }, [open]);

  // Função para resetar o estado de isSubmitting em caso de erro de validação
  const handleInvalid = () => {
    setIsSubmitting(false);
  };



  // Função para editar parcela
  const handleEditarParcela = async (parcela: any, indice: number, total: number) => {
    try {
      if (!parcela || !parcela.id) {
        toast.error('Parcela inválida ou sem ID');
        return;
      }

      // Converter o valor para número com segurança
      const valorNumerico = converterParaNumero(parcela.valor);

      // Preparar os dados para atualização
      const dadosAtualizacao = {
        valor: valorNumerico,
        vencimento: parcela.vencimento ? parcela.vencimento.toString() : null,
        status: parcela.status,
      };

      // Atualizar a parcela via API com valores formatados corretamente
      await updateParcela(parcela.id, dadosAtualizacao);

      // Atualizar a lista de parcelas
      await carregarParcelas();
      toast.success('Parcela atualizada com sucesso');
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Erro ao atualizar parcela');
    }
  };

  // Função para remover parcela
  const handleRemoverParcela = async (parcelaId: string) => {
    try {
      const response = await fetch(`/api/parcelas/${parcelaId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Erro ao remover parcela');
      }

      // Atualizar a lista de parcelas após remoção
      await carregarParcelas();
    } catch (error) {
      throw error; // Propagar erro para ser tratado no componente filho
    }
  };

  // Função para converter qualquer valor para número
  const converterParaNumero = (valor: any): number => {
    if (typeof valor === 'number') return valor;
    if (typeof valor === 'string') {
      // Remover caracteres não numéricos exceto ponto e vírgula
      const valorLimpo = valor.replace(/[^\d,.]/g, '').replace(',', '.');
      return parseFloat(valorLimpo) || 0;
    }
    return 0;
  };

  // Função para visualizar documento
  const handleViewDocument = async (doc: any) => {
    try {
      // Extrair o path do arquivo da URL
      const path = extrairPathDeUrl(doc.arquivo_url);
      if (!path) {
        toast.error('Não foi possível obter o caminho do arquivo');
        return;
      }

      // Obter URL assinada para o arquivo
      const url = await getSignedUrlAnexo(path);

      setPreviewFile({
        url,
        name: doc.nome,
        type: doc.tipo_arquivo
      });
      setPreviewOpen(true);
    } catch (error) {
      toast.error('Erro ao gerar link para visualização do documento');
    }
  };

  // Função para adicionar nova parcela
  const handleAdicionarParcela = async () => {
    if (!lancamento?.id) {
      toast.error('Lançamento não encontrado');
      return;
    }

    try {
      let novaParcela;

      if (parcelas.length === 0) {
        // Se não houver parcelas, criar a primeira
        const dataAtual = new Date();
        const vencimentoFormatado = format(dataAtual, 'yyyy-MM-dd');

        novaParcela = {
          lancamento_id: lancamento.id,
          numero: 1,
          valor: converterParaNumero(lancamento.valor_total),
          vencimento: vencimentoFormatado,
          status: 'pendente',
        };
      } else {
        // Obter a última parcela
        const ultimaParcela = [...parcelas].sort((a, b) => b.numero - a.numero)[0];

        // Calcular próximo vencimento (30 dias após o vencimento da última parcela)
        let proximoVencimento = new Date();
        if (ultimaParcela.vencimento) {
          proximoVencimento = addDays(parseISO(ultimaParcela.vencimento), 30);
        }

        // Formatar data para o formato esperado pela API
        const vencimentoFormatado = format(proximoVencimento, 'yyyy-MM-dd');

        // Criar nova parcela
        novaParcela = {
          lancamento_id: lancamento.id,
          numero: ultimaParcela.numero + 1,
          valor: converterParaNumero(ultimaParcela.valor),
          vencimento: vencimentoFormatado,
          status: 'pendente',
        };
      }

      // Salvar nova parcela via API
      const response = await fetch('/api/parcelas', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(novaParcela),
      });

      const responseData = await response.json().catch(() => null);

      if (!response.ok) {
        const errorMessage = responseData?.error || 'Erro ao adicionar parcela';
        throw new Error(errorMessage);
      }

      // Atualizar a lista de parcelas
      await carregarParcelas();
      toast.success('Parcela adicionada com sucesso');
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Erro ao adicionar parcela');
    }
  };

  return (
    <>
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent 
          className="sm:max-w-[600px] flex flex-col h-full p-0"
          side="right"
        >
          <SheetHeader className="shrink-0 p-6 pb-2">
            <SheetTitle>{lancamento ? 'Editar Lançamento' : 'Novo Lançamento'}</SheetTitle>
            <SheetDescription>
              {lancamento ? (
                'Faça as alterações necessárias nos dados do lançamento.'
              ) : (
                'Preencha os dados para criar um novo lançamento.'
              )}
            </SheetDescription>
          </SheetHeader>



          <div className="flex-grow overflow-y-auto p-6 pt-4">
            <FormLancamento
              lancamento={lancamento ? {
                ...lancamento,
                contatos: lancamento.contatos ? {
                  id: lancamento.contato_id || '',
                  nome_empresa: lancamento.contatos.nome_empresa || ''
                } : null,
                obras: lancamento.obras ? {
                  id: lancamento.obra_id || '',
                  nome: lancamento.obras.nome || ''
                } : null,
                categorias: lancamento.categorias ? {
                  id: lancamento.categoria_id || '',
                  nome: lancamento.categorias.nome || ''
                } : null
              } : undefined}
              onSuccess={() => {
                setIsSubmitting(false);
                onOpenChange(false);
                onSubmit?.();
              }}
              onInvalid={handleInvalid}
            />

            {/* Lista de parcelas */}
            {!carregandoParcelas && parcelas.length > 0 && (
              <ListaParcelasDrawer
                parcelas={parcelas}
                valorTotalLancamento={lancamento?.valor_total || 0}
                onEditParcela={handleEditarParcela}
                onRemoveParcela={handleRemoverParcela}
                onAdicionarParcela={handleAdicionarParcela}
              />
            )}

            {/* Documentos anexados */}
            {lancamento?.documentos && lancamento.documentos.length > 0 && (
              <Card className="mt-6">
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">Documentos Anexados</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {lancamento.documentos.map((doc) => (
                      <Button
                        key={doc.id}
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2 h-auto py-2"
                        onClick={() => handleViewDocument(doc)}
                      >
                        <FileText className="h-4 w-4" />
                        <span className="truncate max-w-[200px]">{doc.nome}</span>
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}


          </div>

          <SheetFooter className="shrink-0 border-t bg-background p-4 flex flex-row justify-end gap-2">
            <Button
              onClick={() => onOpenChange(false)}
              variant="outline"
              disabled={isSubmitting}
              type="button"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleFormSubmit}
              disabled={isSubmitting}
              type="button"
            >
              {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Salvar
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* Componente de preview de arquivo */}
      <FilePreview
        file={previewFile}
        open={previewOpen}
        onOpenChange={setPreviewOpen}
        title={previewFile.name}
      />


    </>
  );
}