'use client';

import { But<PERSON> } from '@/components/ui/button';
import { PanelLeftClose } from 'lucide-react';
import { useSidebar } from '@/contexts/SidebarContext';

interface PageHeaderProps {
  title: string;
  description?: string;
  buttonText?: string;
  onButtonClick?: () => void;
  actions?: React.ReactNode;
}

export function PageHeader({ title, description, buttonText, onButtonClick, actions }: PageHeaderProps) {
  const { toggleCollapse } = useSidebar();

  return (
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-4">
          <Button variant="ghost" size="icon" onClick={toggleCollapse}>
            <PanelLeftClose className="h-6 w-6" />
          </Button>
          <div className="h-6 w-px bg-border" /> {/* Separador Vertical */}
          <div>
            <h1 className="text-lg font-semibold">{title}</h1>
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          {actions}
          {buttonText && onButtonClick && (
            <Button onClick={onButtonClick}>
              {buttonText}
            </Button>
          )}
        </div>
      </div>
      <hr className="my-4" /> {/* Divider */}
    </>
  );
}