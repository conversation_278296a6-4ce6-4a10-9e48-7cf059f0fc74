import React, { useEffect, useState } from 'react';
import { Sheet, Sheet<PERSON>ontent, SheetTitle } from '@/components/ui/sheet';
import { useParcelasService, ParcelaComDetalhes } from '@/services/parcelas';
import { Copy, Edit } from 'lucide-react';
import { toast } from 'sonner';
import { uploadAnexo } from '@/lib/supabase';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import * as Switch from '@radix-ui/react-switch';
import { Dropzone } from '@/components/ui/dropzone';
import { DatePicker } from '@/components/ui/date-picker';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogTrigger, DialogContent, DialogTitle } from '@/components/ui/dialog';
import QRCode from 'react-qr-code';
import { DrawerContato } from '@/app/(auth)/contatos/components/drawer-contato';
import { createStaticPix, hasError } from 'pix-utils';

interface DrawerPagamentosPendentesProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Tipo para agrupamento de grupo pendente
interface GrupoPendente {
  fornecedorId: string;
  fornecedorNome: string;
  projetoId: string;
  projetoNome: string;
  parcelas: ParcelaComDetalhes[];
  valorTotal: number;
  chavePix?: string;
}

function formatCurrency(value?: number | null): string {
  if (value === undefined || value === null) return 'R$ 0,00';
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
}

function formatDate(dateString?: string | null): string {
  if (!dateString) return '-';
  try {
    // Usando o formato com apenas 2 dígitos para o ano
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: '2-digit'
    });
  } catch {
    return dateString || '-';
  }
}

export function DrawerPagamentosPendentes({ open, onOpenChange }: DrawerPagamentosPendentesProps) {
  const [grupos, setGrupos] = useState<GrupoPendente[]>([]);
  const [loading, setLoading] = useState(false);
  const [erro, setErro] = useState<string | null>(null);
  const [grupoAtivoIdx, setGrupoAtivoIdx] = useState(0);
  const parcelasService = useParcelasService();
  const [copied, setCopied] = useState(false);
  const [usarMesmoAnexo, setUsarMesmoAnexo] = useState(true);
  const [anexosGrupo, setAnexosGrupo] = useState<File[]>([]);
  const [anexosIndividuais, setAnexosIndividuais] = useState<Record<string, File[]>>({});
  const [registrando, setRegistrando] = useState(false);
  const [finalizado, setFinalizado] = useState(false);
  const [dataPagamento, setDataPagamento] = useState<Date>(new Date());
  const [comentario, setComentario] = useState('');
  const [qrOpen, setQrOpen] = useState(false);
  const [drawerContatoOpen, setDrawerContatoOpen] = useState(false);

  // Buscar e agrupar parcelas pendentes ao abrir o drawer
  useEffect(() => {
    if (!open) return;
    carregarParcelas();
  }, [open]);

  const carregarParcelas = async () => {
    setLoading(true);
    setErro(null);
    setGrupos([]);
    setGrupoAtivoIdx(0);
    setAnexosGrupo([]);
    setAnexosIndividuais({});

    try {
      // Buscar parcelas pendentes com informações de contato e obra em uma única consulta
      const { data: parcelasData, error } = await supabase
        .from('parcelas')
        .select(`
          id,
          lancamento_id,
          numero,
          valor,
          vencimento,
          status,
          data_pagamento,
          created_at,
          updated_at,
          anexos,
          lancamentos:lancamento_id (
            id,
            descricao,
            valor_total,
            data_competencia,
            forma_pagamento,
            tipo_lancamento,
            status,
            contato_id,
            obra_id,
            contatos:contato_id (
              id,
              nome_empresa,
              nome_razao,
              nome_contato,
              chave_pix
            ),
            obras:obra_id (
              id,
              nome
            )
          )
        `)
        .eq('status', 'pendente')
        .order('vencimento', { ascending: true });

      if (error) {
        
        setErro("Erro ao buscar parcelas pendentes");
        setLoading(false);
        return;
      }

      if (!parcelasData || parcelasData.length === 0) {
        setLoading(false);
        return;
      }

      // Agrupar por fornecedor + projeto
      const gruposMap = new Map<string, GrupoPendente>();
      for (const parcela of parcelasData) {
        // Obter IDs e nomes do fornecedor e projeto
        const fornecedorId = parcela.lancamentos?.contato_id || 'desconhecido';

        // Obter nome do fornecedor diretamente dos dados da consulta
        let fornecedorNome = 'Fornecedor desconhecido';
        if (parcela.lancamentos?.contatos) {
          const contato = parcela.lancamentos.contatos;
          fornecedorNome = contato.nome_empresa || contato.nome_razao || contato.nome_contato || 'Fornecedor desconhecido';
        }

        const projetoId = parcela.lancamentos?.obra_id || 'desconhecido';
        const projetoNome = parcela.lancamentos?.obras?.nome || 'Projeto desconhecido';
        const grupoKey = fornecedorId + '||' + projetoId;

        if (!gruposMap.has(grupoKey)) {
          // Obter a chave PIX do contato, se disponível
          const chavePix = parcela.lancamentos?.contatos?.chave_pix || '';

          gruposMap.set(grupoKey, {
            fornecedorId,
            fornecedorNome,
            projetoId,
            projetoNome,
            parcelas: [],
            valorTotal: 0,
            chavePix
          });
        }
        const grupo = gruposMap.get(grupoKey)!;
        grupo.parcelas.push(parcela);
        grupo.valorTotal += parcela.valor || 0;
      }

      setGrupos(Array.from(gruposMap.values()));
    } catch (e) {
      setErro('Erro ao buscar parcelas pendentes');
    } finally {
      setLoading(false);
    }
  };

  const grupoAtivo = grupos[grupoAtivoIdx];

  // A chave PIX já está disponível no objeto grupoAtivo
  const chavePix = grupoAtivo?.chavePix || '';

  // Garantir que não tentamos buscar um contato com ID "desconhecido"
  const contatoId = grupoAtivo?.fornecedorId && grupoAtivo.fornecedorId !== 'desconhecido' ? grupoAtivo.fornecedorId : null;

  const handleCopyPix = () => {
    if (!chavePix) return;
    navigator.clipboard.writeText(chavePix);
    setCopied(true);
    setTimeout(() => setCopied(false), 1200);
  };

  // Handlers para upload de anexos
  const handleDropGrupo = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setAnexosGrupo(Array.from(files as FileList));
    }
  };
  const handleDropIndividual = (parcelaId: string, e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setAnexosIndividuais(prev => ({ ...prev, [parcelaId]: Array.from(files as FileList) }));
    }
  };

  // Handler para registrar grupo e avançar
  const handleRegistrarGrupo = async () => {
    if (!grupoAtivo) return;
    setRegistrando(true);
    try {
      let anexosParaSalvar: Record<string, any[]> = {};
      if (usarMesmoAnexo) {
        // Upload real dos arquivos do grupo para cada parcela
        const uploads = await Promise.all(anexosGrupo.map(file => uploadAnexo(file, `grupo-${grupoAtivoIdx + 1}`)));
        grupoAtivo.parcelas.forEach(parcela => {
          anexosParaSalvar[parcela.id] = anexosGrupo.map((file, idx) => ({
            url: uploads[idx],
            tipo: 'comprovante',
            nome: file.name,
          }));
        });
      } else {
        // Upload real dos arquivos individuais por parcela
        for (const parcela of grupoAtivo.parcelas) {
          const files = anexosIndividuais[parcela.id] || [];
          if (files.length > 0) {
            const uploads = await Promise.all(files.map(file => uploadAnexo(file, `parcela-${parcela.id}`)));
            anexosParaSalvar[parcela.id] = files.map((file, idx) => ({
              url: uploads[idx],
              tipo: 'comprovante',
              nome: file.name,
            }));
          } else if (Array.isArray(parcela.anexos) && parcela.anexos.length > 0) {
            // Mantém os anexos existentes se não houver novos uploads
            anexosParaSalvar[parcela.id] = parcela.anexos;
          }
        }
      }
      // Atualizar status das parcelas para 'pago' e salvar anexos
      await Promise.all(
        grupoAtivo.parcelas.map(parcela =>
          parcelasService.updateParcelaStatus(parcela.id, 'pago', dataPagamento.toISOString())
            .then(async () => {
              // Atualizar campo anexos no banco
              await supabase
                .from('parcelas')
                .update({
                  anexos: anexosParaSalvar[parcela.id],
                  data_pagamento: dataPagamento.toISOString(),
                  observacao: comentario || null
                })
                .eq('id', parcela.id);
            })
        )
      );

      toast.success('Grupo registrado com sucesso!');

      // Recarregar a lista após salvar
      carregarParcelas();

      if (grupoAtivoIdx < grupos.length - 1) {
        setGrupoAtivoIdx(grupoAtivoIdx + 1);
        setAnexosGrupo([]);
        setAnexosIndividuais({});
        setComentario('');
      } else {
        setFinalizado(true);
        setTimeout(() => {
          onOpenChange(false);
          setFinalizado(false);
        }, 1800);
      }
    } catch (e) {
      toast.error('Erro ao registrar grupo.');
    } finally {
      setRegistrando(false);
    }
  };

  // Handler para pular grupo
  const handlePularGrupo = () => {
    if (grupoAtivoIdx < grupos.length - 1) {
      setGrupoAtivoIdx(grupoAtivoIdx + 1);
      setAnexosGrupo([]);
      setAnexosIndividuais({});
    } else {
      setFinalizado(true);
      setTimeout(() => {
        onOpenChange(false);
        setFinalizado(false);
      }, 1800);
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-full p-0 h-screen" style={{ maxWidth: 1100 }}>
        <div className="p-4 px-6 border-b flex justify-between items-center">
          <SheetTitle className="text-xl font-semibold p-0 m-0">Pagamentos Pendentes</SheetTitle>
          <button
            type="button"
            onClick={() => onOpenChange(false)}
            className="rounded-full h-8 w-8 flex items-center justify-center hover:bg-gray-100"
            aria-label="Fechar"
          >
            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
            </svg>
          </button>
        </div>
        <div className="flex h-full">
          {/* Coluna esquerda: Fila de grupos (30%) */}
          <div className="w-[30%] border-r p-0 overflow-y-auto h-full bg-gray-50">
            <div className="p-4 pb-3">
              <div className="font-semibold text-gray-700">
                Grupos Pendentes {grupos.length > 0 && (
                  <span className="ml-1 text-gray-500 font-normal">({grupos.length})</span>
                )}
              </div>
            </div>
            {loading && <div className="px-4 text-gray-500">Carregando...</div>}
            {erro && <div className="px-4 text-red-500">{erro}</div>}
            {!loading && !erro && grupos.length === 0 && <div className="px-4 text-gray-500">Nenhum grupo pendente</div>}
            <div className="flex flex-col">
              {grupos.map((grupo, idx) => (
                <button
                  type="button"
                  key={grupo.fornecedorId + grupo.projetoId}
                  className={`text-left px-4 py-3 border-0 transition-colors ${
                    idx === grupoAtivoIdx
                      ? 'bg-white border-l-4 border-l-blue-500 shadow-sm'
                      : 'bg-gray-50 hover:bg-gray-100 border-b border-b-gray-200'
                  }`}
                  onClick={() => setGrupoAtivoIdx(idx)}
                >
                  <div className={`text-base leading-snug ${idx === grupoAtivoIdx ? 'font-medium text-gray-800' : 'font-normal text-gray-700'}`}>
                    {grupo.fornecedorNome}
                  </div>
                  <div className="text-xs text-gray-500 mt-0.5">{grupo.projetoNome}</div>
                  <div className="text-xs text-gray-500 mt-2">{grupo.parcelas.length} pagamento{grupo.parcelas.length > 1 ? 's' : ''}</div>
                  <div className={`text-sm mt-1 ${idx === grupoAtivoIdx ? 'font-semibold text-blue-600' : 'font-semibold text-gray-700'}`}>
                    {formatCurrency(grupo.valorTotal)}
                  </div>
                </button>
              ))}
            </div>
          </div>
          {/* Coluna direita: Detalhes do grupo ativo (70%) */}
          <div className="w-[70%] flex flex-col h-full">
            <div className="flex-1 p-6 overflow-y-auto">
            {grupoAtivo ? (
              <>
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <div className="text-sm text-gray-500 font-normal mb-1">
                      Grupo {grupoAtivoIdx + 1} de {grupos.length}
                    </div>
                    <div className="flex items-center gap-2 text-xl font-medium text-gray-800 uppercase">
                      {grupoAtivo.fornecedorNome}
                      {contatoId && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7"
                          onClick={() => setDrawerContatoOpen(true)}
                          aria-label="Editar fornecedor"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}
                      <span className="text-gray-400 mx-1">-</span> {grupoAtivo.projetoNome}
                    </div>
                  </div>
                </div>
                {/* Chave PIX */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-sm font-medium text-gray-700">Chave PIX</div>
                    <div className="flex gap-1">
                      <button
                        type="button"
                        className="h-7 w-7 p-0 border border-gray-200 rounded flex items-center justify-center text-gray-700 hover:bg-gray-50"
                        onClick={handleCopyPix}
                        disabled={!chavePix}
                        title="Copiar chave PIX"
                        aria-label="Copiar chave PIX"
                      >
                        <Copy size={14} />
                      </button>
                      <Dialog open={qrOpen} onOpenChange={setQrOpen}>
                        <DialogTrigger asChild>
                          <button
                            type="button"
                            className="h-7 w-7 p-0 border border-gray-200 rounded flex items-center justify-center text-gray-700 hover:bg-gray-50"
                            disabled={!chavePix}
                            title="Mostrar QR Code"
                            aria-label="Mostrar QR Code"
                          >
                            <span className="text-xs font-medium">QR</span>
                          </button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogTitle>QR Code da Chave PIX</DialogTitle>
                          <div className="flex flex-col items-center gap-4 py-4">
                            {/* Geração do payload EMV PIX */}
                            {(() => {
                              if (!chavePix) return <span>Chave PIX não disponível</span>;
                              // Nome e cidade do recebedor (requisitos do padrão)
                              // Garantir que o nome não tenha caracteres especiais e esteja limitado a 25 caracteres
                              const nome = (grupoAtivo?.fornecedorNome || 'RECEBEDOR').toUpperCase().normalize('NFD').replace(/[^\w\s]/gi, '').substring(0, 25);
                              const cidade = 'SAO PAULO'; // Pode ser dinâmico se disponível
                              const pix = createStaticPix({
                                merchantName: nome,
                                merchantCity: cidade,
                                pixKey: chavePix,
                                infoAdicional: 'Pagamento via QR',
                                transactionAmount: 0
                              });
                              if (hasError(pix)) return <span>Erro ao gerar QR Code PIX</span>;
                              const brCode = pix.toBRCode();
                              return <>
                                <QRCode value={brCode} size={180} />
                                <div className="text-xs break-all max-w-xs text-center">{brCode}</div>
                              </>;
                            })()}
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                  <div className="w-full bg-gray-50 rounded border border-gray-200 px-4 py-4">
                    {chavePix ? (
                      <div className="text-sm break-all text-gray-700">{chavePix}</div>
                    ) : (
                      <div className="text-sm text-gray-500">(Fornecedor sem chave PIX cadastrada)</div>
                    )}
                  </div>
                </div>
                {/* Título de Parcelas do Grupo com toggle */}
                <div className="flex items-center justify-between mb-2">
                  <div className="text-sm font-medium text-gray-700">Parcelas do Grupo</div>
                  <div className="flex items-center gap-2">
                    <label htmlFor="usar-mesmo-anexo-switch" className="text-xs text-gray-600 cursor-pointer select-none">
                      Usar mesmo comprovante
                    </label>
                    <div className="relative inline-block">
                      <Switch.Root
                        checked={usarMesmoAnexo}
                        onCheckedChange={setUsarMesmoAnexo}
                        className="w-11 h-6 bg-gray-200 rounded-full relative data-[state=checked]:bg-blue-500 outline-none transition-colors duration-200"
                        id="usar-mesmo-anexo-switch"
                      >
                        <Switch.Thumb
                          className="block w-5 h-5 bg-white rounded-full shadow transition-transform duration-200 translate-x-0.5 data-[state=checked]:translate-x-5"
                        />
                      </Switch.Root>
                    </div>
                  </div>
                </div>
                {/* Tabela de parcelas */}
                <div className="rounded-md overflow-hidden mb-6 border border-gray-200">
                  <table className="w-full text-sm border-collapse">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="text-left px-4 py-3 font-medium text-gray-600 border-b border-gray-200">VENCIMENTO</th>
                        <th className="text-right px-4 py-3 font-medium text-gray-600 border-b border-gray-200">VALOR</th>
                        {!usarMesmoAnexo && (
                          <th className="text-center px-4 py-3 font-medium text-gray-600 w-56 border-b border-gray-200">Comprovante</th>
                        )}
                      </tr>
                    </thead>
                    <tbody>
                      {grupoAtivo.parcelas.map((parcela, index) => (
                        <tr
                          key={parcela.id}
                          className="hover:bg-gray-50 border-b border-gray-200"
                        >
                          <td className="px-4 py-3 text-gray-700">{formatDate(parcela.vencimento)}</td>
                          <td className="px-4 py-3 text-right font-medium text-gray-800">{formatCurrency(parcela.valor)}</td>
                          {!usarMesmoAnexo && (
                            <td className="px-4 py-3 text-center align-middle">
                              <label className="inline-flex items-center gap-2 cursor-pointer">
                                <Button
                                  type="button"
                                  variant="outline"
                                  className="h-8 px-3 py-1 text-xs flex items-center gap-2 border-gray-200"
                                  onClick={() => document.getElementById(`file-input-${parcela.id}`)?.click()}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M7 10l5-5m0 0l5 5m-5-5v12" /></svg>
                                  <span>Upload</span>
                                </Button>
                                <input
                                  id={`file-input-${parcela.id}`}
                                  type="file"
                                  multiple
                                  className="hidden"
                                  onChange={e => handleDropIndividual(parcela.id, e)}
                                  aria-label={`Anexar arquivos à parcela ${formatDate(parcela.vencimento)} - ${formatCurrency(parcela.valor)}`}
                                />
                                <span className="text-xs text-gray-500">
                                  {(anexosIndividuais[parcela.id] || []).map((file, idx) => file.name).join(', ')}
                                </span>
                              </label>
                              {/* Exibir anexos já salvos */}
                              {Array.isArray(parcela.anexos) && parcela.anexos.length > 0 && (
                                <div className="mt-2 flex flex-wrap gap-2 justify-center">
                                  {parcela.anexos.map((anexo: any, idx: number) => (
                                    <a
                                      key={idx}
                                      href={anexo.url}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="px-3 py-1 bg-green-100 rounded-full text-xs text-green-700"
                                      title={anexo.nome}
                                    >
                                      {anexo.nome || 'Comprovante'}
                                    </a>
                                  ))}
                                </div>
                              )}
                            </td>
                          )}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                {/* Área de dropzone para upload de comprovante */}
                {usarMesmoAnexo && (
                  <div className="mb-8">
                    <div className="text-sm font-medium mb-2 text-gray-700">Comprovante do Grupo</div>
                    <div className="relative">
                      <div className="border border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center py-6 px-4 bg-gray-50">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-400 mb-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M7 10l5-5 5 5M12 5v9M5 19h14"/>
                        </svg>
                        <div className="text-sm text-gray-500 text-center">
                          Clique ou arraste o arquivo aqui
                        </div>
                      </div>
                      <Dropzone
                        onDrop={files => setAnexosGrupo(files)}
                        multiple
                        className="absolute inset-0 opacity-0 cursor-pointer"
                      />
                    </div>

                    {anexosGrupo.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-2">
                        {anexosGrupo.map((file, idx) => (
                          <span key={idx} className="px-3 py-1 bg-gray-100 rounded-full text-xs text-gray-700">
                            {file.name}
                          </span>
                        ))}
                      </div>
                    )}

                    {/* Exibir anexos do grupo (primeira parcela como referência) */}
                    {Array.isArray(grupoAtivo.parcelas[0]?.anexos) && grupoAtivo.parcelas[0].anexos.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-2">
                        {grupoAtivo.parcelas[0].anexos.map((anexo: any, idx: number) => (
                          <a
                            key={idx}
                            href={anexo.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="px-3 py-1 bg-green-100 rounded-full text-xs text-green-700"
                            title={anexo.nome}
                          >
                            {anexo.nome || 'Comprovante'}
                          </a>
                        ))}
                      </div>
                    )}
                  </div>
                )}
                {/* Campos de data e comentário */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
                  <div>
                    <div className="text-sm font-medium mb-2 text-gray-700">Data do Pagamento</div>
                    <DatePicker
                      date={dataPagamento}
                      setDate={(date) => date !== undefined ? setDataPagamento(date) : null}
                      inputClassName="border-gray-200 rounded w-full h-10 text-sm mb-2"
                      placeholder="DD/MM/AAAA"
                    />
                  </div>
                  <div>
                    <div className="text-sm font-medium mb-2 text-gray-700">Comentário (Opcional)</div>
                    <Textarea
                      value={comentario}
                      onChange={e => setComentario(e.target.value)}
                      placeholder="Adicione observações sobre este grupo de pagamentos..."
                      className="resize-none border-gray-200 rounded w-full min-h-[100px] text-sm mb-2"
                      rows={4}
                    />
                  </div>
                </div>
                {/* Mensagem final */}
                {finalizado && (
                  <div className="mt-8 text-center text-lg font-semibold text-primary">
                    Todos os pagamentos concluídos!
                  </div>
                )}
              </>
            ) : (
              <div className="text-muted-foreground">(Detalhes do grupo ativo)</div>
            )}
            </div>

            {/* Footer fixo com botões de ação */}
            {grupoAtivo && (
              <div className="border-t bg-white p-6 flex gap-4 shadow-sm sticky bottom-0 z-10">
                <Button
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium border-0 h-10 rounded"
                  onClick={handleRegistrarGrupo}
                  disabled={registrando || finalizado}
                  type="button"
                >
                  {registrando ? 'Registrando...' : 'Registrar Grupo e Próximo'}
                </Button>
                <Button
                  className="flex-1 border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 h-10 rounded"
                  onClick={handlePularGrupo}
                  disabled={registrando || finalizado}
                  type="button"
                  variant="outline"
                >
                  Pular Grupo
                </Button>
              </div>
            )}
          </div>
        </div>
        <DrawerContato
          open={drawerContatoOpen}
          onOpenChange={setDrawerContatoOpen}
          contatoId={contatoId}
          onSuccess={async () => {
            setDrawerContatoOpen(false);
            carregarParcelas();
          }}
        />
      </SheetContent>
    </Sheet>
  );
}