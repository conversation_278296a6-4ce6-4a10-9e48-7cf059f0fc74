import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Define classes para componentes de sobreposição (popover, dropdown, etc.)
 * Garante que os componentes tenham fundos sólidos e apareçam na frente
 * de outros elementos, especialmente quando aninhados.
 *
 * @param variant - O tipo de componente ou localização
 * @returns - String com as classes Tailwind para aplicar ao componente
 */
export function overlayStyles(variant: 'default' | 'nested' | 'inside-sheet' = 'default') {
  // Base styles para todos os componentes de sobreposição
  const baseStyles = "bg-background text-foreground";

  // Adicionar z-index apropriado baseado no contexto
  switch (variant) {
    case 'nested':
      // Para componentes aninhados dentro de outros overlays
      return `${baseStyles} z-[100]`;
    case 'inside-sheet':
      // Para componentes dentro de Sheet ou Dialog, que precisam de z-index mais alto
      return `${baseStyles} z-[200]`;
    default:
      // Para componentes standalone padrão
      return `${baseStyles} z-50`;
  }
}

/**
 * Extrai o path após '/anexos/' na URL de um anexo
 * @param url URL completa do anexo
 * @returns O path extraído ou string vazia se não encontrar o padrão
 */
export function extrairPathDeUrl(url: string): string {
  // Extrai o path após '/anexos/' na URL
  const match = url.match(/anexos\/(.+?)(\?|$)/);
  return match ? match[1] : '';
}

/**
 * Formata um valor numérico para o formato de moeda brasileira (R$)
 * @param value Valor numérico a ser formatado
 * @returns String formatada no padrão de moeda brasileira
 */
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
}

/**
 * Formata uma data para o formato brasileiro (DD/MM/YYYY)
 * @param date Data a ser formatada
 * @returns String formatada no padrão brasileiro
 */
export function formatDate(date: Date): string {
  try {
    // Garantir que a data seja meio-dia para evitar problemas de timezone
    const adjustedDate = new Date(date);
    adjustedDate.setHours(12, 0, 0, 0);
    return new Intl.DateTimeFormat('pt-BR').format(adjustedDate);
  } catch (error) {
    
    return new Intl.DateTimeFormat('pt-BR').format(date);
  }
}