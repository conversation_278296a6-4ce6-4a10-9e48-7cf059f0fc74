import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';
import { cookies } from 'next/headers';
import { createServiceClient } from '@/lib/supabase/service-client';
import { formatDateToISOString } from '@/lib/date-utils';

// POST /api/documentos/[id]/aprovar - Aprovar um documento e criar lançamento
export const dynamic = 'force-dynamic';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Acessar params.id de forma assíncrona
    const resolvedParams = await params;
    const documentoId = resolvedParams.id;

    if (!documentoId) {
      return NextResponse.json(
        { error: 'ID do documento não fornecido' },
        { status: 400 }
      );
    }

    // Inicializar o cliente do Supabase
    const supabase = await getSupabaseRouteClient();

    // Verificar a sessão - Padrão recomendado para Route Handlers
    let session;

    try {
      const { data } = await supabase.auth.getSession();
      session = data.session;

      if (!session) {
        console.log('[DEBUG API] Sessão não encontrada, tentando alternativa...');
        // Tentar obter o usuário como alternativa
        const { data: userData } = await supabase.auth.getUser();
        if (!userData.user) {
          return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }
      }
    } catch (authError) {
      console.error('[DEBUG API] Erro ao verificar autenticação:', authError);
      return NextResponse.json({ error: 'Erro de autenticação' }, { status: 401 });
    }

    // Obter o body da requisição com os dados do lançamento
    const dadosLancamento = await request.json();

    // Criar cliente de serviço para contornar RLS
    const serviceClient = createServiceClient();
    if (!serviceClient) {
      return NextResponse.json({ error: 'Erro ao criar cliente de serviço para aprovação' }, { status: 500 });
    }

    // Primeiro, obter o documento para ter acesso ao user_id e outros dados
    const { data: docData, error: docError } = await serviceClient
      .from('documentos')
      .select('user_id, arquivo_url, arquivo_path, nome, tipo_arquivo')
      .eq('id', documentoId)
      .single();

    if (docError) {
      console.error('[API APROVAR] Erro ao obter documento para aprovação:', docError);
      return NextResponse.json({ error: docError.message }, { status: 500 });
    }

    // Extrair as parcelas do objeto de lançamento antes de inserir
    let parcelas = dadosLancamento.parcelas || [];

    // Remover o campo parcelas do objeto de lançamento para evitar o erro
    const { parcelas: _, ...lancamentoSemParcelas } = dadosLancamento;

    // Garantir que a data de competência esteja no formato correto
    const dataCompetencia = lancamentoSemParcelas.data_competencia;
    let dataCompetenciaFormatada = dataCompetencia;

    // Se a data for um objeto Date, formatá-la para string ISO
    if (dataCompetencia instanceof Date) {
      dataCompetenciaFormatada = formatDateToISOString(dataCompetencia) || dataCompetencia;
    }

    // Adicionar o user_id do documento ao lançamento e garantir formato correto da data
    const lancamentoComUserId = {
      ...lancamentoSemParcelas,
      data_competencia: dataCompetenciaFormatada,
      user_id: docData.user_id
    };

    console.log('[API APROVAR] Criando lançamento com user_id:', docData.user_id);

    // Determinar o número de parcelas
    const numeroParcelas = parcelas.length > 0 ? parcelas.length : 1;

    console.log('[API APROVAR] Criando lançamento com', numeroParcelas, 'parcela(s)');

    // Preparar arrays de datas de vencimento e valores para a função RPC
    let datasVencimento = null;
    let valoresParcelas = null;

    if (parcelas.length > 0) {
      // Processar as parcelas para garantir formato correto das datas
      parcelas = parcelas.map((p, index) => {
        // Garantir que a data de vencimento esteja no formato correto
        let vencimento = p.vencimento;

        // Se a data for um objeto Date, formatá-la para string ISO
        if (vencimento instanceof Date) {
          vencimento = formatDateToISOString(vencimento) || vencimento;
        }

        return {
          ...p,
          numero: p.numero || index + 1,
          vencimento: vencimento
        };
      });

      // Mapear diretamente os valores e datas das parcelas
      datasVencimento = parcelas.map(p => p.vencimento);
      valoresParcelas = parcelas.map(p => p.valor);

      console.log('[API APROVAR] Número de parcelas recebidas:', parcelas.length);
      console.log('[API APROVAR] Parcelas processadas:', JSON.stringify(parcelas));
      console.log('[API APROVAR] Datas de vencimento:', datasVencimento);
      console.log('[API APROVAR] Valores das parcelas:', valoresParcelas);
    }

    // Escolher a função RPC apropriada com base na presença de parcelas personalizadas
    let rpcResult, rpcError;

    if (parcelas.length > 0) {
      // Usar a nova função que aceita um array JSON de parcelas
      console.log('[API APROVAR] Usando função RPC com array JSON de parcelas');
      console.log('[API APROVAR] Número de parcelas enviadas para RPC:', parcelas.length);

      const rpcParams = {
        p_descricao: lancamentoComUserId.descricao,
        p_valor_total: lancamentoComUserId.valor_total,
        p_data_competencia: lancamentoComUserId.data_competencia,
        p_forma_pagamento: lancamentoComUserId.forma_pagamento,
        p_tipo_lancamento: lancamentoComUserId.tipo_lancamento,
        p_status: lancamentoComUserId.status,
        p_observacoes: lancamentoComUserId.observacoes || '',
        p_obra_id: lancamentoComUserId.obra_id || null,
        p_contato_id: lancamentoComUserId.contato_id || null,
        p_categoria_id: lancamentoComUserId.categoria_id || null,
        p_parcelas: parcelas
      };

      console.log('[API APROVAR] Parâmetros RPC:', JSON.stringify(rpcParams));

      const rpcResponse = await serviceClient.rpc(
        'criar_lancamento_com_parcelas_array',
        rpcParams
      );

      rpcResult = rpcResponse.data;
      rpcError = rpcResponse.error;

      if (rpcError) {
        console.error('[API APROVAR] Erro na resposta RPC:', rpcError);
      } else {
        console.log('[API APROVAR] Resposta RPC bem-sucedida:', rpcResult);
      }
    } else {
      // Usar a função simplificada para parcelas automáticas
      console.log('[API APROVAR] Usando função RPC simplificada');
      const rpcResponse = await serviceClient.rpc(
        'criar_lancamento_com_parcelas_simples',
        {
          p_descricao: lancamentoComUserId.descricao,
          p_valor_total: lancamentoComUserId.valor_total,
          p_data_competencia: lancamentoComUserId.data_competencia,
          p_forma_pagamento: lancamentoComUserId.forma_pagamento,
          p_tipo_lancamento: lancamentoComUserId.tipo_lancamento,
          p_status: lancamentoComUserId.status,
          p_observacoes: lancamentoComUserId.observacoes || '',
          p_obra_id: lancamentoComUserId.obra_id || null,
          p_contato_id: lancamentoComUserId.contato_id || null,
          p_categoria_id: lancamentoComUserId.categoria_id || null,
          p_numero_parcelas: numeroParcelas,
          p_user_id: docData.user_id
        }
      );
      rpcResult = rpcResponse.data;
      rpcError = rpcResponse.error;
    }

    if (rpcError) {
      console.error('[API APROVAR] Erro ao criar lançamento via RPC:', rpcError);

      // Tentar método alternativo com insert direto
      console.log('[API APROVAR] Tentando método alternativo com insert direto');

      // Primeiro, criar o lançamento sem as parcelas usando o cliente de serviço
      const { data: lancamento, error: lancamentoError } = await serviceClient
        .from('lancamentos')
        .insert(lancamentoComUserId)
        .select()
        .single();

      // Verificar se houve erro ao criar o lançamento
      if (lancamentoError) {
        console.error('[API APROVAR] Erro ao criar lançamento:', lancamentoError);
        return NextResponse.json(
          { error: 'Erro ao criar lançamento: ' + lancamentoError.message },
          { status: 500 }
        );
      }

      // Se chegou aqui, o método alternativo funcionou
      console.log('[API APROVAR] Método alternativo funcionou, criando parcelas');

      // Depois, criar as parcelas associadas ao lançamento usando o cliente de serviço
      if (parcelas.length > 0) {
        try {
          console.log('[API APROVAR] Método alternativo: Número de parcelas recebidas:', parcelas.length);
          console.log('[API APROVAR] Método alternativo: Parcelas recebidas:', JSON.stringify(parcelas));

          // Preparar as parcelas com o ID do lançamento
          // Garantir que todas as parcelas sejam criadas corretamente
          const parcelasComLancamentoId = parcelas.map((parcela) => {
            // Garantir que a data de vencimento esteja no formato correto
            let vencimento = parcela.vencimento;

            // Se a data for um objeto Date, formatá-la para string ISO
            if (vencimento instanceof Date) {
              vencimento = formatDateToISOString(vencimento) || vencimento;
            }

            return {
              lancamento_id: lancamento.id,
              numero: parcela.numero,
              valor: parcela.valor,
              vencimento: vencimento,
              status: 'pendente'
            };
          });

          console.log('[API APROVAR] Método alternativo: Número de parcelas a serem criadas:', parcelasComLancamentoId.length);
          console.log('[API APROVAR] Método alternativo: Parcelas processadas:', JSON.stringify(parcelasComLancamentoId));

          // Inserir todas as parcelas de uma vez
          const { data: parcelasInseridas, error: parcelasError } = await serviceClient
            .from('parcelas')
            .insert(parcelasComLancamentoId)
            .select();

          if (parcelasError) {
            console.error('[API APROVAR] Erro ao inserir parcelas em lote:', parcelasError);

            // Fallback: inserir uma a uma se a inserção em lote falhar
            console.log('[API APROVAR] Tentando inserir parcelas uma a uma como fallback');
            const parcelasInseridasIndividualmente = [];

            for (const parcela of parcelasComLancamentoId) {
              console.log(`[API APROVAR] Inserindo parcela ${parcela.numero} individualmente:`, JSON.stringify(parcela));

              const { data: parcelaInserida, error: parcelaError } = await serviceClient
                .from('parcelas')
                .insert(parcela)
                .select();

              if (parcelaError) {
                console.error(`[API APROVAR] Erro ao criar parcela ${parcela.numero}:`, parcelaError);
                throw parcelaError;
              } else {
                console.log(`[API APROVAR] Parcela ${parcela.numero} inserida com sucesso:`, parcelaInserida);
                parcelasInseridasIndividualmente.push(parcelaInserida);
              }
            }

            console.log('[API APROVAR] Todas as parcelas inseridas individualmente com sucesso');
            console.log('[API APROVAR] Total de parcelas inseridas individualmente:', parcelasInseridasIndividualmente.length);
          } else {
            console.log('[API APROVAR] Todas as parcelas inseridas em lote com sucesso');
            console.log('[API APROVAR] Total de parcelas inseridas em lote:', parcelasInseridas.length);
            console.log('[API APROVAR] Parcelas inseridas:', parcelasInseridas);
          }
        } catch (parcelasError) {
          console.error('[API APROVAR] Erro ao criar parcelas:', parcelasError);
          // Mesmo com erro nas parcelas, continuamos para atualizar o documento
          // mas registramos o erro para diagnóstico
        }
      } else {
        // Criar uma única parcela com o valor total
        // Garantir que a data de vencimento esteja no formato correto
        let vencimento = lancamentoComUserId.data_competencia;

        // Se a data for um objeto Date, formatá-la para string ISO
        if (vencimento instanceof Date) {
          vencimento = formatDateToISOString(vencimento) || vencimento;
        }

        const parcelaUnica = {
          lancamento_id: lancamento.id,
          numero: 1,
          valor: lancamentoComUserId.valor_total,
          vencimento: vencimento,
          status: 'pendente'
        };

        console.log('[API APROVAR] Inserindo parcela única:', parcelaUnica);

        const { error: parcelaError } = await serviceClient
          .from('parcelas')
          .insert(parcelaUnica);

        if (parcelaError) {
          console.error('[API APROVAR] Erro ao criar parcela única:', parcelaError);
        }
      }

      // Buscar anexos adicionais do documento
      const { data: anexos, error: anexosError } = await serviceClient
        .from('documento_anexos')
        .select('*')
        .eq('documento_id', documentoId);

      if (anexosError) {
        console.error('[API APROVAR] Erro ao buscar anexos do documento:', anexosError);
        // Continuar mesmo com erro nos anexos
      } else if (anexos && anexos.length > 0) {
        console.log('[API APROVAR] Anexos encontrados:', anexos.length);

        // Criar anexos para o lançamento
        try {
          // Primeiro, criar o anexo principal do documento
          const anexoPrincipal = {
            lancamento_id: lancamento.id,
            nome: docData.nome,
            arquivo_url: docData.arquivo_url,
            arquivo_path: docData.arquivo_path,
            tipo_arquivo: docData.tipo_arquivo,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          // Inserir o anexo principal
          const { error: anexoPrincipalError } = await serviceClient
            .from('lancamento_anexos')
            .insert(anexoPrincipal);

          if (anexoPrincipalError) {
            console.error('[API APROVAR] Erro ao criar anexo principal:', anexoPrincipalError);
          } else {
            console.log('[API APROVAR] Anexo principal criado com sucesso');
          }

          // Preparar os anexos adicionais para inserção
          const lancamentoAnexos = anexos.map(anexo => ({
            lancamento_id: lancamento.id,
            nome: anexo.nome,
            arquivo_url: anexo.arquivo_url,
            arquivo_path: anexo.arquivo_path,
            tipo_arquivo: anexo.tipo_arquivo,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }));

          // Inserir os anexos adicionais
          const { error: anexosLancamentoError } = await serviceClient
            .from('lancamento_anexos')
            .insert(lancamentoAnexos);

          if (anexosLancamentoError) {
            console.error('[API APROVAR] Erro ao criar anexos adicionais:', anexosLancamentoError);
          } else {
            console.log('[API APROVAR] Anexos adicionais criados com sucesso:', lancamentoAnexos.length);
          }
        } catch (anexosError) {
          console.error('[API APROVAR] Erro ao processar anexos:', anexosError);
          // Continuar mesmo com erro nos anexos
        }
      } else {
        // Criar apenas o anexo principal do documento
        try {
          const anexoPrincipal = {
            lancamento_id: lancamento.id,
            nome: docData.nome,
            arquivo_url: docData.arquivo_url,
            arquivo_path: docData.arquivo_path,
            tipo_arquivo: docData.tipo_arquivo,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          const { error: anexoPrincipalError } = await serviceClient
            .from('lancamento_anexos')
            .insert(anexoPrincipal);

          if (anexoPrincipalError) {
            console.error('[API APROVAR] Erro ao criar anexo principal:', anexoPrincipalError);
          } else {
            console.log('[API APROVAR] Anexo principal criado com sucesso');
          }
        } catch (anexoError) {
          console.error('[API APROVAR] Erro ao criar anexo principal:', anexoError);
          // Continuar mesmo com erro no anexo
        }
      }

      // Atualizar o documento usando o cliente de serviço
      const { data, error } = await serviceClient
        .from('documentos')
        .update({
          status: 'aprovado',
          lancamento_id: lancamento.id
        })
        .eq('id', documentoId)
        .select()
        .single();

      if (error) {
        console.error('[API APROVAR] Erro ao atualizar documento:', error);
        return NextResponse.json(
          { error: 'Erro ao aprovar documento: ' + error.message },
          { status: 500 }
        );
      }

      return NextResponse.json({
        documento: data,
        lancamento: lancamento
      });
    } else {
      // RPC funcionou, obter o lançamento criado
      console.log('[API APROVAR] RPC funcionou, obtendo lançamento criado com ID:', rpcResult);

      // Obter o lançamento criado
      const { data: lancamento, error: getLancamentoError } = await serviceClient
        .from('lancamentos')
        .select('*')
        .eq('id', rpcResult)
        .single();

      if (getLancamentoError) {
        console.error('[API APROVAR] Erro ao obter lançamento criado:', getLancamentoError);
        return NextResponse.json(
          { error: 'Erro ao obter lançamento criado: ' + getLancamentoError.message },
          { status: 500 }
        );
      }

      // Buscar anexos adicionais do documento
      const { data: anexos, error: anexosError } = await serviceClient
        .from('documento_anexos')
        .select('*')
        .eq('documento_id', documentoId);

      if (anexosError) {
        console.error('[API APROVAR] Erro ao buscar anexos do documento (RPC):', anexosError);
        // Continuar mesmo com erro nos anexos
      } else if (anexos && anexos.length > 0) {
        console.log('[API APROVAR] Anexos encontrados (RPC):', anexos.length);

        // Criar anexos para o lançamento
        try {
          // Primeiro, criar o anexo principal do documento
          const anexoPrincipal = {
            lancamento_id: rpcResult,
            nome: docData.nome,
            arquivo_url: docData.arquivo_url,
            arquivo_path: docData.arquivo_path,
            tipo_arquivo: docData.tipo_arquivo,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          // Inserir o anexo principal
          const { error: anexoPrincipalError } = await serviceClient
            .from('lancamento_anexos')
            .insert(anexoPrincipal);

          if (anexoPrincipalError) {
            console.error('[API APROVAR] Erro ao criar anexo principal (RPC):', anexoPrincipalError);
          } else {
            console.log('[API APROVAR] Anexo principal criado com sucesso (RPC)');
          }

          // Preparar os anexos adicionais para inserção
          const lancamentoAnexos = anexos.map(anexo => ({
            lancamento_id: rpcResult,
            nome: anexo.nome,
            arquivo_url: anexo.arquivo_url,
            arquivo_path: anexo.arquivo_path,
            tipo_arquivo: anexo.tipo_arquivo,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }));

          // Inserir os anexos adicionais
          const { error: anexosLancamentoError } = await serviceClient
            .from('lancamento_anexos')
            .insert(lancamentoAnexos);

          if (anexosLancamentoError) {
            console.error('[API APROVAR] Erro ao criar anexos adicionais (RPC):', anexosLancamentoError);
          } else {
            console.log('[API APROVAR] Anexos adicionais criados com sucesso (RPC):', lancamentoAnexos.length);
          }
        } catch (anexosError) {
          console.error('[API APROVAR] Erro ao processar anexos (RPC):', anexosError);
          // Continuar mesmo com erro nos anexos
        }
      } else {
        // Criar apenas o anexo principal do documento
        try {
          const anexoPrincipal = {
            lancamento_id: rpcResult,
            nome: docData.nome,
            arquivo_url: docData.arquivo_url,
            arquivo_path: docData.arquivo_path,
            tipo_arquivo: docData.tipo_arquivo,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          const { error: anexoPrincipalError } = await serviceClient
            .from('lancamento_anexos')
            .insert(anexoPrincipal);

          if (anexoPrincipalError) {
            console.error('[API APROVAR] Erro ao criar anexo principal (RPC):', anexoPrincipalError);
          } else {
            console.log('[API APROVAR] Anexo principal criado com sucesso (RPC)');
          }
        } catch (anexoError) {
          console.error('[API APROVAR] Erro ao criar anexo principal (RPC):', anexoError);
          // Continuar mesmo com erro no anexo
        }
      }

      // Atualizar o documento usando o cliente de serviço
      const { data, error } = await serviceClient
        .from('documentos')
        .update({
          status: 'aprovado',
          lancamento_id: rpcResult
        })
        .eq('id', documentoId)
        .select()
        .single();

      if (error) {
        console.error('[API APROVAR] Erro ao atualizar documento:', error);
        return NextResponse.json(
          { error: 'Erro ao aprovar documento: ' + error.message },
          { status: 500 }
        );
      }

      return NextResponse.json({
        documento: data,
        lancamento: lancamento
      });
    }
  } catch (error: any) {
    console.error('[API APROVAR] Erro interno:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor: ' + error.message },
      { status: 500 }
    );
  }
}
