---
description: 
globs: 
alwaysApply: false
---
# Debugging de Autenticação

## Componentes de Diagnóstico

### AuthDebugger
O componente [AuthDebugger](mdc:src/components/AuthDebugger.tsx) fornece uma interface visual para diagnosticar problemas de autenticação, mostrando:
- Status da sessão atual
- Detalhes do usuário conectado
- Cookies do Supabase
- Opções para limpar cookies e reiniciar a sessão

Para usar, basta adicionar:
```tsx
<AuthDebugger />
```

## Estratégias de Debug

### Logs de Autenticação
As rotas de API usam um padrão de logs padronizado para debugging:
```typescript
console.log('[DEBUG API] Status da sessão:', sessionData.session ? 'Autenticado' : 'Não autenticado');
```

### Middleware
O [middleware](mdc:src/middleware.ts) implementa logs detalhados sobre o processo de autenticação:
```typescript
console.log('[Middleware] Running for path:', req.nextUrl.pathname);
```

### Utilitários de Reparo
Os utilitários em [fixCookies.ts](mdc:src/lib/fixCookies.ts) ajudam a resolver problemas com cookies corrompidos:
- `clearSupabaseCookies()`: Remove todos os cookies relacionados ao Supabase
- `resetSession()`: Limpa cookies e redireciona para login
- `emergencyFix()`: Executa correção completa em caso de erros persistentes

## Soluções Comuns

### Problemas com RLS
Para contornar problemas de Row Level Security:
1. Identificar a tabela com problemas
2. Verificar as políticas RLS atuais
3. Considerar desabilitar RLS temporariamente:
```sql
ALTER TABLE public.nome_tabela DISABLE ROW LEVEL SECURITY;
```

### Alternativas para Autenticação em APIs
Quando a autenticação via cookies não funciona:
1. Implementar parâmetros alternativos (como `userId` via query string)
2. Modificar a API para aceitar esses parâmetros
3. Ajustar o cliente para enviar os parâmetros alternativos

