import { useState, useEffect, useRef } from 'react';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plus, LayoutGrid, Pencil, Trash2, AlertCircle } from 'lucide-react';
import { Spinner } from '@/components/ui/spinner';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from 'sonner';
import NovaVisualizacaoModal from './nova-visualizacao-modal';
import EditarVisualizacaoModal from './editar-visualizacao-modal';
import { supabase } from '@/lib/supabase/client'; // Importar o cliente Supabase
import { useVisualizacoesService } from './filtros/visualizacoes-service'; // Importar o hook do serviço
import { Star } from 'lucide-react';

export interface Visualizacao {
  id: string;
  nome: string;
  descricao?: string;
  filtros_json: any;
  created_at: string;
  is_default?: boolean;
}

interface Props {
  visualizacaoAtiva: Visualizacao | null;
  setVisualizacaoAtiva: (v: Visualizacao | null) => void;
  filtrosAtuais: any;
}

export default function VisualizacoesDropdown({ visualizacaoAtiva, setVisualizacaoAtiva, filtrosAtuais }: Props) {
  const [visualizacoes, setVisualizacoes] = useState<Visualizacao[]>([]);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [visualizacaoParaEditar, setVisualizacaoParaEditar] = useState<Visualizacao | null>(null);
  const [visualizacaoParaExcluir, setVisualizacaoParaExcluir] = useState<Visualizacao | null>(null);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);

  // Usar o hook do serviço de visualizações
  const visualizacoesService = useVisualizacoesService();

  // Criar uma referência para o serviço para evitar re-renders desnecessários
  const visualizacoesServiceRef = useRef(visualizacoesService);

  // Obter ID do usuário atual e carregar visualizações (apenas uma vez ao montar)
  useEffect(() => {
    let isMounted = true;
    setLoading(true);

    async function getUserIdAndLoadVisualizations() {
      try {
        // Obter a sessão do usuário
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          toast.error('Erro de autenticação');
          return;
        }

        // Definir o ID do usuário se estiver autenticado
        if (sessionData.session?.user) {
          const currentUserId = sessionData.session.user.id;

          if (isMounted) {
            setUserId(currentUserId);
          }
        }

        // Carregar visualizações independentemente do usuário estar autenticado
        try {
          const visualizacoesData = await visualizacoesServiceRef.current.buscarTodasVisualizacoes();

          if (isMounted && visualizacoesData.length > 0) {
            setVisualizacoes(visualizacoesData);
          }
        } catch (visualizacoesError) {
          if (isMounted) {
            toast.error('Erro ao carregar visualizações');
          }
        }
      } catch (error) {
        if (isMounted) {
          toast.error('Erro ao verificar autenticação');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    getUserIdAndLoadVisualizations();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, []); // Remover visualizacoesService da dependência para evitar re-renders desnecessários

  // Recarregar visualizações quando os modais são fechados (após mudanças)
  useEffect(() => {
    let isMounted = true;
    let shouldReload = false;

    // Verificar se algum modal acabou de ser fechado
    if (!modalOpen && !editModalOpen && !confirmDeleteOpen) {
      // Armazenar o estado para evitar múltiplas chamadas
      shouldReload = true;
    }

    // Só recarregar quando os modais forem fechados
    if (shouldReload) {
      setLoading(true);

      async function reloadVisualizations() {
        try {
          const visualizacoesData = await visualizacoesServiceRef.current.buscarTodasVisualizacoes();

          if (isMounted) {
            setVisualizacoes(visualizacoesData);
          }
        } catch (error) {
          if (isMounted) {
            toast.error('Erro ao recarregar visualizações');
          }
        } finally {
          if (isMounted) {
            setLoading(false);
          }
        }
      }

      reloadVisualizations();
    }

    return () => {
      isMounted = false;
    };
  }, [modalOpen, editModalOpen, confirmDeleteOpen]);

  function handleSelecionar(v: Visualizacao) {
    setVisualizacaoAtiva(v);
    setOpen(false);
    // Persistir no localStorage
    if (typeof window !== 'undefined') {
      window.localStorage.setItem('visualizacao_lancamentos_ativa', v.id);

      // Check if sidebar is collapsed and save the visualization for that state too
      const isSidebarCollapsed = window.localStorage.getItem('sidebar_collapsed') === 'true';
      if (isSidebarCollapsed) {
        window.localStorage.setItem('visualizacao_lancamentos_ativa_when_collapsed', v.id);
      }
    }
  }

  function handleNovaVisualizacao() {
    setModalOpen(true);
    setOpen(false);
  }

  function handleEditarVisualizacao(e: React.MouseEvent, v: Visualizacao) {
    e.stopPropagation(); // Evitar que a visualização seja selecionada
    setVisualizacaoParaEditar(v);
    setEditModalOpen(true);
    setOpen(false);
  }

  function handleExcluirVisualizacao(e: React.MouseEvent, v: Visualizacao) {
    e.stopPropagation(); // Evitar que a visualização seja selecionada
    setVisualizacaoParaExcluir(v);
    setConfirmDeleteOpen(true);
    setOpen(false);
  }

  async function handleDefinirPadrao(e: React.MouseEvent, v: Visualizacao) {
    e.stopPropagation(); // Evitar que a visualização seja selecionada

    if (!userId) {
      toast.error('Você precisa estar autenticado para definir uma visualização como padrão');
      return;
    }

    // Mostrar toast de carregamento
    const loadingToast = toast.loading('Definindo visualização como padrão...');

    try {
      // Usar o serviço para definir a visualização como padrão
      const success = await visualizacoesServiceRef.current.definirVisualizacaoPadrao(v.id, userId);

      // Remover toast de carregamento
      toast.dismiss(loadingToast);

      if (success) {
        toast.success('Visualização definida como padrão');

        // Atualizar a lista local
        setVisualizacoes(vs => vs.map(item => ({
          ...item,
          is_default: item.id === v.id
        })));

        // Recarregar visualizações para garantir que o estado esteja atualizado
        const visualizacoesAtualizadas = await visualizacoesServiceRef.current.buscarTodasVisualizacoes();
        setVisualizacoes(visualizacoesAtualizadas);
      } else {
        toast.error('Não foi possível definir a visualização como padrão');
      }
    } catch (error: any) {
      // Remover toast de carregamento em caso de erro
      toast.dismiss(loadingToast);

toast.error(error.message || 'Erro ao definir visualização como padrão');

      // Tentar recarregar visualizações mesmo em caso de erro
      try {
        const visualizacoesAtualizadas = await visualizacoesServiceRef.current.buscarTodasVisualizacoes();
        setVisualizacoes(visualizacoesAtualizadas);
      } catch (reloadError) {
        // Ignorar erro de recarga
      }
    }
  }

  async function confirmarExclusao() {
    if (!visualizacaoParaExcluir) return;

    try {
      // Usar o serviço para excluir a visualização
      await visualizacoesServiceRef.current.excluirVisualizacao(visualizacaoParaExcluir.id);

      // Remover da lista local
      setVisualizacoes(vs => vs.filter(v => v.id !== visualizacaoParaExcluir.id));

      // Se a visualização excluída era a ativa, limpar a seleção
      if (visualizacaoAtiva?.id === visualizacaoParaExcluir.id) {
        setVisualizacaoAtiva(null);
        if (typeof window !== 'undefined') {
          window.localStorage.removeItem('visualizacao_lancamentos_ativa');

          // Also remove from collapsed state if it's the same visualization
          const collapsedVisualizationId = window.localStorage.getItem('visualizacao_lancamentos_ativa_when_collapsed');
          if (collapsedVisualizationId === visualizacaoParaExcluir.id) {
            window.localStorage.removeItem('visualizacao_lancamentos_ativa_when_collapsed');
          }
        }
      }

      toast.success('Visualização excluída com sucesso');
    } catch (error: any) {
      toast.error(error.message || "Não foi possível excluir a visualização.");
    } finally {
      setConfirmDeleteOpen(false);
      setVisualizacaoParaExcluir(null);
    }
  }

  // Carregar visualização ativa do localStorage ou a visualização padrão ao montar
  useEffect(() => {
    if (!visualizacaoAtiva && visualizacoes.length > 0) {
      // Primeiro, tentar carregar do localStorage
      if (typeof window !== 'undefined') {
        // Check if sidebar is collapsed
        const isSidebarCollapsed = window.localStorage.getItem('sidebar_collapsed') === 'true';

        // If sidebar is collapsed, try to load the visualization that was active when it was collapsed
        let idSalvo = isSidebarCollapsed
          ? window.localStorage.getItem('visualizacao_lancamentos_ativa_when_collapsed')
          : window.localStorage.getItem('visualizacao_lancamentos_ativa');

        // Fallback to regular active visualization if the collapsed one doesn't exist
        if (!idSalvo && isSidebarCollapsed) {
          idSalvo = window.localStorage.getItem('visualizacao_lancamentos_ativa');
        }

        const vSalva = visualizacoes.find(v => v.id === idSalvo);
        if (vSalva) {
          setVisualizacaoAtiva(vSalva);
          return;
        }
      }

      // Se não encontrou no localStorage, procurar a visualização padrão
      const visualizacaoPadrao = visualizacoes.find(v => v.is_default);
      if (visualizacaoPadrao) {
        setVisualizacaoAtiva(visualizacaoPadrao);

        // Salvar no localStorage também
        if (typeof window !== 'undefined') {
          window.localStorage.setItem('visualizacao_lancamentos_ativa', visualizacaoPadrao.id);

          // Check if sidebar is collapsed and save the visualization for that state too
          const isSidebarCollapsed = window.localStorage.getItem('sidebar_collapsed') === 'true';
          if (isSidebarCollapsed) {
            window.localStorage.setItem('visualizacao_lancamentos_ativa_when_collapsed', visualizacaoPadrao.id);
          }
        }
      }
    }
  }, [visualizacoes, visualizacaoAtiva, setVisualizacaoAtiva]);

  // Detectar mudanças no estado do sidebar (colapso/expansão)
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Função para salvar o estado da visualização quando o sidebar muda
    const handleSidebarChange = () => {
      if (!visualizacaoAtiva) return;

      const isSidebarCollapsed = window.localStorage.getItem('sidebar_collapsed') === 'true';

      if (isSidebarCollapsed) {
        // Salvar a visualização ativa para o estado colapsado
        window.localStorage.setItem('visualizacao_lancamentos_ativa_when_collapsed', visualizacaoAtiva.id);
      } else {
        // Salvar a visualização ativa para o estado expandido
        window.localStorage.setItem('visualizacao_lancamentos_ativa', visualizacaoAtiva.id);
      }
    };

    // Observar mudanças no localStorage para o item 'sidebar_collapsed'
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function(key, value) {
      // Chamar a implementação original
      originalSetItem.apply(this, [key, value]);

      // Se a chave for 'sidebar_collapsed', disparar o evento
      if (key === 'sidebar_collapsed') {
        handleSidebarChange();
      }
    };

    // Limpar o override ao desmontar
    return () => {
      localStorage.setItem = originalSetItem;
    };
  }, [visualizacaoAtiva]);

  // Salvar visualização ativa quando a página é atualizada ou fechada
  useEffect(() => {
    if (typeof window === 'undefined' || !visualizacaoAtiva) return;

    // Função para salvar o estado atual da visualização
    const handleBeforeUnload = () => {
      const isSidebarCollapsed = window.localStorage.getItem('sidebar_collapsed') === 'true';

      if (isSidebarCollapsed) {
        window.localStorage.setItem('visualizacao_lancamentos_ativa_when_collapsed', visualizacaoAtiva.id);
      } else {
        window.localStorage.setItem('visualizacao_lancamentos_ativa', visualizacaoAtiva.id);
      }
    };

    // Adicionar listener para o evento beforeunload
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Limpar o listener ao desmontar
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [visualizacaoAtiva]);

  // Não precisamos mais do fallback para usuário conhecido
  // pois o serviço já busca todas as visualizações independentemente do usuário

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="h-9 gap-2 min-w-[160px]" aria-label="Visualizações salvas">
            <LayoutGrid className="h-4 w-4" />
            <span className="truncate max-w-[100px]">
              {visualizacaoAtiva ? visualizacaoAtiva.nome : 'Visualizações'}
            </span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="bg-background text-foreground z-50 w-64 p-0">
          <div className="py-2">
            {loading ? (
              <div className="flex items-center justify-center py-4"><Spinner /></div>
            ) : (
              <>
                {visualizacoes.length === 0 && (
                  <div className="text-muted-foreground text-sm px-4 py-2">Nenhuma visualização salva</div>
                )}
                {visualizacoes.map(v => (
                  <div
                    key={v.id}
                    className={`w-full text-left px-4 py-2 hover:bg-muted focus:bg-muted transition group relative ${visualizacaoAtiva?.id === v.id ? 'bg-muted font-semibold' : ''}`}
                  >
                    <button
                      type="button"
                      className="w-full text-left flex flex-col"
                      onClick={() => handleSelecionar(v)}
                    >
                      <div className="flex items-center">
                        {v.is_default && (
                          <Star className="h-3.5 w-3.5 mr-1 text-amber-500 fill-amber-500" />
                        )}
                        <span>{v.nome}</span>
                      </div>
                      {v.descricao && <span className="text-xs text-muted-foreground">{v.descricao}</span>}
                    </button>

                    {/* Botões de ação que aparecem no hover */}
                    <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      {!v.is_default && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 text-amber-500 hover:text-amber-600"
                          onClick={(e) => handleDefinirPadrao(e, v)}
                          aria-label="Definir como padrão"
                          title="Definir como padrão"
                        >
                          <Star className="h-3.5 w-3.5" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7"
                        onClick={(e) => handleEditarVisualizacao(e, v)}
                        aria-label="Editar visualização"
                      >
                        <Pencil className="h-3.5 w-3.5" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7 text-destructive hover:text-destructive"
                        onClick={(e) => handleExcluirVisualizacao(e, v)}
                        aria-label="Excluir visualização"
                      >
                        <Trash2 className="h-3.5 w-3.5" />
                      </Button>
                    </div>
                  </div>
                ))}
                <div className="border-t my-2" />
                <button
                  className="w-full text-left px-4 py-2 text-primary hover:bg-accent focus:bg-accent font-medium flex items-center gap-2"
                  onClick={handleNovaVisualizacao}
                  type="button"
                >
                  <Plus className="h-4 w-4" /> Nova visualização
                </button>
                {visualizacaoAtiva && (
                  <button
                    className="w-full text-left px-4 py-2 text-destructive hover:bg-accent focus:bg-accent font-medium flex items-center gap-2"
                    onClick={() => {
                      setVisualizacaoAtiva(null);
                      if (typeof window !== 'undefined') {
                        window.localStorage.removeItem('visualizacao_lancamentos_ativa');
                        window.localStorage.removeItem('visualizacao_lancamentos_ativa_when_collapsed');
                      }
                      setOpen(false);
                      toast.success('Visualização limpa');
                    }}
                    type="button"
                  >
                    <Trash2 className="h-4 w-4" /> Limpar visualização
                  </button>
                )}
              </>
            )}
          </div>
        </PopoverContent>
      </Popover>
      <NovaVisualizacaoModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        filtrosAtuais={filtrosAtuais}
        onVisualizacaoCriada={(nova: Visualizacao) => {
          setVisualizacoes(vs => [nova, ...vs]);
          setVisualizacaoAtiva(nova);
          if (typeof window !== 'undefined') {
            window.localStorage.setItem('visualizacao_lancamentos_ativa', nova.id);

            // Check if sidebar is collapsed and save the visualization for that state too
            const isSidebarCollapsed = window.localStorage.getItem('sidebar_collapsed') === 'true';
            if (isSidebarCollapsed) {
              window.localStorage.setItem('visualizacao_lancamentos_ativa_when_collapsed', nova.id);
            }
          }
        }}
      />

      {/* Modal de edição */}
      <EditarVisualizacaoModal
        open={editModalOpen}
        onOpenChange={setEditModalOpen}
        visualizacao={visualizacaoParaEditar}
        onVisualizacaoAtualizada={(atualizada: Visualizacao) => {
          // Atualizar na lista local
          setVisualizacoes(vs => vs.map(v =>
            v.id === atualizada.id ? atualizada : v
          ));

          // Se a visualização atualizada era a ativa, atualizar também
          if (visualizacaoAtiva?.id === atualizada.id) {
            setVisualizacaoAtiva(atualizada);
          }

          toast.success('Visualização atualizada com sucesso');
        }}
      />

      {/* Diálogo de confirmação para exclusão */}
      <AlertDialog open={confirmDeleteOpen} onOpenChange={setConfirmDeleteOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Confirmar exclusão
            </AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir a visualização &quot;{visualizacaoParaExcluir?.nome}&quot;?
              Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmarExclusao}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}