'use client';

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Spinner } from '@/components/ui/spinner';
import { useContatosService } from '@/services/contatos';
import { Contato } from '@/types/contatos';
import { FormContatoEdit } from './form-contato-edit';

interface EditarContatoProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contatoId: string | null;
  onSuccess: () => void;
}

export function EditarContato({ open, onOpenChange, contatoId, onSuccess }: EditarContatoProps) {
  const [contato, setContato] = useState<Contato | null>(null);
  const [loading, setLoading] = useState(false);
  const contatosService = useContatosService();

  useEffect(() => {
    if (contatoId && open) {
      carregar<PERSON>ontato();
    }
  }, [contatoId, open]);

  async function carregarContato() {
    if (!contatoId) return;

    try {
      setLoading(true);
      const data = await contatosService.buscarContatoPorId(contatoId);
      setContato(data);
    } catch (error) {
      
    } finally {
      setLoading(false);
    }
  }

  function handleSuccess() {
    onSuccess();
    onOpenChange(false);
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Editar Contato</DialogTitle>
          <DialogDescription>
            Faça as alterações necessárias nos dados do contato.
          </DialogDescription>
        </DialogHeader>
        
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Spinner className="h-8 w-8" />
          </div>
        ) : contato ? (
          <FormContatoEdit 
            contato={contato} 
            onCancel={() => onOpenChange(false)} 
            onSuccess={handleSuccess} 
          />
        ) : null}
      </DialogContent>
    </Dialog>
  );
}