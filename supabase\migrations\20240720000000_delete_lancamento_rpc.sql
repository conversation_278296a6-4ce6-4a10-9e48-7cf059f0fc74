-- Create a function to delete a lancamento and its parcelas in a transaction
CREATE OR REPLACE FUNCTION delete_lancamento_with_parcelas(p_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  doc_count INTEGER;
BEGIN
  -- Start a transaction
  BEGIN
    -- First, update documents to remove the reference to the lancamento
    UPDATE documentos
    SET lancamento_id = NULL
    WHERE lancamento_id = p_id;
    
    -- Get the count of affected documents
    GET DIAGNOSTICS doc_count = ROW_COUNT;
    
    -- Delete parcelas (this should cascade due to the foreign key constraint)
    -- But we'll do it explicitly to ensure proper order
    DELETE FROM parcelas
    WHERE lancamento_id = p_id;
    
    -- Finally, delete the lancamento
    DELETE FROM lancamentos
    WHERE id = p_id;
    
    -- Return success
    RETURN TRUE;
  EXCEPTION
    WHEN OTHERS THEN
      -- Rollback the transaction in case of any error
      RAISE EXCEPTION 'Error deleting lancamento: %', SQLERRM;
      RETURN FALSE;
  END;
END;
$$;

-- Create a function to delete multiple lancamentos in a transaction
CREATE OR REPLACE FUNCTION delete_many_lancamentos(p_ids UUID[])
RETURNS TABLE(id UUID, success BOOLEAN, error_message TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_id UUID;
  v_success BOOLEAN;
  v_error TEXT;
BEGIN
  -- Process each lancamento
  FOREACH v_id IN ARRAY p_ids
  LOOP
    BEGIN
      -- Try to delete the lancamento
      SELECT delete_lancamento_with_parcelas(v_id) INTO v_success;
      
      -- Record the result
      id := v_id;
      success := v_success;
      error_message := NULL;
      
      -- Return this row
      RETURN NEXT;
    EXCEPTION
      WHEN OTHERS THEN
        -- Record the error
        id := v_id;
        success := FALSE;
        error_message := SQLERRM;
        
        -- Return this row
        RETURN NEXT;
    END;
  END LOOP;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION delete_lancamento_with_parcelas(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION delete_many_lancamentos(UUID[]) TO authenticated;
