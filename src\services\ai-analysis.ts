import { DadosExtraidos } from '@/types/documentos';

// Função para analisar um documento com Gemini AI
export async function analisarDocumento(
  documentoUrl: string
): Promise<DadosExtraidos> {
  try {
    // Adicionar timestamp para evitar cache
    const timestamp = new Date().getTime();
    const url = `/api/documentos/analisar?t=${timestamp}`;

    // Fazer a chamada para a API do Gemini
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache'
      },
      body: JSON.stringify({ url: documentoUrl }),
    });

    // Se a resposta não for ok, tentar obter detalhes do erro
    if (!response.ok) {
      let errorMessage = 'Erro ao analisar documento';

      // Clonar a resposta para poder ler o corpo múltiplas vezes se necessário
      const responseClone = response.clone();

      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorData.message || errorMessage;
      } catch (parseError) {
        // Se não conseguir fazer parse do JSON, usar o texto da resposta
        try {
          const errorText = await responseClone.text();
          errorMessage = errorText || `Erro ${response.status}: ${response.statusText}`;
        } catch (textError) {
          errorMessage = `Erro ${response.status}: ${response.statusText}`;
        }
      }
      
      throw new Error(errorMessage);
    }

    // Processar a resposta bem-sucedida
    // Clonar a resposta para evitar problemas de "body already read"
    const responseClone = response.clone();

    try {
      const data = await response.json();
      return data;
    } catch (jsonError) {
      // Tentar ler como texto se o JSON falhar
      try {
        const textData = await responseClone.text();

        // Tentar extrair JSON do texto
        const jsonMatch = textData.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const extractedData = JSON.parse(jsonMatch[0]);
          return extractedData;
        }
      } catch (textError) {
        // Falha ao ler texto da resposta
      }

      throw new Error('Não foi possível processar a resposta da análise');
    }
  } catch (error: any) {
    throw new Error(error.message || 'Erro ao analisar documento');
  }
}

// Hook para usar o serviço de análise de documentos
export function useAiAnalysisService() {
  return {
    analisarDocumento,
  };
}