'use client';

import { useState, useEffect } from 'react';
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  SheetTitle,
  SheetDescription,
  SheetFooter,
} from '@/components/ui/sheet';
import { cn } from '@/lib/utils';
import { But<PERSON> } from '@/components/ui/button';
import { Documento } from '@/types/documentos';
import { formatDateToISOString } from '@/lib/date-utils';
import { FormDocumento } from './form-documento';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Loader2, AlertTriangle } from 'lucide-react';
import { aprovarDocumento, rejeitarDocumento, salvarDocumento } from '@/services/documentos';
import { FilePreview } from '@/components/FilePreview';
import { DocumentoAnexosNovo } from './documento-anexos-novo';
import { Separator } from '@/components/ui/separator';
import { InfoPagamento } from './info-pagamento';
import { DrawerDropzone } from '@/components/ui/drawer-dropzone';


interface DrawerDocumentoProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  documento: Documento | null;
  onSuccess: () => void;
}

export function DrawerDocumento({ open, onOpenChange, documento, onSuccess }: DrawerDocumentoProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [rejeicaoDialogOpen, setRejeicaoDialogOpen] = useState(false);
  const [motivoRejeicao, setMotivoRejeicao] = useState('');
  const [previewOpen, setPreviewOpen] = useState(false);
  const [showDropzone, setShowDropzone] = useState(false);

  // Adicionar/remover atributo no body quando drawer abrir/fechar
  useEffect(() => {
    if (open) {
      document.body.setAttribute('data-drawer-documento-open', 'true');
    } else {
      document.body.removeAttribute('data-drawer-documento-open');
      setShowDropzone(false); // Esconder dropzone quando drawer fechar
    }

    // Cleanup ao desmontar
    return () => {
      document.body.removeAttribute('data-drawer-documento-open');
    };
  }, [open]);

  // Detectar quando arquivos estão sendo arrastados - apenas mostrar/esconder dropzone
  useEffect(() => {
    if (!open) return;

    let dragCounter = 0;

    const handleDragEnter = (e: DragEvent) => {
      dragCounter++;
      
      if (e.dataTransfer?.types.includes('Files')) {
        console.log('🎯 DrawerDocumento: Drag detectado, mostrando dropzone');
        setShowDropzone(true);
      }
    };

    const handleDragLeave = (e: DragEvent) => {
      dragCounter--;
      
      // Só esconder quando não há mais elementos sendo arrastados
      if (dragCounter === 0) {
        console.log('🎯 DrawerDocumento: Drag saiu, escondendo dropzone');
        setShowDropzone(false);
      }
    };

    // NÃO interceptar dragover e drop - deixar o DrawerDropzone processar
    document.addEventListener('dragenter', handleDragEnter, false);
    document.addEventListener('dragleave', handleDragLeave, false);

    return () => {
      document.removeEventListener('dragenter', handleDragEnter, false);
      document.removeEventListener('dragleave', handleDragLeave, false);
    };
  }, [open]);

  // Handler para arquivos dropados no dropzone
  const handleFilesDrop = async (files: File[]) => {
    console.log('📁 DrawerDocumento: Arquivos recebidos para processamento:', files.map(f => f.name));
    // O DrawerDropzone já fez o processamento internamente
    // Esconder o dropzone imediatamente
    setShowDropzone(false);
  };

  // Handler para quando o enriquecimento estiver completo
  const handleEnrichmentComplete = () => {
    console.log('✅ DrawerDocumento: Enriquecimento completo');
    // Atualizar o documento ou fazer refresh dos dados
    onSuccess();
  };

  const handleAprovar = async (data: any) => {
    if (!documento) return;

    setIsSubmitting(true);
    try {
      await aprovarDocumento(documento.id, data);
      toast.success('Documento aprovado e lançamento criado com sucesso!');
      onOpenChange(false);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'Erro ao aprovar documento');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRejeitar = async () => {
    if (!documento) return;
    if (!motivoRejeicao.trim()) {
      toast.error('Informe o motivo da rejeição');
      return;
    }

    setIsSubmitting(true);
    try {
      await rejeitarDocumento(documento.id, motivoRejeicao);
      toast.success('Documento rejeitado com sucesso!');
      setRejeicaoDialogOpen(false);
      onOpenChange(false);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'Erro ao rejeitar documento');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSalvar = async (data: any) => {
    if (!documento) return;

    setIsSubmitting(true);
    try {
      // Preparar os dados para salvar
      let valorTotal;
      try {
        // Usar lógica mais robusta para extrair valor monetário (similar à do form-documento)
        const valorFormatado = data.valor_total;
        if (!valorFormatado) {
          valorTotal = 0;
        } else {
          // Remover todos os caracteres não numéricos, exceto vírgula e ponto
          // Primeiro, remover o símbolo de moeda e espaços
          let valorLimpo = valorFormatado.replace(/[R$\s]/g, '');

          // Tratar valores com pontos de milhar e vírgulas decimais (formato brasileiro)
          // Ex: "15.189,46" -> 15189.46
          if (valorLimpo.includes('.') && valorLimpo.includes(',')) {
            // Remover pontos de milhar e substituir vírgula por ponto
            valorLimpo = valorLimpo.replace(/\./g, '').replace(',', '.');
          } else if (valorLimpo.includes(',')) {
            // Apenas substituir vírgula por ponto
            valorLimpo = valorLimpo.replace(',', '.');
          }

          // Converter para número
          valorTotal = parseFloat(valorLimpo);

          // Verificar se o valor é válido
          if (isNaN(valorTotal)) {
            valorTotal = 0;
          }
        }

        if (valorTotal <= 0) {
          throw new Error('Valor total deve ser maior que zero');
        }
      } catch (e) {
        toast.error('Valor total inválido. Por favor, verifique o formato.');
        setIsSubmitting(false);
        return;
      }

      const dataVencimento = formatDateToISOString(data.data_vencimento);
      if (!dataVencimento) {
        toast.error('Data de vencimento inválida. Por favor, verifique o formato.');
        setIsSubmitting(false);
        return;
      }

      // Verificar se obra_id está definido e não é uma string vazia
      const obraId = data.obra_id && data.obra_id.trim() !== '' ? data.obra_id : null;

      // Verificar se a descrição está definida
      const descricao = data.descricao && data.descricao.trim() !== '' ? data.descricao : documento.nome;

      const dadosParaSalvar = {
        fornecedor: data.fornecedor,
        valor_total: valorTotal,
        data_vencimento: dataVencimento,
        contato_id: data.contato_id || null,
        obra_id: obraId,
        descricao: descricao
      };

      // Tentar atualizar diretamente o campo obra_id primeiro
      if (obraId !== null) {
        try {
          const obraResponse = await fetch(`/api/documentos/atualizar-obra-sql?id=${documento.id}&obra_id=${obraId}&t=${Date.now()}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });

          if (!obraResponse.ok) {
            toast.warning('Aviso: Houve um problema ao atualizar a obra. Tente novamente.');
          }
        } catch (obraError) {
          // Silenciosamente falhar e continuar
        }
      }

      // Salvar o documento sem alterar seu status
      const documentoAtualizado = await salvarDocumento(documento.id, dadosParaSalvar);

      // Verificar se a atualização foi bem-sucedida
      if (documentoAtualizado) {
        // Verificar se o campo obra_id foi atualizado corretamente
        if (obraId !== null && documentoAtualizado.obra_id !== obraId) {
          // Tentar atualizar novamente
          try {
            const retryResponse = await fetch(`/api/documentos/${documento.id}?t=${Date.now()}&forceServiceRole=true`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
              },
              body: JSON.stringify({ obra_id: obraId })
            });

            // Silenciosamente continuar mesmo se falhar
          } catch (retryError) {
            // Silenciosamente falhar e continuar
          }
        }
      }

      toast.success('Documento salvo com sucesso!');

      // Pequena pausa para garantir que os dados foram atualizados no banco
      await new Promise(resolve => setTimeout(resolve, 500));

      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'Erro ao salvar documento');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent 
          className="sm:max-w-[600px] flex flex-col h-full p-0"
          side="right"
          data-documento-id={documento?.id}
        >
          <SheetHeader className="shrink-0 p-6 pb-2">
            <SheetTitle>Detalhes do Documento</SheetTitle>
            <SheetDescription>
              {documento?.nome || 'Carregando...'}
            </SheetDescription>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto p-6">
            {documento && (
              <FormDocumento
                documento={documento}
                onSubmit={handleAprovar}
                onSalvar={handleSalvar}
                onRejeitar={() => setRejeicaoDialogOpen(true)}
                isSubmitting={isSubmitting}
              />
            )}
          </div>



          {documento && (
            <SheetFooter className="shrink-0 border-t bg-background p-4 flex flex-row justify-end gap-2">
              {documento.status === 'pendente' ? (
                <>
                  <Button
                    variant="outline"
                    onClick={() => setRejeicaoDialogOpen(true)}
                    disabled={isSubmitting}
                    type="button"
                  >
                    Rejeitar
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      const saveButton = document.getElementById('form-save-button');
                      if (saveButton) {
                        saveButton.click();
                      }
                    }}
                    disabled={isSubmitting}
                    type="button"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Salvando...
                      </>
                    ) : (
                      'Salvar'
                    )}
                  </Button>
                  <Button
                    onClick={() => {
                      const submitButton = document.getElementById('form-submit-button');
                      if (submitButton) {
                        submitButton.click();
                      }
                    }}
                    disabled={isSubmitting}
                    type="button"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Aprovando...
                      </>
                    ) : (
                      'Aprovar e Criar Lançamento'
                    )}
                  </Button>
                </>
              ) : (
                <Button
                  onClick={() => {
                    const saveButton = document.getElementById('form-save-button');
                    if (saveButton) {
                      saveButton.click();
                    }
                  }}
                  disabled={isSubmitting}
                  type="button"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Salvando...
                    </>
                  ) : (
                    'Salvar Alterações'
                  )}
                </Button>
              )}
            </SheetFooter>
          )}
        </SheetContent>
      </Sheet>

      {/* Diálogo de rejeição */}
      <Dialog open={rejeicaoDialogOpen} onOpenChange={setRejeicaoDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rejeitar Documento</DialogTitle>
            <DialogDescription>
              Informe o motivo da rejeição deste documento.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Label htmlFor="motivo-rejeicao" className="text-destructive flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Motivo da Rejeição
            </Label>
            <Textarea
              id="motivo-rejeicao"
              value={motivoRejeicao}
              onChange={(e) => setMotivoRejeicao(e.target.value)}
              placeholder="Informe o motivo da rejeição..."
              className="mt-2"
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setRejeicaoDialogOpen(false)}
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleRejeitar}
              disabled={isSubmitting || !motivoRejeicao.trim()}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Rejeitando...
                </>
              ) : (
                'Rejeitar Documento'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Componente de preview de arquivo */}
      {documento && (
        <FilePreview
          file={{
            url: documento.arquivo_url,
            name: documento.nome,
            type: documento.tipo_arquivo
          }}
          open={previewOpen}
          onOpenChange={setPreviewOpen}
          title={documento.nome}
        />
      )}

      {/* Dropzone para enriquecimento de documentos */}
      {showDropzone && documento && open && (
        <DrawerDropzone
          onDrop={handleFilesDrop}
          onEnrichmentComplete={handleEnrichmentComplete}
          documentoId={documento.id}
        />
      )}
    </>
  );
}