'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';

interface SidebarContextType {
  isCollapsed: boolean;
  toggleCollapse: () => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export function SidebarProvider({ children }: { children: React.ReactNode }) {
  // Initialize state from localStorage if available
  const [isCollapsed, setIsCollapsed] = useState<boolean>(() => {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('sidebar_collapsed');
      return savedState === 'true';
    }
    return false;
  });

  const pathname = usePathname();

  // Save to localStorage whenever the state changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('sidebar_collapsed', isCollapsed.toString());

      // When sidebar is collapsed, save the current active visualization
      if (isCollapsed) {
        const activeVisualizationId = localStorage.getItem('visualizacao_lancamentos_ativa');
        if (activeVisualizationId) {
          // Store the active visualization ID in a separate key for when the sidebar is collapsed
          localStorage.setItem('visualizacao_lancamentos_ativa_when_collapsed', activeVisualizationId);
        }
      }
    }
  }, [isCollapsed]);

  useEffect(() => {
    // Effect for pathname changes
  }, [pathname]);

  const toggleCollapse = () => {
    setIsCollapsed(prev => {
      const newState = !prev;

      // When toggling to collapsed state, save the current visualization
      if (newState && typeof window !== 'undefined') {
        const activeVisualizationId = localStorage.getItem('visualizacao_lancamentos_ativa');
        if (activeVisualizationId) {
          localStorage.setItem('visualizacao_lancamentos_ativa_when_collapsed', activeVisualizationId);
        }
      }

      return newState;
    });
  };

  return (
    <SidebarContext.Provider value={{ isCollapsed, toggleCollapse }}>
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebar() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
}