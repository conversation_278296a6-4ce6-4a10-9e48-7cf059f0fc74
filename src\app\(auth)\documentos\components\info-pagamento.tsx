'use client';

import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar, CreditCard, Info } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface InfoPagamentoProps {
  lancamentoId: string;
}

interface LancamentoInfo {
  id: string;
  status: 'Em aberto' | 'Pago' | 'Cancelado';
  forma_pagamento: 'dinheiro' | 'pix' | 'cartao_credito' | 'cartao_debito' | 'cheque' | 'transferencia' | 'boleto';
  data_pagamento?: string | null;
  data_competencia: string;
  valor_total: number;
}

export function InfoPagamento({ lancamentoId }: InfoPagamentoProps) {
  const [lancamento, setLancamento] = useState<LancamentoInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const carregarLancamento = async () => {
      try {
        const response = await fetch(`/api/lancamentos/${lancamentoId}`);
        if (response.ok) {
          const data = await response.json();
          setLancamento(data);
        }
      } catch (error) {
        console.error('Erro ao carregar informações do lançamento:', error);
      } finally {
        setLoading(false);
      }
    };

    if (lancamentoId) {
      carregarLancamento();
    }
  }, [lancamentoId]);

  if (loading) {
    return (
      <div className="p-3 bg-muted/50 rounded-md">
        <div className="animate-pulse">
          <div className="h-4 bg-muted rounded w-32 mb-2"></div>
          <div className="h-3 bg-muted rounded w-24"></div>
        </div>
      </div>
    );
  }

  if (!lancamento) {
    return null;
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Pago':
        return <Badge variant="default" className="bg-green-600 hover:bg-green-700">✅ Pago</Badge>;
      case 'Em aberto':
        return <Badge variant="secondary">⏳ Pendente</Badge>;
      case 'Cancelado':
        return <Badge variant="destructive">❌ Cancelado</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getFormaPagamentoLabel = (forma: string) => {
    const formas = {
      'dinheiro': '💵 Dinheiro',
      'pix': '📱 PIX',
      'cartao_credito': '💳 Cartão de Crédito',
      'cartao_debito': '💳 Cartão de Débito',
      'cheque': '📄 Cheque',
      'transferencia': '🏦 Transferência',
      'boleto': '📋 Boleto'
    };
    return formas[forma as keyof typeof formas] || forma;
  };

  return (
    <div className="p-3 bg-muted/50 rounded-md border">
      <div className="flex items-center justify-between mb-2">
        <Label className="text-sm font-medium">Status de Pagamento</Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="icon" className="h-6 w-6">
              <Info className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 bg-background text-foreground z-[100]">
            <div className="space-y-3">
              <div>
                <Label className="text-xs text-muted-foreground">Forma de Pagamento</Label>
                <div className="flex items-center gap-2 mt-1">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{getFormaPagamentoLabel(lancamento.forma_pagamento)}</span>
                </div>
              </div>
              
              {lancamento.data_pagamento && (
                <div>
                  <Label className="text-xs text-muted-foreground">Data de Pagamento</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      {format(new Date(lancamento.data_pagamento), 'dd/MM/yyyy', { locale: ptBR })}
                    </span>
                  </div>
                </div>
              )}
              
              <div>
                <Label className="text-xs text-muted-foreground">Data de Competência</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    {format(new Date(lancamento.data_competencia), 'dd/MM/yyyy', { locale: ptBR })}
                  </span>
                </div>
              </div>
              
              <div>
                <Label className="text-xs text-muted-foreground">Valor Total</Label>
                <div className="text-sm font-medium mt-1">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(lancamento.valor_total)}
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
      
      <div className="flex items-center gap-2">
        {getStatusBadge(lancamento.status)}
        <span className="text-xs text-muted-foreground">
          {getFormaPagamentoLabel(lancamento.forma_pagamento)}
        </span>
      </div>
    </div>
  );
} 