'use client';

import { useState } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Settings } from 'lucide-react';

export type Column = {
  id: string;
  label: string;
  isVisible: boolean;
};

interface ColumnSelectorProps {
  columns: Column[];
  onChange: (columns: Column[]) => void;
}

export function ColumnSelector({ columns, onChange }: ColumnSelectorProps) {
  const [localColumns, setLocalColumns] = useState<Column[]>(columns);

  const handleColumnToggle = (columnId: string) => {
    const updatedColumns = localColumns.map((col) =>
      col.id === columnId ? { ...col, isVisible: !col.isVisible } : col
    );
    
    setLocalColumns(updatedColumns);
    onChange(updatedColumns);
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="ml-auto h-8 gap-1">
          <Settings className="h-4 w-4" />
          <span>Colunas</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent align="end" className="w-[200px] p-0">
        <div className="p-2 font-medium border-b">Personalizar Colunas</div>
        <div className="p-2 space-y-2">
          {localColumns.map((column) => (
            <div key={column.id} className="flex items-center space-x-2">
              <Checkbox
                id={`column-${column.id}`}
                checked={column.isVisible}
                onCheckedChange={() => handleColumnToggle(column.id)}
              />
              <label
                htmlFor={`column-${column.id}`}
                className="text-sm font-normal cursor-pointer"
              >
                {column.label}
              </label>
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}