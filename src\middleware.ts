import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  // Verificar cookie de autenticação silenciosamente
  const authCookieName = Object.keys(req.cookies.getAll()).find(name =>
    name.startsWith('sb-') && name.endsWith('-auth-token')
  );

  const res = NextResponse.next()
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          req.cookies.set({
            name,
            value,
            ...options,
          })
          res.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: CookieOptions) {
          req.cookies.set({
            name,
            value: '',
            ...options,
          })
          res.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  // Verificar sessão
  const sessionResult = await supabase.auth.getSession()
  const { data: { session } } = sessionResult

  const { pathname } = req.nextUrl

  // Verificar se é uma rota de API
  const isApiRoute = pathname.startsWith('/api');

  // Para rotas de API, não redirecionamos, apenas verificamos a autenticação
  if (isApiRoute) {
    // Se não houver sessão, deixamos a rota API decidir se precisa de autenticação
    if (!session) {
      // Não bloqueamos aqui, deixamos a rota API decidir se precisa de autenticação
    }
    return res;
  }

  // Redireciona para login se não houver sessão e não estiver na página de login (apenas para rotas não-API)
  if (!session && pathname !== '/login') {
    return NextResponse.redirect(new URL('/login', req.url))
  }

  // Redireciona para a raiz se houver sessão e estiver na página de login
  if (session && pathname === '/login') {
    return NextResponse.redirect(new URL('/', req.url))
  }

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     *
     * Incluímos as rotas de API para garantir que a autenticação seja verificada
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};