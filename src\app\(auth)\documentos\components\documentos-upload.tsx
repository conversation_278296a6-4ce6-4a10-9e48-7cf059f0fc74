'use client';

import { useState, useRef, useEffect } from 'react';
import { Dropzone } from '@/components/ui/dropzone';
import { Button } from '@/components/ui/button';
import { Upload, FileText, Image, X } from 'lucide-react';
import { toast } from 'sonner';
import { uploadDocumento, getDocumentoById, atualizarDadosExtraidos, atualizarContatoDocumento } from '@/services/documentos';
import { analisarDocumento } from '@/services/ai-analysis';
import { supabase } from '@/lib/supabase/client';
import { DOCUMENT_PROCESSING_STEPS } from '@/components/ui/document-processing-notification';
import { DocumentProcessingNotificationLight } from '@/components/ui/document-processing-notification-light';
import { useContatosService } from '@/services/contatos';

interface DocumentosUploadProps {
  onUploadSuccess: (file?: File, step?: number, isComplete?: boolean) => void;
}

export function DocumentosUpload({ onUploadSuccess }: DocumentosUploadProps) {
  const [files, setFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [analyzing, setAnalyzing] = useState(false);
  const [uploadedDocIds, setUploadedDocIds] = useState<string[]>([]);
  const [processingFile, setProcessingFile] = useState<File | null>(null);
  const [processingStep, setProcessingStep] = useState<number>(0);
  const [showProcessingNotification, setShowProcessingNotification] = useState(false);
  const [processingComplete, setProcessingComplete] = useState(false);
  const processingStepTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Inicializar o serviço de contatos
  const contatosService = useContatosService();

  const handleDrop = (acceptedFiles: File[]) => {
    // Filtrar apenas arquivos PDF, PNG e JPEG
    const validFiles = acceptedFiles.filter(file => {
      const isValid =
        file.type === 'application/pdf' ||
        file.type === 'image/png' ||
        file.type === 'image/jpeg';

      if (!isValid) {
        toast.error(`Arquivo não suportado: ${file.name}. Apenas PDF, PNG e JPEG são aceitos.`);
      }

      return isValid;
    });

    if (validFiles.length > 0) {
      // Adicionar os arquivos à lista e iniciar o upload imediatamente
      setFiles(validFiles);

      // Iniciar o upload automaticamente com um pequeno delay
      // para garantir que o estado foi atualizado
      setTimeout(() => {
        uploadFiles(validFiles);
      }, 100);
    }
  };

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  // Função para buscar e vincular contato automaticamente
  const buscarEVincularContato = async (documentoId: string, fornecedor: string | null | undefined) => {
    if (!fornecedor) return null;

    try {
      // Buscar contato pelo nome do fornecedor
      const contatoEncontrado = await contatosService.buscarContatoPorNome(fornecedor);

      if (contatoEncontrado) {
        // Vincular o contato ao documento
        await atualizarContatoDocumento(documentoId, contatoEncontrado.id);

        // Retornar o contato encontrado
        return contatoEncontrado;
      }
    } catch (error) {
      
    }

    return null;
  };

  // Função para analisar um documento com avanço gradual da animação
  const analisarDocumentoUpload = async (documentoId: string) => {
    try {
      // Buscar o documento para obter a URL
      const documento = await getDocumentoById(documentoId);

      if (!documento) {
        
        return;
      }

      // Avançar para o passo de extração de texto (OCR)
      setProcessingStep(1); // OCR
      onUploadSuccess(undefined, 1, false); // Notificar a página principal
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Avançar para o passo de identificação de fornecedor
      setProcessingStep(2); // Identificando fornecedor
      onUploadSuccess(undefined, 2, false); // Notificar a página principal
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Avançar para o passo de extração de valores
      setProcessingStep(3); // Extraindo valores
      onUploadSuccess(undefined, 3, false); // Notificar a página principal

      // Analisar o documento com IA (processo real)
      const dadosExtraidos = await analisarDocumento(documento.arquivo_url);

      // Avançar para o passo de extração de datas
      setProcessingStep(4); // Extraindo datas
      onUploadSuccess(undefined, 4, false); // Notificar a página principal
      await new Promise(resolve => setTimeout(resolve, 800));

      // Avançar para o passo de classificação
      setProcessingStep(5); // Classificando documento
      onUploadSuccess(undefined, 5, false); // Notificar a página principal
      await new Promise(resolve => setTimeout(resolve, 800));

      // Avançar para o passo de verificação de duplicidade
      setProcessingStep(6); // Verificando duplicidade
      onUploadSuccess(undefined, 6, false); // Notificar a página principal

      // Atualizar o documento com os dados extraídos
      const documentoAtualizado = await atualizarDadosExtraidos(documentoId, dadosExtraidos);

      // Não mais vincular contato automaticamente - apenas detectar dados
      // O contato será criado apenas quando o documento for aprovado
      // Os dados detectados já estão salvos em dados_extraidos.contato_detectado

      // Avançar para o passo final
      setProcessingStep(7); // Finalizando análise
      onUploadSuccess(undefined, 7, false); // Notificar a página principal
      await new Promise(resolve => setTimeout(resolve, 800));

      return true;
    } catch (error: any) {
      
      return false;
    }
  };

  // Limpar ao desmontar o componente
  useEffect(() => {
    return () => {
      if (processingStepTimerRef.current) {
        clearTimeout(processingStepTimerRef.current);
      }
    };
  }, []);

  // Limpar timers de processamento
  const clearProcessingTimers = () => {
    if (processingStepTimerRef.current) {
      clearTimeout(processingStepTimerRef.current);
      processingStepTimerRef.current = null;
    }
  };

  const uploadFiles = async (filesToUpload?: File[]) => {
    // Usar os arquivos fornecidos ou os do estado
    const currentFiles = filesToUpload || files;

    // Verificar se há arquivos para enviar
    if (currentFiles.length === 0) {
      toast.error('Selecione pelo menos um arquivo para enviar.');
      return;
    }

    // Evitar múltiplos uploads simultâneos
    if (uploading || analyzing) {
      toast.info('Upload já em andamento, aguarde a conclusão.');
      return;
    }

    setUploading(true);
    setUploadedDocIds([]);

    try {
      // Obter o ID do usuário atual
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('Usuário não autenticado');
      }

      const userId = session.user.id;

      // Iniciar o processamento com o primeiro arquivo
      if (currentFiles.length > 0) {
        // Configurar o arquivo que está sendo processado e notificar a página principal
        setProcessingFile(currentFiles[0]);
        setProcessingStep(0); // Iniciar no primeiro passo
        setProcessingComplete(false);
        setShowProcessingNotification(true);

        // Notificar a página principal sobre o início do processamento
        onUploadSuccess(currentFiles[0], 0, false);
      }

      // Upload de cada arquivo usando o serviço - um por um para evitar problemas
      const uploadedDocs = [];
      for (const file of currentFiles) {
        try {
          const doc = await uploadDocumento(file, userId);
          uploadedDocs.push(doc);
        } catch (error: any) {
          
          toast.error(`Erro ao enviar ${file.name}: ${error.message || 'Erro desconhecido'}`);
        }
      }

      // Se nenhum upload foi bem-sucedido, lançar erro
      if (uploadedDocs.length === 0) {
        throw new Error('Nenhum arquivo foi enviado com sucesso');
      }

      // Armazenar os IDs dos documentos enviados
      const docIds = uploadedDocs.map(doc => doc.id);
      setUploadedDocIds(docIds);

      // Limpar a lista de arquivos somente após o upload bem-sucedido
      setFiles([]);

      // Não notificar a página principal ainda - vamos esperar a análise completa

      // Iniciar análise automática dos documentos
      setAnalyzing(true);

      // Analisar cada documento
      for (const docId of docIds) {
        try {
          await analisarDocumentoUpload(docId);
        } catch (error: any) {
          
          // Não exibir toast para não sobrecarregar o usuário
        }
      }

      // Marcar o processamento como completo
      setProcessingComplete(true);

      // Notificar a página principal sobre a conclusão
      onUploadSuccess(undefined, 7, true);

      // Aguardar um pouco para que o usuário veja a mensagem de conclusão
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Esconder a notificação
      setShowProcessingNotification(false);

      // Não mostrar toast no final do processo
      // O componente de notificação já mostra o status
    } catch (error: any) {

// Esconder a notificação em caso de erro
      setShowProcessingNotification(false);

      // Verificar se é erro de autenticação
      if (error.message && (
        error.message.includes('auth') ||
        error.message.includes('JWT') ||
        error.message.includes('token') ||
        error.message.includes('401') ||
        error.message.includes('não autenticado')
      )) {
        toast.error('Erro de autenticação. Tente fazer login novamente.');
      } else {
        toast.error(error.message || 'Erro ao enviar documentos');
      }
    } finally {
      setUploading(false);
      setAnalyzing(false);
      setProcessingFile(null);
    }
  };

  const getFileIcon = (file: File) => {
    if (file.type === 'application/pdf') {
      return <FileText className="h-5 w-5 text-red-500" />;
    }
    return <Image className="h-5 w-5 text-blue-500" />;
  };

  return (
    <div className="space-y-4">
      <Dropzone
        onDrop={handleDrop}
        accept={{
          'application/pdf': ['.pdf'],
          'image/png': ['.png'],
          'image/jpeg': ['.jpg', '.jpeg']
        }}
        multiple
        disabled={uploading || analyzing}
      >
        <div className="flex flex-col items-center justify-center py-6">
          {uploading || analyzing ? (
            <>
              <div className="h-10 w-10 border-4 border-muted-foreground/30 border-t-transparent rounded-full animate-spin mb-2" />
              <p className="text-sm text-muted-foreground mb-1">
                {uploading ? 'Enviando arquivo...' : 'Analisando documento...'}
              </p>
              <p className="text-xs text-muted-foreground">
                Por favor, aguarde a conclusão
              </p>
            </>
          ) : (
            <>
              <Upload className="h-10 w-10 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground mb-1">
                Arraste e solte ou clique para selecionar
              </p>
              <p className="text-xs text-muted-foreground">
                Suporta PDF, PNG e JPEG
              </p>
            </>
          )}
        </div>
      </Dropzone>

      {/* Notification de processamento - Renderizado fora do fluxo normal para evitar problemas de layout */}

      {/* Mostrar a lista de arquivos apenas quando não estiver fazendo upload */}
      {files.length > 0 && !uploading && !analyzing && (
        <div className="space-y-4">
          <div className="border rounded-md divide-y">
            {files.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-3">
                <div className="flex items-center space-x-3">
                  {getFileIcon(file)}
                  <div>
                    <p className="text-sm font-medium truncate max-w-[200px] md:max-w-[400px]">
                      {file.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {(file.size / 1024).toFixed(1)} KB
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => removeFile(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>

          <div className="flex justify-end">
            <Button
              onClick={() => uploadFiles()}
              className="flex items-center space-x-2"
            >
              <Upload className="h-4 w-4" />
              <span>Enviar {files.length} arquivo(s)</span>
            </Button>
          </div>
        </div>
      )}

      {/* A notificação será renderizada fora do componente para garantir posicionamento correto */}
    </div>
  );
}