import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';

// Função para criar um cliente Supabase com a chave de serviço
const createServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    
    return null;
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    // Obter dados da requisição
    const { id, user_id } = await request.json();

    if (!id) {
      return NextResponse.json({ error: 'ID da visualização não fornecido' }, { status: 400 });
    }

    if (!user_id) {
      return NextResponse.json({ error: 'ID do usuário não fornecido' }, { status: 400 });
    }

    // Verificar autenticação
    const supabase = await getSupabaseRouteClient();

    // Verificar se o usuário está autenticado
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      
      // Continuar mesmo com erro, pois estamos usando o service client
    }

    // Verificar se o usuário autenticado é o mesmo que está sendo passado
    if (session?.user && session.user.id !== user_id) {
      
      // Continuar mesmo com aviso, pois pode ser uma operação administrativa
    }

    // Criar cliente de serviço para contornar problemas de CORS e RLS
    const serviceClient = createServiceClient();

    if (!serviceClient) {
      return NextResponse.json({ error: 'Não foi possível criar o cliente de serviço' }, { status: 500 });
    }

    // Verificar se a visualização existe
    const { data: visualizacao, error: visualizacaoError } = await serviceClient
      .from('visualizacoes')
      .select('contexto')
      .eq('id', id)
      .single();

    if (visualizacaoError) {
      return NextResponse.json({ error: 'Visualização não encontrada' }, { status: 404 });
    }

    const contexto = visualizacao.contexto;

    // Abordagem 1: Usar a função RPC
    try {
      const { data, error } = await serviceClient.rpc(
        'set_default_visualization',
        {
          p_visualization_id: id,
          p_user_id: user_id
        }
      );

      if (!error) {
        return NextResponse.json({ success: true, method: 'rpc' });
      }

      // Continuar para o fallback
    } catch (rpcError) {
      
      // Continuar para o fallback
    }

    // Abordagem 2: Atualização direta em duas etapas
    try {
      // Primeiro, remover o status de padrão de todas as visualizações do usuário no mesmo contexto
      const { error: resetError } = await serviceClient
        .from('visualizacoes')
        .update({ is_default: false })
        .eq('user_id', user_id)
        .eq('contexto', contexto);

      if (resetError) {

        // Continuar mesmo com erro
      }

      // Depois, definir a visualização selecionada como padrão
      const { error: updateError } = await serviceClient
        .from('visualizacoes')
        .update({ is_default: true })
        .eq('id', id);

      if (updateError) {
        return NextResponse.json({ error: `Erro ao definir visualização como padrão: ${updateError.message}` }, { status: 500 });
      }

      return NextResponse.json({ success: true, method: 'direct' });
    } catch (directUpdateError: any) {
      return NextResponse.json({ error: `Erro na atualização direta: ${directUpdateError.message}` }, { status: 500 });
    }
  } catch (error: any) {
    
    return NextResponse.json({ error: `Erro interno do servidor: ${error.message}` }, { status: 500 });
  }
}