import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';

export const dynamic = 'force-dynamic';

// GET /api/contatos - Lista todos os contatos
export async function GET() {
  try {
    const supabase = await getSupabaseRouteClient();

    const { data, error } = await supabase
      .from('contatos')
      .select('*')
      .order('nome_empresa', { ascending: true });

    if (error) {
      console.error('Erro ao buscar contatos:', error);
      return NextResponse.json({ error: 'Erro ao buscar contatos' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Erro interno ao buscar contatos:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// POST /api/contatos - Cria um novo contato
export async function POST(request: NextRequest) {
  try {
    const supabase = await getSupabaseRouteClient();
    const body = await request.json();

    const { data, error } = await supabase
      .from('contatos')
      .insert(body)
      .select()
      .single();

    if (error) {
      console.error('Erro ao criar contato:', error);
      return NextResponse.json({ error: 'Erro ao criar contato' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Erro interno ao criar contato:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}