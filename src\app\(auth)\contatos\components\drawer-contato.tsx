'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>etT<PERSON><PERSON> } from '@/components/ui/sheet';
import { FormContato } from './form-contato';
import { FormContatoEdit } from './form-contato-edit';
import { Button } from '@/components/ui/button';
import { Edit2 } from 'lucide-react';
import { Contato } from '@/types/contatos';

interface DrawerContatoProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contatoId?: string | null;
  onContatoSaved?: () => void;
  onSuccess?: () => Promise<void>;
}

export function DrawerContato({ open, onOpenChange, contatoId, onContatoSaved, onSuccess }: DrawerContatoProps) {
  const [contato, setContato] = useState<Contato | null>(null);
  const [modo, setModo] = useState<'criar' | 'editar' | 'visualizar'>('criar');

  const loadContato = async (id: string) => {
    try {
      const response = await fetch(`/api/contatos?id=${id}`);
      if (response.ok) {
        const data = await response.json();
        setContato(data);
      }
    } catch (error) {
      
    }
  };

  useEffect(() => {
    if (contatoId) {
      setModo('visualizar');
      loadContato(contatoId);
    } else {
      setModo('criar');
      setContato(null);
    }
  }, [contatoId]);

  const handleContatoSaved = async () => {
    onContatoSaved?.();
    if (onSuccess) {
      await onSuccess();
    }
    onOpenChange(false);
  };

  const handleEdit = () => {
    setModo('editar');
  };

  const handleCancelEdit = () => {
    setModo('visualizar');
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-[600px] overflow-y-auto">
        <SheetHeader>
          <div className="flex items-center justify-between">
            <SheetTitle>
              {modo === 'criar' && 'Novo Contato'}
              {modo === 'editar' && 'Editar Contato'}
              {modo === 'visualizar' && (contato?.nome_empresa || 'Contato')}
            </SheetTitle>
            {modo === 'visualizar' && contato && (
              <Button onClick={handleEdit} variant="outline" size="sm">
                <Edit2 className="h-4 w-4 mr-2" />
                Editar
              </Button>
            )}
          </div>
        </SheetHeader>

        <div className="mt-6">
          {modo === 'criar' && (
            <FormContato 
              onSuccess={handleContatoSaved}
              onCancel={handleCancel}
            />
          )}
          {modo === 'editar' && contato && (
            <FormContatoEdit 
              contato={contato} 
              onSuccess={handleContatoSaved}
              onCancel={handleCancelEdit}
            />
          )}
          {modo === 'visualizar' && contato && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Nome/Empresa</h4>
                  <p className="text-sm">{contato.nome_empresa}</p>
                </div>
                {contato.nome_razao && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Razão Social</h4>
                    <p className="text-sm">{contato.nome_razao}</p>
                  </div>
                )}
                {contato.nome_contato && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Nome Contato</h4>
                    <p className="text-sm">{contato.nome_contato}</p>
                  </div>
                )}
                {contato.cpf_cnpj && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">CPF/CNPJ</h4>
                    <p className="text-sm">{contato.cpf_cnpj}</p>
                  </div>
                )}
                {contato.email && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Email</h4>
                    <p className="text-sm">{contato.email}</p>
                  </div>
                )}
                {contato.telefone && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Telefone</h4>
                    <p className="text-sm">{contato.telefone}</p>
                  </div>
                )}
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Tipo</h4>
                  <p className="text-sm">{contato.tipo}</p>
                </div>
                {contato.tipo_pessoa && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Tipo Pessoa</h4>
                    <p className="text-sm">{contato.tipo_pessoa}</p>
                  </div>
                )}
                {contato.chave_pix && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Chave PIX</h4>
                    <p className="text-sm">{contato.chave_pix}</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}