-- Script SQL para inserção dos lançamentos
-- Gerado automaticamente a partir do arquivo Excel

BEGIN;

-- Lançamento 1: Telha metálica
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 2231, '2025-04-02', 'cartao', 'Telha metálica', NULL, 'Fornecedor: TOP TELHA METÁLICA INDUSTRIA, CO.', 'concluido')
RETURNING id INTO @lancamento_id_0;

-- Parcelas do lançamento 1
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_0, 45748, 557.75, '2025-04-02', 'pago');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_0, 45749, 557.75, '2025-05-02', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_0, 45750, 557.75, '2025-06-02', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_0, 45751, 557.75, '2025-07-02', 'pendente');

-- Lançamento 2: Visitas técnicas (terraplanagem + brocas)
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 1200, '2025-04-06', 'pix', 'Visitas técnicas (terraplanagem + brocas)', NULL, 'Fornecedor: Hélder Alexandre dos Santos Bettin', 'concluido')
RETURNING id INTO @lancamento_id_1;

-- Parcelas do lançamento 2
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_1, 1, 1200, '2025-04-06', 'pago');

-- Lançamento 3: Construção de muros de arrimo e divisa
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 35165.2, '2025-03-27', 'pix', 'Construção de muros de arrimo e divisa', NULL, 'Fornecedor: Villa Engenharia', 'concluido')
RETURNING id INTO @lancamento_id_2;

-- Parcelas do lançamento 3
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_2, 45689, 17582.6, '2025-03-27', 'pago');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_2, 45690, 17582.6, 'null', 'pendente');

-- Lançamento 4: Materiais p/ fundação (areia, pedra, cimento, arames)
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 3052.03, '2025-03-18', 'outros', 'Materiais p/ fundação (areia, pedra, cimento, arames)', NULL, 'Fornecedor: RD Godoy Materiais para Construções LTDA', 'concluido')
RETURNING id INTO @lancamento_id_3;

-- Parcelas do lançamento 4
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_3, 1, 3052.03, '2025-03-18', 'pago');

-- Lançamento 5: Madeiras, pregos e tapume ecológico
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 4597.9, '2025-03-17', 'outros', 'Madeiras, pregos e tapume ecológico', NULL, 'Fornecedor: ITA Madeiras', 'concluido')
RETURNING id INTO @lancamento_id_4;

-- Parcelas do lançamento 5
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_4, 1, 4597.9, '2025-03-17', 'pago');

-- Lançamento 6: Terraplanagem (limpeza camada vegetal + corte do platô)
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 4500, '2025-03-17', 'outros', 'Terraplanagem (limpeza camada vegetal + corte do platô)', NULL, 'Fornecedor: Prestador não identificado', 'concluido')
RETURNING id INTO @lancamento_id_5;

-- Parcelas do lançamento 6
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_5, 1, 4500, '2025-03-17', 'pago');

-- Lançamento 7: Poste padrão + cavalete de água + caixa de esgoto
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 3250, '2025-04-01', 'cartao', 'Poste padrão + cavalete de água + caixa de esgoto', NULL, 'Fornecedor: RG de Jesus Comércio de Poste Padrão Ltda', 'concluido')
RETURNING id INTO @lancamento_id_6;

-- Parcelas do lançamento 7
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_6, 45931, 325, '2025-04-01', 'pago');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_6, 45932, 325, '2025-05-01', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_6, 45933, 325, '2025-06-01', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_6, 45934, 325, '2025-07-01', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_6, 45935, 325, '2025-08-01', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_6, 45936, 325, '2025-09-01', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_6, 45937, 325, '2025-10-01', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_6, 45938, 325, '2025-11-01', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_6, 45939, 325, '2025-12-01', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_6, 45940, 325, '2026-01-01', 'pendente');

-- Lançamento 8: Armação sob medida (estacas) – Proposta 1
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 2920, '2025-04-02', 'outros', 'Armação sob medida (estacas) – Proposta 1', NULL, 'Fornecedor: Cosest Comércio de Ferro e Aço LTDA', 'concluido')
RETURNING id INTO @lancamento_id_7;

-- Parcelas do lançamento 8
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_7, 45689, 1460, '2025-04-17', 'pago');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_7, 45690, 1460, '2025-05-02', 'pendente');

-- Lançamento 9: Armação sob medida (estacas) – Proposta 2
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 6600, '2025-04-02', 'outros', 'Armação sob medida (estacas) – Proposta 2', NULL, 'Fornecedor: Cosest Comércio de Ferro e Aço LTDA', 'concluido')
RETURNING id INTO @lancamento_id_8;

-- Parcelas do lançamento 9
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_8, 45689, 3300, '2025-04-17', 'pago');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_8, 45690, 3300, '2025-05-02', 'pendente');

-- Lançamento 10: Perfuração de 705m + mobilização
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 11175, '2025-04-01', 'pix', 'Perfuração de 705m + mobilização', NULL, 'Fornecedor: Reigani Serviços de Terraplenagem EIRELI', 'concluido')
RETURNING id INTO @lancamento_id_9;

-- Parcelas do lançamento 10
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_9, 1, 11175, '2025-04-01', 'pago');

-- Lançamento 11: Pontaletes p/ gabarito da casa (30 und cortados)
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 500, '2025-04-01', 'outros', 'Pontaletes p/ gabarito da casa (30 und cortados)', NULL, 'Fornecedor: ITA Madeiras', 'concluido')
RETURNING id INTO @lancamento_id_10;

-- Parcelas do lançamento 11
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_10, 1, 500, '2025-04-01', 'pago');

-- Lançamento 12: Limpeza de terra (finalização)
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 1500, '2025-04-01', 'pix', 'Limpeza de terra (finalização)', NULL, 'Fornecedor: Johnny Queiroz Ferreira', 'concluido')
RETURNING id INTO @lancamento_id_11;

-- Parcelas do lançamento 12
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_11, 1, 1500, '2025-04-01', 'pago');

-- Lançamento 13: Câmera de segurança externa (A28B)
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 153.44, '2025-04-09', 'cartao', 'Câmera de segurança externa (A28B)', NULL, 'Fornecedor: BJ GO ECOM LTDA', 'concluido')
RETURNING id INTO @lancamento_id_12;

-- Parcelas do lançamento 13
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_12, 45689, 76.72, '2025-04-09', 'pago');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_12, 45690, 76.72, '2025-05-09', 'pendente');

-- Lançamento 14: Cartão MicroSD 128GB para câmera
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 89.7, '2025-04-09', 'cartao', 'Cartão MicroSD 128GB para câmera', NULL, 'Fornecedor: M Rock Comércio Eletrônico LTDA', 'concluido')
RETURNING id INTO @lancamento_id_13;

-- Parcelas do lançamento 14
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_13, 1, 89.7, '2025-04-09', 'pago');

-- Lançamento 15: Concretagem (35,5m³ - FCK 25MPa)
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 15620.01, '2025-04-04', 'cartao', 'Concretagem (35,5m³ - FCK 25MPa)', NULL, 'Fornecedor: Capital Mix Concreto LTDA', 'concluido')
RETURNING id INTO @lancamento_id_14;

-- Parcelas do lançamento 15
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_14, 45717, 5206.67, '2025-04-10', 'pago');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_14, 45718, 5206.67, '2025-05-10', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_14, 45719, 5206.67, '2025-06-10', 'pendente');

-- Lançamento 16: Blocos e canaletas 19/19/39
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 9200, 'null', 'cartao', 'Blocos e canaletas 19/19/39', NULL, 'Fornecedor: C&S Blocos Itatiba', 'em_aberto')
RETURNING id INTO @lancamento_id_15;

-- Parcelas do lançamento 16
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_15, 1, 9200, 'null', 'pendente');

-- Lançamento 17: Locação de betoneira 400L
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 1500, '2025-04-11', 'boleto', 'Locação de betoneira 400L', NULL, 'Fornecedor: LocaTudo Locadora', 'em_aberto')
RETURNING id INTO @lancamento_id_16;

-- Parcelas do lançamento 17
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_16, 45778, 300, '2025-05-09', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_16, 45779, 300, '2025-06-06', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_16, 45780, 300, '2025-07-04', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_16, 45781, 300, '2025-08-01', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_16, 45782, 300, '2025-08-29', 'pendente');

-- Lançamento 18: Blocos BV19 e canaletas C19
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 9198, '2025-04-14', 'outros', 'Blocos BV19 e canaletas C19', NULL, 'Fornecedor: C&S Blocos Itatiba', 'concluido')
RETURNING id INTO @lancamento_id_17;

-- Parcelas do lançamento 18
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_17, 1, 3066, 'null', 'pago');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_17, 2, 3066, 'null', 'pendente');
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_17, 3, 3066, 'null', 'pendente');

-- Lançamento 19: Materiais diversos de apoio (cal, fita, mangueira etc.)
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 197.3, '2025-04-14', 'outros', 'Materiais diversos de apoio (cal, fita, mangueira etc.)', NULL, 'Fornecedor: RD Godoy Materiais para Construções LTDA', 'em_aberto')
RETURNING id INTO @lancamento_id_18;

-- Parcelas do lançamento 19
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_18, 1, 197.3, '2025-04-14', 'pendente');

-- Lançamento 20: Mangueira 30m + corrente + cadeado
INSERT INTO lancamentos (obra_id, tipo_lancamento, valor_total, data_competencia, forma_pagamento, descricao, contato_id, observacoes, status)
VALUES ('6fac8ae8-3dac-41d2-83c6-fc71c86e2dfe', 'terceiros', 187.8, '2025-04-11', 'outros', 'Mangueira 30m + corrente + cadeado', NULL, 'Fornecedor: RD Godoy Materiais para Construções LTDA', 'em_aberto')
RETURNING id INTO @lancamento_id_19;

-- Parcelas do lançamento 20
INSERT INTO parcelas (lancamento_id, numero, valor, vencimento, status)
VALUES (@lancamento_id_19, 1, 187.8, 'null', 'pendente');

COMMIT;
