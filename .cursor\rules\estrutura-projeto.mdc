---
description: Estrutura do projeto completa
globs: 
alwaysApply: false
---
# Estrutura do Projeto

## Visão Geral
Este projeto é uma aplicação de Controle Financeiro para Obras usando Next.js, React, TypeScript, Tailwind CSS e Supabase.

## Estrutura de Diretórios

### Frontend (Next.js App Router)
- `/src/app`: Rotas e páginas da aplicação usando App Router
  - `/(auth)`: Rotas autenticadas
    - `/lancamentos`: Módulo de lançamentos financeiros
    - `/contas`: Gerenciamento de contas
    - `/contatos`: Cadastro de contatos/fornecedores
    - `/contratos`: Contratos e acordos
    - `/obras`: Cadastro e gestão de obras
    - `/parcelas`: Administração de parcelas
  - `/api`: Endpoints da API
    - `/lancamentos`: APIs para gerenciar lançamentos
    - `/parcelas`: APIs para parcelas
    - `/import-lancamentos`: Importação de dados

### Componentes e UI
- `/src/components`: Componentes reutilizáveis
  - `/ui`: Componentes de interface Shadcn/UI
  - `/Layout`: Componentes de layout (Sidebar, MainAppLayout)
  - `/Button`: Componentes de botão personalizados

### Outros Diretórios
- `/src/contexts`: Contextos React (exemplo: SidebarContext)
- `/src/hooks`: Hooks personalizados
- `/src/services`: Serviços para comunicação com o backend
- `/src/types`: Tipos e interfaces TypeScript
- `/src/lib`: Utilitários e bibliotecas
  - `/supabase`: Configuração e clientes Supabase

### Banco de Dados
- `/supabase`: Configuração do Supabase
  - `/migrations`: Migrações de banco de dados
    - Estrutura inicial: tabelas principais
    - Políticas de segurança RLS
    - Funções e gatilhos

## Componentes de UI Base
O projeto utiliza [shadcn/ui](mdc:src/components/ui) para componentes de interface, com personalizações específicas para o projeto.

## Autenticação
A autenticação é gerenciada pelo Supabase Auth, implementada através do middleware e rotas API.

## Observações Importantes
- O projeto utiliza Row Level Security (RLS) para a maioria das tabelas, exceto `visualizacoes`
- As API routes usam `createRouteHandlerClient` do Supabase para autenticação
- O frontend usa `createClientComponentClient` para interações com Supabase

## Organização de Diretórios

O projeto segue a arquitetura de diretórios do Next.js com App Router:

- **src/app**: Contém as páginas e rotas do aplicativo
  - **(auth)**: Páginas protegidas que requerem autenticação
    - **configuracoes/**: Configurações do sistema
    - **contas/**: Gerenciamento de contas financeiras
    - **contatos/**: Gerenciamento de contatos (clientes, fornecedores)
    - **contratos/**: Gestão de contratos
    - **lancamentos/**: Lançamentos financeiros
    - **obras/**: Gestão de obras/projetos
    - **parcelas/**: Controle de parcelas de pagamento
  - **api/**: Endpoints da API
  - **login/**: Página de login e autenticação

- **src/components**: Componentes reutilizáveis
  - **Button/**: Componentes de botão
  - **Layout/**: Componentes de layout (Header, Sidebar, etc.)
  - **ui/**: Componentes de UI básicos (shadcn/ui)

- **src/contexts**: Contextos do React
- **src/hooks**: Custom hooks React
- **src/lib**: Bibliotecas e utilidades
  - **supabase/**: Cliente e configuração do Supabase
- **src/services**: Serviços que interagem com API/backend
- **src/types**: Definições de tipos TypeScript

- **supabase/**: Configuração e migrações do Supabase
  - **migrations/**: Scripts SQL para migrações do banco de dados

## Padrões de Arquivos

Cada módulo dentro de `(auth)/` geralmente contém:

1. Um arquivo `page.tsx` que define a página principal 
2. Uma pasta `components/` com os componentes específicos daquele módulo

Exemplo para o módulo de contatos:
```
src/app/(auth)/contatos/
  ├── page.tsx                    # Página principal
  └── components/                 # Componentes específicos
      ├── drawer-contato.tsx      # Drawer para criar contato
      ├── editar-contato.tsx      # Dialog para editar contato 
      ├── form-contato.tsx        # Formulário de criação
      ├── form-contato-edit.tsx   # Formulário de edição
      └── lista-contatos.tsx      # Tabela de listagem
```

## Convenções de Nomenclatura

- Páginas: PascalCase para componentes exportados (ex: `export default function ContatosPage()`)
- Componentes: PascalCase tanto para arquivos quanto para nomes de função (ex: `DrawerContato`)
- Hooks e serviços: camelCase com prefixo adequado (ex: `useContatosService()`)
- Tipos: PascalCase para interfaces e tipos, geralmente no singular (ex: `Contato`)
- DTOs: PascalCase com sufixo DTO (ex: `CreateContatoDTO`)

## Banco de Dados

Utilizamos o Supabase como banco de dados, com as tabelas:
- **contatos**: Clientes, fornecedores e prestadores
- **obras**: Projetos/obras em andamento
- **lancamentos**: Transações financeiras
- **parcelas**: Parcelas de pagamento
- **categorias**: Categorias para lançamentos

O sistema utiliza RLS (Row Level Security) do Postgres para segurança.

