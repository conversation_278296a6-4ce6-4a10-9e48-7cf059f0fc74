'use client'

import { useState, useEffect } from 'react'
import FiltrosAvancados from './filtros-avancados'
import FiltrosAtivos from './filtros-ativos'
import VisualizacoesDropdown from './visualizacoes-dropdown'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from 'sonner'
import { Visualizacao, useVisualizacoesService } from './visualizacoes-service'
import { Filter, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { supabase } from '@/lib/supabase/client'

interface FiltrosContainerProps {
  projetos: string[]
  fornecedores: string[]
  statusOptions: { value: string; label: string }[]
}

export default function FiltrosContainer({
  projetos,
  fornecedores,
  statusOptions
}: FiltrosContainerProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  // Estado para os filtros
  const [filtros, setFiltros] = useState<any>({})
  const [filtrosAvancadosOpen, setFiltrosAvancadosOpen] = useState(false)

  // Estado para as visualizações
  const [visualizacoes, setVisualizacoes] = useState<Visualizacao[]>([])
  const [isLoadingVisualizacoes, setIsLoadingVisualizacoes] = useState(true)
  const [visualizacaoAtiva, setVisualizacaoAtiva] = useState<Visualizacao | null>(null)
  const [userId, setUserId] = useState<string | null>(null)
  const [visualizacaoManualmenteLimpa, setVisualizacaoManualmenteLimpa] = useState(false)

  // Contador de filtros ativos
  const activeFiltersCount = Object.keys(filtros).length

  // Usar o hook do serviço de visualizações
  const visualizacoesService = useVisualizacoesService();

  // Carregar visualizações usando o serviço simplificado
  const carregarVisualizacoes = async () => {
    setIsLoadingVisualizacoes(true)
    try {
      // Usar o serviço para buscar todas as visualizações
      const visualizacoesData = await visualizacoesService.buscarTodasVisualizacoes();

      if (visualizacoesData.length > 0) {
        setVisualizacoes(visualizacoesData);
      } else {
        setVisualizacoes([]);
      }
    } catch (error) {
      toast.error('Não foi possível carregar as visualizações')
      setVisualizacoes([]);
    } finally {
      setIsLoadingVisualizacoes(false)
    }
  }

  // Obter o ID do usuário ao montar o componente
  useEffect(() => {
    async function getUserId() {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) {
          
          return;
        }

        if (session?.user) {
          setUserId(session.user.id);
        }
      } catch (error) {
        
      }
    }

    getUserId();
  }, []);

  // Carregar visualizações ao montar o componente (apenas uma vez)
  useEffect(() => {
    carregarVisualizacoes()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Carregar visualização padrão se não houver filtros na URL
  useEffect(() => {
    // Só carregar a visualização padrão se não houver filtros na URL e se tivermos visualizações carregadas
    // E se a visualização não foi manualmente limpa
    const temFiltrosNaUrl = Array.from(searchParams.keys()).some(key => key.startsWith('filtro_'));

    // Limpar visualização ativa se houver filtros na URL
    if (temFiltrosNaUrl && visualizacaoAtiva) {
      setVisualizacaoAtiva(null);
      if (typeof window !== 'undefined') {
        window.localStorage.removeItem('visualizacao_lancamentos_ativa');
        window.localStorage.removeItem('visualizacao_lancamentos_ativa_when_collapsed');
      }
    }

    if (!temFiltrosNaUrl && visualizacoes.length > 0 && userId && !visualizacaoManualmenteLimpa) {
      // Verificar se já temos uma visualização ativa no localStorage
      if (typeof window !== 'undefined') {
        const idSalvo = window.localStorage.getItem('visualizacao_lancamentos_ativa');
        if (idSalvo) {
          const vSalva = visualizacoes.find(v => v.id === idSalvo);
          if (vSalva) {
            setVisualizacaoAtiva(vSalva);
            setFiltros(vSalva.filtros_json || {});
            return;
          }
        }
      }

      // Se não tiver no localStorage, procurar a visualização padrão
      const visualizacaoPadrao = visualizacoes.find(v => v.is_default);
      if (visualizacaoPadrao) {
        setVisualizacaoAtiva(visualizacaoPadrao);
        setFiltros(visualizacaoPadrao.filtros_json || {});

        // Salvar no localStorage também
        if (typeof window !== 'undefined') {
          window.localStorage.setItem('visualizacao_lancamentos_ativa', visualizacaoPadrao.id);
        }
      }
    }
  }, [visualizacoes, userId, searchParams, visualizacaoManualmenteLimpa, visualizacaoAtiva]);

  // Atualizar a URL com os filtros
  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString())

    // Limpar parâmetros de filtro existentes
    const keysToRemove = []
    for (const key of params.keys()) {
      if (key.startsWith('filtro_')) {
        keysToRemove.push(key)
      }
    }

    keysToRemove.forEach(key => params.delete(key))

    // Adicionar novos parâmetros de filtro
    Object.entries(filtros).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach((v: any) => {
            params.append(`filtro_${key}`, encodeURIComponent(v))
          })
        } else if (value instanceof Date) {
          params.set(`filtro_${key}`, value.toISOString())
        } else {
          params.set(`filtro_${key}`, encodeURIComponent(String(value)))
        }
      }
    })

    // Atualizar a URL
    const newUrl = params.toString() ? `?${params.toString()}` : ''
    router.push(`/lancamentos${newUrl}`, { scroll: false })
  }, [filtros, router, searchParams])

  // Carregar filtros da URL ao montar o componente
  useEffect(() => {
    const filtrosFromUrl: any = {}

    for (const [key, value] of searchParams.entries()) {
      if (key.startsWith('filtro_')) {
        const filtroKey = key.replace('filtro_', '')

        if (key.includes('data')) {
          filtrosFromUrl[filtroKey] = new Date(value)
        } else if (searchParams.getAll(key).length > 1) {
          // Decodificar cada valor em arrays
          filtrosFromUrl[filtroKey] = searchParams.getAll(key).map(v => decodeURIComponent(v))
        } else if (filtroKey === 'status') {
          // Garantir que status seja sempre um array
          filtrosFromUrl[filtroKey] = [decodeURIComponent(value)]
        } else {
          filtrosFromUrl[filtroKey] = decodeURIComponent(value)
        }
      }
    }

    if (Object.keys(filtrosFromUrl).length > 0) {
      setFiltros(filtrosFromUrl)
    }
  }, [searchParams])

  // Manipular mudanças nos filtros
  const handleFiltrosChange = (novosFiltros: any) => {
    setFiltros(novosFiltros)
  }

  // Manipular seleção de visualização
  const handleVisualizacaoSelect = (filtrosJson: any) => {
    // Garantir que filtrosJson seja um objeto válido, mesmo que vazio
    if (!filtrosJson || typeof filtrosJson !== 'object') {
      toast.warning('Esta visualização contém filtros em formato inválido')
      return
    }

    setFiltros(filtrosJson)

    // Mensagem diferente dependendo se há filtros ou não
    if (Object.keys(filtrosJson).length === 0) {
      toast.success('Visualização sem filtros aplicada')
    } else {
      toast.success('Visualização aplicada')
    }
  }

  // Manipular criação de visualização
  const handleVisualizacaoCriada = (novaVisualizacao: any) => {
    carregarVisualizacoes();
  }

  return (
    <div className="flex flex-col gap-3 mb-4">
      <div className="flex flex-wrap items-center gap-2">
        <div className="flex items-center gap-2">
          <VisualizacoesDropdown
            visualizacoes={visualizacoes}
            isLoading={isLoadingVisualizacoes}
            onSelect={handleVisualizacaoSelect}
            onRefreshNeeded={carregarVisualizacoes}
            filtrosAtuais={filtros}
            onVisualizacaoCriada={handleVisualizacaoCriada}
            visualizacaoAtiva={visualizacaoAtiva}
            setVisualizacaoAtiva={setVisualizacaoAtiva}
            setVisualizacaoManualmenteLimpa={setVisualizacaoManualmenteLimpa}
          />

          {visualizacaoAtiva && (
            <Button
              variant="ghost"
              size="sm"
              className="h-9 px-2 text-xs"
              onClick={() => {
                // Limpar a visualização ativa
                setVisualizacaoAtiva(null);

                // Marcar como manualmente limpa para evitar recarregar a visualização padrão
                setVisualizacaoManualmenteLimpa(true);

                // Limpar os filtros
                setFiltros({});

                // Remover do localStorage
                if (typeof window !== 'undefined') {
                  window.localStorage.removeItem('visualizacao_lancamentos_ativa');
                  window.localStorage.removeItem('visualizacao_lancamentos_ativa_when_collapsed');
                }

                toast.success('Visualização limpa');
              }}
            >
              <X className="h-3.5 w-3.5 mr-1" />
              Limpar visualização
            </Button>
          )}
        </div>

        <Popover open={filtrosAvancadosOpen} onOpenChange={setFiltrosAvancadosOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className={cn(
                "h-9 px-3 gap-1 border border-gray-300 rounded-md bg-white",
                activeFiltersCount > 0 && "border-gray-300"
              )}
            >
              <Filter className="h-4 w-4" />
              <span>Filtros</span>
              {activeFiltersCount > 0 && (
                <Badge variant="secondary" className="ml-1 h-5 px-1.5 bg-gray-100 text-gray-800">
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="rounded-md border shadow-md p-0 min-w-[500px] max-w-[600px] w-full overflow-visible bg-white z-50"
            side="bottom"
            align="start"
            sideOffset={5}
          >
            <FiltrosAvancados
              projetos={projetos}
              fornecedores={fornecedores}
              statusOptions={statusOptions}
              initialValues={filtros}
              onChange={handleFiltrosChange}
              onSaveView={undefined}
              isPopoverContent={true}
            />
          </PopoverContent>
        </Popover>

        {/* Filtros Ativos como chips interativos */}
        <FiltrosAtivos
          filtros={filtros}
          projetos={projetos}
          fornecedores={fornecedores}
          statusOptions={statusOptions}
          onChange={handleFiltrosChange}
        />
      </div>
    </div>
  )
}