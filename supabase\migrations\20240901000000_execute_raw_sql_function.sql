-- Função para executar SQL bruto com segurança
-- Esta função é usada como último recurso para operações que falham pelos métodos normais
-- Deve ser usada com cuidado, pois permite a execução de SQL arbitrário
CREATE OR REPLACE FUNCTION execute_raw_sql(sql TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Executar o SQL fornecido
  EXECUTE sql;
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Erro ao executar SQL: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- Conceder permissões para usuários autenticados
GRANT EXECUTE ON FUNCTION execute_raw_sql(TEXT) TO authenticated;

COMMENT ON FUNCTION execute_raw_sql(TEXT) IS 'Função para executar SQL bruto como último recurso para operações que falham pelos métodos normais. Deve ser usada com cuidado.';
