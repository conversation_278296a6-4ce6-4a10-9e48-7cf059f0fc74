-- Função para buscar documentos com filtros
CREATE OR REPLACE FUNCTION buscar_documentos(
  p_status TEXT DEFAULT NULL,
  p_data_inicio DATE DEFAULT NULL,
  p_data_fim DATE DEFAULT NULL,
  p_fornecedor TEXT DEFAULT NULL,
  p_valor_min NUMERIC DEFAULT NULL,
  p_valor_max NUMERIC DEFAULT NULL,
  p_user_id UUID DEFAULT NULL
)
RETURNS SETOF documentos
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM documentos
  WHERE
    (p_status IS NULL OR status = p_status) AND
    (p_data_inicio IS NULL OR created_at::date >= p_data_inicio) AND
    (p_data_fim IS NULL OR created_at::date <= p_data_fim) AND
    (p_fornecedor IS NULL OR fornecedor ILIKE '%' || p_fornecedor || '%') AND
    (p_valor_min IS NULL OR valor_total >= p_valor_min) AND
    (p_valor_max IS NULL OR valor_total <= p_valor_max) AND
    (p_user_id IS NULL OR user_id = p_user_id)
  ORDER BY created_at DESC;
END;
$$;

-- Comentário para a função
COMMENT ON FUNCTION buscar_documentos IS 'Busca documentos com filtros opcionais por status, data, fornecedor, valor e usuário';

-- Função para obter estatísticas de documentos
CREATE OR REPLACE FUNCTION estatisticas_documentos(
  p_user_id UUID DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_pendentes INTEGER;
  v_aprovados INTEGER;
  v_rejeitados INTEGER;
  v_valor_pendentes NUMERIC;
  v_valor_aprovados NUMERIC;
  v_valor_rejeitados NUMERIC;
  v_vencidos INTEGER;
  v_proximos_vencimentos INTEGER;
  v_resultado JSONB;
BEGIN
  -- Filtro de usuário
  v_pendentes := (
    SELECT COUNT(*)
    FROM documentos
    WHERE status = 'pendente'
    AND (p_user_id IS NULL OR user_id = p_user_id)
  );
  
  v_aprovados := (
    SELECT COUNT(*)
    FROM documentos
    WHERE status = 'aprovado'
    AND (p_user_id IS NULL OR user_id = p_user_id)
  );
  
  v_rejeitados := (
    SELECT COUNT(*)
    FROM documentos
    WHERE status = 'rejeitado'
    AND (p_user_id IS NULL OR user_id = p_user_id)
  );
  
  v_valor_pendentes := (
    SELECT COALESCE(SUM(valor_total), 0)
    FROM documentos
    WHERE status = 'pendente'
    AND valor_total IS NOT NULL
    AND (p_user_id IS NULL OR user_id = p_user_id)
  );
  
  v_valor_aprovados := (
    SELECT COALESCE(SUM(valor_total), 0)
    FROM documentos
    WHERE status = 'aprovado'
    AND valor_total IS NOT NULL
    AND (p_user_id IS NULL OR user_id = p_user_id)
  );
  
  v_valor_rejeitados := (
    SELECT COALESCE(SUM(valor_total), 0)
    FROM documentos
    WHERE status = 'rejeitado'
    AND valor_total IS NOT NULL
    AND (p_user_id IS NULL OR user_id = p_user_id)
  );
  
  -- Documentos com vencimento nos próximos 7 dias
  v_proximos_vencimentos := (
    SELECT COUNT(*)
    FROM documentos
    WHERE status = 'pendente'
    AND data_vencimento IS NOT NULL
    AND data_vencimento BETWEEN CURRENT_DATE AND (CURRENT_DATE + INTERVAL '7 days')
    AND (p_user_id IS NULL OR user_id = p_user_id)
  );
  
  -- Documentos vencidos
  v_vencidos := (
    SELECT COUNT(*)
    FROM documentos
    WHERE status = 'pendente'
    AND data_vencimento IS NOT NULL
    AND data_vencimento < CURRENT_DATE
    AND (p_user_id IS NULL OR user_id = p_user_id)
  );
  
  -- Construir o resultado
  v_resultado := jsonb_build_object(
    'pendentes', jsonb_build_object(
      'quantidade', v_pendentes,
      'valor_total', v_valor_pendentes
    ),
    'aprovados', jsonb_build_object(
      'quantidade', v_aprovados,
      'valor_total', v_valor_aprovados
    ),
    'rejeitados', jsonb_build_object(
      'quantidade', v_rejeitados,
      'valor_total', v_valor_rejeitados
    ),
    'vencidos', jsonb_build_object(
      'quantidade', v_vencidos
    ),
    'proximos_vencimentos', jsonb_build_object(
      'quantidade', v_proximos_vencimentos
    )
  );
  
  RETURN v_resultado;
END;
$$;

-- Comentário para a função
COMMENT ON FUNCTION estatisticas_documentos IS 'Retorna estatísticas sobre documentos: pendentes, aprovados, rejeitados, vencidos e próximos vencimentos';
