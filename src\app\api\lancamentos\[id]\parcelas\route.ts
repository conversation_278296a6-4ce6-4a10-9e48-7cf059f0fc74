import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

// Função para criar um cliente Supabase com a chave de serviço
function createServiceClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    return null;
  }

  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

// GET /api/lancamentos/[id]/parcelas
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // Obter o ID da URL diretamente
    const pathParts = request.nextUrl.pathname.split('/');
    const lancamentoId = pathParts[pathParts.length - 2]; // O ID está na penúltima posição da URL

    if (!lancamentoId) {
      return NextResponse.json(
        { error: 'ID do lançamento não fornecido' },
        { status: 400 }
      );
    }

    // Criar cliente Supabase
    const supabase = createServiceClient();
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Erro de configuração do servidor' },
        { status: 500 }
      );
    }

    // Consultar parcelas do lançamento
    const { data, error } = await supabase
      .from('parcelas')
      .select(`
        *,
        lancamentos (
          *,
          contatos ( nome_empresa, chave_pix ),
          obras ( nome )
        )
      `)
      .eq('lancamento_id', lancamentoId)
      .order('numero', { ascending: true });

    if (error) {

      return NextResponse.json(
        { error: 'Erro ao buscar parcelas' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {

    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// POST /api/lancamentos/[id]/parcelas - Atualizar múltiplas parcelas de uma vez
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  let triggerDisabled = false;
  let supabase: any;

  try {
    // Obter o ID da URL diretamente
    const pathParts = request.nextUrl.pathname.split('/');
    const lancamentoId = pathParts[pathParts.length - 2]; // O ID está na penúltima posição da URL

    if (!lancamentoId) {
      return NextResponse.json(
        { error: 'ID do lançamento não fornecido' },
        { status: 400 }
      );
    }

    // Obter os dados da requisição
    const { parcelas } = await request.json();

    if (!parcelas || !Array.isArray(parcelas) || parcelas.length === 0) {
      return NextResponse.json(
        { error: 'Nenhuma parcela fornecida para atualização' },
        { status: 400 }
      );
    }

    // Criar cliente Supabase
    supabase = createServiceClient();
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Erro de configuração do servidor' },
        { status: 500 }
      );
    }

    // Verificar se o lançamento existe
    const { data: lancamento, error: lancamentoError } = await supabase
      .from('lancamentos')
      .select('valor_total')
      .eq('id', lancamentoId)
      .single();

    if (lancamentoError || !lancamento) {

      return NextResponse.json(
        { error: 'Lançamento não encontrado' },
        { status: 404 }
      );
    }

    // Tentar desabilitar o trigger primeiro, antes de qualquer operação
    try {
      await supabase.rpc('disable_parcelas_validation_trigger');
      triggerDisabled = true;

    } catch (triggerError) {
      // Se a RPC falhar, continuamos sem desabilitar o trigger

    }

    // Função para converter valor para número com segurança e precisão
    const converterParaNumero = (valor: any): number => {
      if (typeof valor === 'number') {
        // Garantir precisão de 2 casas decimais para números
        return parseFloat(valor.toFixed(2));
      }

      if (typeof valor === 'string') {
        // Remover todos os caracteres não numéricos exceto ponto e vírgula
        // Tratar valores com vírgula
        const valorLimpo = valor.replace(/[^\d,.]/g, '').replace(',', '.');

        // Converter para número e garantir 2 casas decimais
        const numero = parseFloat(valorLimpo) || 0;
        return parseFloat(numero.toFixed(2));
      }

      return 0;
    };

    // Calcular o valor total das parcelas
    const valorTotalParcelas = parcelas.reduce((total, parcela) => {
      // Converter para número com segurança
      const valor = converterParaNumero(parcela.valor);
      return total + valor;
    }, 0);

    // Arredondar valores para 2 casas decimais para evitar problemas de precisão
    const valorTotalParcelasArredondado = parseFloat(valorTotalParcelas.toFixed(2));
    const valorTotalLancamentoArredondado = parseFloat(lancamento.valor_total.toFixed(2));

    // Calcular a diferença absoluta entre os valores
    const diferencaAbsoluta = Math.abs(valorTotalParcelasArredondado - valorTotalLancamentoArredondado);





    // Verificar se o valor total das parcelas excede o valor total do lançamento com tolerância
    const TOLERANCIA = 2.0; // Aumentar a tolerância para 2 reais para evitar problemas com arredondamento

    // Se a diferença for menor que a tolerância, considera valores iguais
    // Caso contrário, verifica se o valor das parcelas excede o do lançamento
    if (diferencaAbsoluta <= TOLERANCIA) {

    } else if (valorTotalParcelasArredondado > valorTotalLancamentoArredondado) {
      // Habilitar o trigger novamente se foi desabilitado, antes de retornar erro
      if (triggerDisabled) {
        try {
          await supabase.rpc('enable_parcelas_validation_trigger');

        } catch (triggerError) {

        }
      }

      return NextResponse.json(
        {
          error: 'O valor total das parcelas não pode exceder o valor total do lançamento',
          valorTotalParcelas: valorTotalParcelasArredondado,
          valorTotalLancamento: valorTotalLancamentoArredondado
        },
        { status: 400 }
      );
    }

    // Atualizar ou criar as parcelas uma a uma
    const resultados = [];
    const erros = [];

    for (const parcela of parcelas) {
      // Garantir que o valor seja um número válido
      const valorValidado = converterParaNumero(parcela.valor);

      if (valorValidado <= 0) {

        parcela.valor = 0.01;
      } else {
        parcela.valor = valorValidado;
      }

      // Atualizar a parcela
      const { data, error } = await supabase
        .from('parcelas')
        .update({
          valor: parcela.valor,
          vencimento: parcela.vencimento || null,
          status: parcela.status || 'pendente',
          data_pagamento: parcela.data_pagamento || null,
        })
        .eq('id', parcela.id)
        .select();

      if (error) {

        erros.push({ id: parcela.id, erro: error.message });
      } else {
        resultados.push(data);
      }
    }

    // Habilitar o trigger novamente
    if (triggerDisabled) {
      try {
        await supabase.rpc('enable_parcelas_validation_trigger');

      } catch (triggerError) {

      }
    }

    // Se houve erros, retornar informações sobre eles
    if (erros.length > 0) {
      return NextResponse.json(
        {
          message: 'Algumas parcelas não puderam ser atualizadas',
          erros,
          resultados
        },
        { status: 207 } // 207 Multi-Status
      );
    }

    // Se tudo ocorreu bem, retornar sucesso
    return NextResponse.json({
      message: 'Todas as parcelas foram atualizadas com sucesso',
      resultados
    });

  } catch (error: any) {


    // Tenta reabilitar o trigger se necessário
    if (triggerDisabled && supabase) {
      try {
        await supabase.rpc('enable_parcelas_validation_trigger');

      } catch (triggerError) {

      }
    }

    return NextResponse.json(
      {
        error: `Erro interno do servidor: ${error.message || 'Erro desconhecido'}`
      },
      { status: 500 }
    );
  }
}