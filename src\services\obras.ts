'use client';

// Remover import antigo de tipos específicos
// import { Obra, CreateObraDTO, UpdateObraDTO } from '@/types/obras';
import { Database } from "@/types/supabase"; // Importar tipos gerais
// Ajustar import do cliente supabase se necessário (verificar se useSupabase ainda é usado/necessário)
// import { useSupabase } from '@/hooks/useSupabase'; // Comentado por enquanto
import { supabase } from '@/lib/supabase/client'; // Usar cliente compartilhado diretamente

// Usar tipos gerados
type Obra = Database["public"]["Tables"]["obras"]["Row"];
type CreateObraDTO = Database["public"]["Tables"]["obras"]["Insert"];
type UpdateObraDTO = Database["public"]["Tables"]["obras"]["Update"];

export function useObrasService() {
  // const supabase = useSupabase(); // Comentar ou remover se o cliente direto for usado

  return {
    async listarObras(): Promise<Obra[]> {
      const { data, error } = await supabase
        .from('obras')
        .select('*') // Usar * pois temos o tipo Row
        .order('created_at', { ascending: false });

      if (error) {
        
        throw error;
      }
      return data || [];
    },

    async buscarObraPorId(id: string): Promise<Obra | null> {
      const { data, error } = await supabase
        .from('obras')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null;
        }
        
        throw error;
      }
      return data;
    },

    async criarObra(obra: CreateObraDTO): Promise<Obra | null> { // Retornar obra criada
      const { data, error } = await supabase
        .from('obras')
        .insert(obra)
        .select()
        .single();

      if (error) {
        
        throw error;
      }
      return data;
    },

    async atualizarObra(id: string, obra: UpdateObraDTO): Promise<Obra | null> { // Retornar obra atualizada
      const { data, error } = await supabase
        .from('obras')
        .update(obra)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        
        throw error;
      }
      return data;
    },

    async excluirObra(id: string): Promise<void> {
      const { error } = await supabase
        .from('obras')
        .delete()
        .eq('id', id);

      if (error) {
        
        throw error;
      }
    }
  };
}