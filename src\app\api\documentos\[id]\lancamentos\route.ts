import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase/client';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const documentoId = params.id;

    // Buscar lançamentos relacionados ao documento
    const { data: lancamentos, error } = await supabase
      .from('lancamentos')
      .select('id, descricao, status, data_pagamento, valor_total')
      .eq('documento_id', documentoId);

    if (error) {
      console.error('Erro ao buscar lançamentos:', error);
      return NextResponse.json(
        { error: 'Erro ao buscar lançamentos relacionados' },
        { status: 500 }
      );
    }

    return NextResponse.json({ lancamentos: lancamentos || [] });

  } catch (error) {
    console.error('Erro na API de lançamentos do documento:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 