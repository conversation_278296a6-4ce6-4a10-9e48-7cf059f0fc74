import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Criar cliente Supabase com a chave de serviço para contornar o RLS
const createServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    
    return null;
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

// POST /api/documentos/atualizar-obra-sql - Atualiza o campo obra_id usando SQL direto
export async function POST(request: NextRequest) {
  try {
    // Obter parâmetros da URL
    const url = new URL(request.url);
    const id = url.searchParams.get('id');
    const obra_id = url.searchParams.get('obra_id');

    if (!id) {
      return NextResponse.json({ error: 'ID do documento não fornecido' }, { status: 400 });
    }// Criar cliente Supabase com a chave de serviço
    const supabase = createServiceClient();
    if (!supabase) {
      return NextResponse.json({ error: 'Erro ao criar cliente Supabase' }, { status: 500 });
    }

    // Construir a consulta SQL
    const obraIdValue = obra_id === 'null' ? null : obra_id;
    const sqlQuery = `
      UPDATE documentos 
      SET obra_id = ${obraIdValue ? `'${obraIdValue}'` : 'NULL'}, 
          updated_at = NOW() 
      WHERE id = '${id}' 
      RETURNING id, obra_id, updated_at;
    `;// Executar a consulta SQL diretamente
    const { data, error } = await supabase.rpc('execute_sql', {
      query: sqlQuery
    });

    if (error) {

// Tentar uma abordagem alternativa com a API de banco de dados
      try {const { data: queryData, error: queryError } = await supabase
          .from('documentos')
          .update({ 
            obra_id: obraIdValue,
            updated_at: new Date().toISOString()
          })
          .eq('id', id)
          .select();
          
        if (queryError) {
          
          return NextResponse.json({ error: error.message }, { status: 500 });
        }return NextResponse.json(queryData);
      } catch (altError) {
        
        return NextResponse.json({ error: error.message }, { status: 500 });
      }
    }// Verificar se a atualização foi aplicada
    const { data: verificacao, error: verificacaoError } = await supabase
      .from('documentos')
      .select('id, obra_id')
      .eq('id', id)
      .single();

    if (verificacaoError) {
      
    } else {}

    return NextResponse.json(data);
  } catch (error: any) {
    
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}