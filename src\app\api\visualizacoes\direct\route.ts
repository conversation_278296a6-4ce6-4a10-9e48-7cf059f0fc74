import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import {
  directGetAllVisualizacoes,
  directGetVisualizacaoById,
  directUpdateVisualizacao,
  directDeleteVisualizacao,
  checkFunctionsExist
} from './sql-functions';
import { insertVisualizacaoBypassRls, checkInsertFunctionExists } from './insert-function';
import { executeSqlFunction, checkExecuteSqlFunctionExists } from './execute-sql';
import { createAllFunctions } from './create-functions';

// Criar cliente Supabase com service_role para acesso direto ao banco
const createDirectClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl) {
    return null;
  }

  // Se não tiver a chave de serviço, usar a chave anônima como fallback
  const apiKey = supabaseServiceKey || supabaseAnonKey;

  if (!apiKey) {
    return null;
  }

  return createClient(supabaseUrl, apiKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

// Executar SQL diretamente no banco de dados
const executeSql = async (supabase: any, sql: string, params?: any): Promise<any> => {
  try {
    const { data, error } = await supabase.rpc('execute_sql', {
      sql_query: sql,
      params: params || {}
    });

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    throw error;
  }
};

// Verificar e criar funções SQL se necessário
const ensureSqlFunctions = async (supabase: any): Promise<boolean> => {
  try {
    // Verificar se as funções existem
    try {
      const { data, error } = await supabase.rpc('check_functions_exist');

      if (!error && data && data.count !== 4) {
        // Criar as funções de acesso
        try { await supabase.rpc('direct_get_all_visualizacoes'); } catch (e) { /* ignorar */ }
        try { await supabase.rpc('direct_get_visualizacao_by_id'); } catch (e) { /* ignorar */ }
        try { await supabase.rpc('direct_update_visualizacao'); } catch (e) { /* ignorar */ }
        try { await supabase.rpc('direct_delete_visualizacao'); } catch (e) { /* ignorar */ }
      }
    } catch (e) {
      // Ignorar erros ao verificar funções
    }

    // Verificar se a função de inserção existe
    try {
      const { data: insertData, error: insertError } = await supabase.rpc('check_insert_function_exists');

      if (!insertError && insertData && insertData.count !== 1) {
        // Criar a função de inserção
        try { await supabase.rpc('insert_visualizacao_bypass_rls'); } catch (e) { /* ignorar */ }
      }
    } catch (e) {
      // Ignorar erros ao verificar função de inserção
    }

    // Verificar se a função de execução SQL existe
    try {
      const { data: execData, error: execError } = await supabase.rpc('check_execute_sql_function_exists');

      if (!execError && execData && execData.count !== 1) {
        // Criar a função de execução SQL
        try { await supabase.rpc('execute_sql'); } catch (e) { /* ignorar */ }
      }
    } catch (e) {
      // Ignorar erros ao verificar função de execução
    }

    return true;
  } catch (error) {
    return false;
  }
};

// GET /api/visualizacoes/direct - Lista todas as visualizações diretamente
export async function GET(request: NextRequest) {
  try {
    // Obter ID da visualização da query string, se disponível
    const url = new URL(request.url);
    const id = url.searchParams.get('id');
    const createFunctions = url.searchParams.get('create_functions') === 'true';

    // Criar cliente Supabase direto
    const supabase = createDirectClient();
    if (!supabase) {
      return NextResponse.json({ error: 'Erro ao criar cliente Supabase' }, { status: 500 });
    }

    // Se solicitado, criar as funções SQL
    if (createFunctions) {
      await createAllFunctions(supabase);
      return NextResponse.json({ success: true, message: 'Funções SQL criadas com sucesso' });
    }

    // Se tiver ID, buscar visualização específica
    if (id) {
      try {
        // Tentar usar a função RPC
        const { data, error } = await supabase.rpc(
          'direct_get_visualizacao_by_id',
          { p_id: id }
        );

        if (!error && data && data.length > 0) {
          return NextResponse.json(data[0]);
        }

        // Se falhou, tentar acessar diretamente a tabela
        const { data: directData, error: directError } = await supabase
          .from('visualizacoes')
          .select('*')
          .eq('id', id)
          .single();

        if (directError) {
          return NextResponse.json({ error: directError.message }, { status: 500 });
        }

        if (!directData) {
          return NextResponse.json({ error: 'Visualização não encontrada' }, { status: 404 });
        }

        return NextResponse.json(directData);
      } catch (rpcError) {
        // Tentar acessar diretamente a tabela
        const { data: directData, error: directError } = await supabase
          .from('visualizacoes')
          .select('*')
          .eq('id', id)
          .single();

        if (directError) {
          return NextResponse.json({ error: directError.message }, { status: 500 });
        }

        if (!directData) {
          return NextResponse.json({ error: 'Visualização não encontrada' }, { status: 404 });
        }

        return NextResponse.json(directData);
      }
    }

    // Caso contrário, buscar todas as visualizações
    try {
      // Tentar usar a função RPC
      const { data, error } = await supabase.rpc('direct_get_all_visualizacoes');

      if (!error && data) {
        return NextResponse.json(data || []);
      }

      // Se falhou, tentar acessar diretamente a tabela
      const { data: directData, error: directError } = await supabase
        .from('visualizacoes')
        .select('*')
        .order('updated_at', { ascending: false });

      if (directError) {
        return NextResponse.json({ error: directError.message }, { status: 500 });
      }

      return NextResponse.json(directData || []);
    } catch (rpcError) {
      // Tentar acessar diretamente a tabela
      const { data: directData, error: directError } = await supabase
        .from('visualizacoes')
        .select('*')
        .order('updated_at', { ascending: false });

      if (directError) {
        return NextResponse.json({ error: directError.message }, { status: 500 });
      }

      return NextResponse.json(directData || []);
    }
  } catch (error: any) {
    return NextResponse.json({ error: 'Erro interno do servidor: ' + (error?.message || 'desconhecido') }, { status: 500 });
  }
}

// PATCH /api/visualizacoes/direct - Atualiza uma visualização diretamente
export async function PATCH(request: NextRequest) {
  try {
    // Obter o body da requisição
    const body = await request.json();
    const { id, nome, descricao, filtros_json } = body;

    // Validar ID
    if (!id) {
      return NextResponse.json({ error: 'ID da visualização não fornecido' }, { status: 400 });
    }

    // Validar nome
    if (!nome || typeof nome !== 'string' || nome.trim().length === 0) {
      return NextResponse.json({ error: 'Nome é obrigatório' }, { status: 400 });
    }

    // Criar cliente Supabase direto
    const supabase = createDirectClient();
    if (!supabase) {
      return NextResponse.json({ error: 'Erro ao criar cliente Supabase' }, { status: 500 });
    }

    try {
      // Tentar usar a função RPC
      const { data, error } = await supabase.rpc(
        'direct_update_visualizacao',
        {
          p_id: id,
          p_nome: nome,
          p_descricao: descricao || null,
          p_filtros_json: filtros_json || {}
        }
      );

      if (!error && data) {
        return NextResponse.json(data);
      }

      // Se falhou, tentar atualizar diretamente na tabela
      const { data: directData, error: directError } = await supabase
        .from('visualizacoes')
        .update({
          nome,
          descricao: descricao || null,
          filtros_json: filtros_json || {},
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (directError) {
        return NextResponse.json({ error: directError.message }, { status: 500 });
      }

      if (!directData) {
        return NextResponse.json({ error: 'Visualização não encontrada' }, { status: 404 });
      }

      return NextResponse.json(directData);
    } catch (rpcError) {
      // Tentar atualizar diretamente na tabela
      const { data: directData, error: directError } = await supabase
        .from('visualizacoes')
        .update({
          nome,
          descricao: descricao || null,
          filtros_json: filtros_json || {},
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (directError) {
        return NextResponse.json({ error: directError.message }, { status: 500 });
      }

      if (!directData) {
        return NextResponse.json({ error: 'Visualização não encontrada' }, { status: 404 });
      }

      return NextResponse.json(directData);
    }
  } catch (error: any) {
    return NextResponse.json({ error: 'Erro interno do servidor: ' + (error?.message || 'desconhecido') }, { status: 500 });
  }
}

// DELETE /api/visualizacoes/direct - Remove uma visualização diretamente
export async function DELETE(request: NextRequest) {
  try {
    // Obter ID da visualização da query string
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'ID da visualização não fornecido' }, { status: 400 });
    }

    // Criar cliente Supabase direto
    const supabase = createDirectClient();
    if (!supabase) {
      return NextResponse.json({ error: 'Erro ao criar cliente Supabase' }, { status: 500 });
    }

    try {
      // Tentar usar a função RPC
      const { data, error } = await supabase.rpc(
        'direct_delete_visualizacao',
        { p_id: id }
      );

      if (!error && data) {
        return NextResponse.json({ success: true });
      }

      // Se falhou, tentar excluir diretamente da tabela
      // Primeiro, verificar se a visualização existe
      const { data: checkData, error: checkError } = await supabase
        .from('visualizacoes')
        .select('id')
        .eq('id', id)
        .single();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 é "não encontrado"
        return NextResponse.json({ error: checkError.message }, { status: 500 });
      }

      if (!checkData) {
        return NextResponse.json({ error: 'Visualização não encontrada' }, { status: 404 });
      }

      // Excluir a visualização
      const { error: deleteError } = await supabase
        .from('visualizacoes')
        .delete()
        .eq('id', id);

      if (deleteError) {
        return NextResponse.json({ error: deleteError.message }, { status: 500 });
      }

      return NextResponse.json({ success: true });
    } catch (rpcError) {
      // Tentar excluir diretamente da tabela
      const { error: deleteError } = await supabase
        .from('visualizacoes')
        .delete()
        .eq('id', id);

      if (deleteError) {
        return NextResponse.json({ error: deleteError.message }, { status: 500 });
      }

      return NextResponse.json({ success: true });
    }
  } catch (error: any) {
    return NextResponse.json({ error: 'Erro interno do servidor: ' + (error?.message || 'desconhecido') }, { status: 500 });
  }
}

// POST /api/visualizacoes/direct - Cria uma nova visualização diretamente
export async function POST(request: NextRequest) {
  try {
    // Obter o body da requisição
    const body = await request.json();
    const { nome, descricao, filtros_json, user_id, contexto } = body;

    // Validações básicas
    if (!nome || typeof nome !== 'string' || nome.trim().length === 0) {
      return NextResponse.json({ error: 'Nome é obrigatório' }, { status: 400 });
    }

    if (!filtros_json || typeof filtros_json !== 'object') {
      return NextResponse.json({ error: 'Filtros inválidos' }, { status: 400 });
    }

    if (!user_id) {
      return NextResponse.json({ error: 'É necessário estar autenticado para criar uma visualização' }, { status: 401 });
    }

    // Criar cliente Supabase direto
    const supabase = createDirectClient();
    if (!supabase) {
      return NextResponse.json({ error: 'Erro ao criar cliente Supabase' }, { status: 500 });
    }

    try {
      // Tentar primeiro com a nova função direct_insert_visualizacao
      const { data: directInsertData, error: directInsertError } = await supabase.rpc('direct_insert_visualizacao', {
        p_user_id: user_id,
        p_contexto: contexto || 'lancamentos',
        p_nome: nome,
        p_descricao: descricao || null,
        p_filtros_json: filtros_json
      });

      if (!directInsertError && directInsertData) {
        const visualizacaoId = directInsertData;

        // Buscar a visualização completa
        const { data: newData, error: fetchError } = await supabase
          .from('visualizacoes')
          .select('*')
          .eq('id', visualizacaoId)
          .single();

        if (!fetchError && newData) {
          return NextResponse.json(newData, { status: 201 });
        }

        // Mesmo com erro no fetch, retornar sucesso com o ID
        return NextResponse.json({
          id: visualizacaoId,
          user_id,
          contexto: contexto || 'lancamentos',
          nome,
          descricao: descricao || null,
          filtros_json
        }, { status: 201 });
      }

      // Se falhar, tentar inserir diretamente na tabela
      const { data, error } = await supabase
        .from('visualizacoes')
        .insert({
          user_id,
          contexto: contexto || 'lancamentos',
          nome,
          descricao: descricao || null,
          filtros_json
        })
        .select()
        .single();

      if (error) {
        // Verificar se é um erro de permissão
        if (error.code === 'PGRST301' || error.message.includes('permission denied')) {
          // Tentar inserir com a função de serviço
          const { data: serviceData, error: serviceError } = await supabase.rpc('service_insert_visualizacao', {
            p_user_id: user_id,
            p_contexto: contexto || 'lancamentos',
            p_nome: nome,
            p_descricao: descricao || null,
            p_filtros_json: filtros_json
          });

          if (serviceError) {
            return NextResponse.json({ error: serviceError.message }, { status: 500 });
          }

          // Buscar a visualização recém-criada
          const visualizacaoId = serviceData;

          const { data: newData, error: fetchError } = await supabase
            .from('visualizacoes')
            .select('*')
            .eq('id', visualizacaoId)
            .single();

          if (fetchError) {
            return NextResponse.json({
              success: true,
              message: 'Visualização criada, mas não foi possível recuperar os dados',
              id: visualizacaoId
            }, { status: 201 });
          }

          return NextResponse.json(newData, { status: 201 });
        }

        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data, { status: 201 });
    } catch (insertError: any) {
      // Tentar uma abordagem alternativa
      try {
        // Tentar inserir com a função de serviço
        const { data: serviceData, error: serviceError } = await supabase.rpc('service_insert_visualizacao', {
          p_user_id: user_id,
          p_contexto: contexto || 'lancamentos',
          p_nome: nome,
          p_descricao: descricao || null,
          p_filtros_json: filtros_json
        });

        if (serviceError) {
          return NextResponse.json({ error: serviceError.message }, { status: 500 });
        }

        const visualizacaoId = serviceData;

        // Buscar a visualização completa
        const { data: newData, error: fetchError } = await supabase
          .from('visualizacoes')
          .select('*')
          .eq('id', visualizacaoId)
          .single();

        if (fetchError) {
          // Retornar dados básicos
          return NextResponse.json({
            id: visualizacaoId,
            user_id,
            contexto: contexto || 'lancamentos',
            nome,
            descricao: descricao || null,
            filtros_json
          }, { status: 201 });
        }

        return NextResponse.json(newData, { status: 201 });
      } catch (sqlError) {
        return NextResponse.json({ error: 'Erro ao criar visualização: ' + (insertError?.message || 'desconhecido') }, { status: 500 });
      }
    }
  } catch (error: any) {
    return NextResponse.json({ error: 'Erro interno do servidor: ' + (error?.message || 'desconhecido') }, { status: 500 });
  }
}