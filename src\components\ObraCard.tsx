'use client'

import { MoreHorizontal, Users } from 'lucide-react'
import Image from 'next/image' // Usar Image para otimizações

interface ObraCardProps {
  obra: {
    id: string
    name: string
    location: string
    status: string
    statusColor: string
    progress: number
    progressColor: string
    avatars: string[] // Array de URLs/paths das imagens
    deadline: string
  }
}

export function ObraCard({ obra }: ObraCardProps) {
  return (
    <div className="bg-white p-4 rounded-lg shadow border border-gray-100 space-y-3">
      {/* Top section: Status Badge and Options */}
      <div className="flex justify-between items-center">
        <span
          className={`px-2 py-0.5 rounded-full text-xs font-medium ${obra.statusColor}`}
        >
          {obra.status}
        </span>
        <button className="text-gray-400 hover:text-gray-600" aria-label="Opções da obra">
          <MoreHorizontal className="h-5 w-5" />
        </button>
      </div>

      {/* Middle section: Name and Location */}
      <div>
        <h3 className="font-semibold text-gray-900 truncate">{obra.name}</h3>
        <p className="text-sm text-gray-500">{obra.location}</p>
      </div>

      {/* Progress Bar section */}
      <div>
        <div className="flex justify-between text-sm mb-1">
          <span className="text-gray-500">Progresso</span>
          <span className="font-medium text-gray-700">{obra.progress}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-1.5">
          <div
            data-progress={obra.progress}
            className={`h-1.5 rounded-full ${obra.progressColor} [width:attr(data-progress_%)]`}
          ></div>
        </div>
      </div>

      {/* Bottom section: Avatars and Deadline */}
      <div className="flex justify-between items-center pt-2">
        <div className="flex -space-x-2 overflow-hidden">
          {obra.avatars.slice(0, 3).map((avatarUrl, index) => (
            <Image
              key={index}
              className="inline-block h-6 w-6 rounded-full ring-2 ring-white"
              src={avatarUrl} // Necessita de imagens reais ou placeholders válidos
              alt={`Avatar ${index + 1}`}
              width={24}
              height={24}
              // Adicionar placeholder se as imagens não existirem
              // onError={(e) => e.currentTarget.src = '/placeholder-avatar.png'}
            />
          ))}
          {obra.avatars.length > 3 && (
            <span className="flex items-center justify-center h-6 w-6 rounded-full ring-2 ring-white bg-gray-100 text-xs text-gray-500">
              +{obra.avatars.length - 3}
            </span>
          )}
          {/* Fallback if no avatars */}
          {obra.avatars.length === 0 && (
             <div className="flex items-center text-sm text-gray-400">
               <Users className="h-4 w-4 mr-1"/> Sem equipe
             </div>
          )}
        </div>
        <span className="text-sm text-gray-500">Término: {obra.deadline}</span>
      </div>
    </div>
  )
}