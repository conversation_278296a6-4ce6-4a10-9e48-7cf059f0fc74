// Função SQL para inserir visualizações contornando o RLS

export const insertVisualizacaoBypassRls = `
CREATE OR REPLACE FUNCTION insert_visualizacao_bypass_rls(
  p_id UUID,
  p_user_id UUID,
  p_contexto TEXT,
  p_nome TEXT,
  p_descricao TEXT,
  p_filtros_json JSONB,
  p_created_at TIMESTAMPTZ,
  p_updated_at TIMESTAMPTZ
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO visualizacoes (
    id,
    user_id,
    contexto,
    nome,
    descricao,
    filtros_json,
    created_at,
    updated_at
  ) VALUES (
    p_id,
    p_user_id,
    p_contexto,
    p_nome,
    p_descricao,
    p_filtros_json,
    p_created_at,
    p_updated_at
  );
  
  RETURN TRUE;
END;
$$;
`;

// Função para verificar se a função existe
export const checkInsertFunctionExists = `
SELECT COUNT(*) AS count
FROM information_schema.routines 
WHERE routine_type = 'FUNCTION' 
AND routine_name = 'insert_visualizacao_bypass_rls';
`;