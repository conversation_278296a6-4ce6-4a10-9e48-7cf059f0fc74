-- Função para executar SQL diretamente
CREATE OR <PERSON><PERSON><PERSON><PERSON> FUNCTION public.execute_sql(sql_query TEXT, params JSONB DEFAULT '{}'::jsonb)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
BEGIN
  EXECUTE sql_query INTO result;
  RETURN result;
EXCEPTION WHEN OTHERS THEN
  RETURN jsonb_build_object(
    'error', SQLERRM,
    'detail', SQLSTATE,
    'query', sql_query
  );
END;
$$;

-- Comentário para a função
COMMENT ON FUNCTION public.execute_sql IS 'Função para executar SQL diretamente';
