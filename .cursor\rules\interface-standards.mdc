---
description: 
globs: 
alwaysApply: false
---
# Padrões de Interface - POC Automação Financeira

## Componentes de UI Reutilizáveis

O projeto utiliza diversos componentes UI reutilizáveis, principalmente do shadcn/ui com customizações específicas para o projeto.

### DatePicker
O [componente DatePicker](mdc:src/components/ui/date-picker.tsx) deve ser utilizado para seleção de datas. Quando usado com estados de React, lembre-se de adaptar o `setDate`:

```tsx
// Uso correto com useState
const [data, setData] = useState<Date>(new Date());

<DatePicker 
  date={data} 
  setDate={(date) => date !== undefined ? setData(date) : null}
  placeholder="DD/MM/AAAA"
  inputClassName="pr-10" 
/>
```

### Switch (Toggle)
Para switches de toggle, use o componente do Radix UI, seguindo este padrão:

```tsx
<Switch.Root
  checked={estado}
  onCheckedChange={setEstado}
  className="w-11 h-6 bg-muted rounded-full relative data-[state=checked]:bg-blue-500 outline-none transition-colors duration-200"
  id="switch-id"
>
  <Switch.Thumb
    className="block w-5 h-5 bg-white rounded-full shadow transition-transform duration-200 translate-x-0.5 data-[state=checked]:translate-x-5"
  />
</Switch.Root>
```

### Dropzone para Upload
Para áreas de upload de arquivos, use o padrão de design abaixo:

```tsx
<div className="border-2 border-dashed border-gray-300 rounded-md p-6 flex flex-col items-center justify-center">
  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
  </svg>
  <div className="text-sm text-gray-500 text-center">
    <span>Clique ou arraste o arquivo aqui</span>
  </div>
  <Dropzone onDrop={handleFiles} multiple className="absolute inset-0 opacity-0 cursor-pointer" />
</div>
```

## Estrutura de Drawer/Sheet

Para drawers e sheets, utilize a seguinte estrutura para manter consistência:

```tsx
<Sheet open={open} onOpenChange={onOpenChange}>
  <SheetContent side="right" className="w-full p-0 h-screen" style={{ maxWidth: 1100 }}>
    {/* Cabeçalho com borda inferior */}
    <div className="p-6 border-b">
      <SheetTitle className="text-xl font-semibold p-0 m-0">Título do Drawer</SheetTitle>
    </div>
    
    {/* Conteúdo */}
    <div className="flex h-full">
      {/* Coluna esquerda (se aplicável) */}
      <div className="w-[30%] border-r p-4 overflow-y-auto h-full">
        {/* Conteúdo da coluna esquerda */}
      </div>
      
      {/* Coluna direita/principal */}
      <div className="w-[70%] p-6 overflow-y-auto h-full">
        {/* Conteúdo principal */}
      </div>
    </div>
  </SheetContent>
</Sheet>
```

## Cores e Estilos

### Botões
- **Botão Primário**: `className="bg-blue-600 hover:bg-blue-700 text-white font-medium"`
- **Botão Secundário**: `className="border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"`
- **Botão de Ícone**: `className="h-7 w-7 p-0"` com `variant="outline"`

### Tabelas
- **Cabeçalhos**: Uppercase, font-semibold, bg-muted
- **Linhas Alternadas**: Use `even:bg-muted/50` para linhas alternadas

### Inputs e Campos
- **Altura Padrão**: h-10 para inputs
- **Altura de Textarea**: h-[80px] ou rows={3}
- **Placeholders**: Use placeholders informativos e consistentes

## Layout e Espaçamento

### Containers
- Use margens bottom de 6 para separar seções: `className="mb-6"`
- Para grids, use: `className="grid grid-cols-1 md:grid-cols-2 gap-4"`

### Tamanhos Padrões
- **Largura Máxima de Drawer**: 1100px
- **Padding de Seção**: p-6
- **Gap entre Elementos**: gap-4 ou gap-2 para elementos menores

## Exemplos de Componentes Complexos

Para ver exemplos de implementações complexas, consulte:

- [Drawer de Pagamentos Pendentes](mdc:src/app/(auth)/parcelas/components/drawer-pagamentos-pendentes.tsx)
- [Formulário de Lançamentos](mdc:src/app/(auth)/lancamentos/components/form-lancamento.tsx)

## Dicas de Implementação

1. Sempre faça o mock visual primeiro, depois adicione a funcionalidade
2. Priorize responsividade utilizando classes como `md:grid-cols-2`
3. Use variantes de componentes sempre que possível para manter consistência
4. Para casos de upload de arquivos, sempre mostre previews ou indicação visual dos arquivos selecionados

