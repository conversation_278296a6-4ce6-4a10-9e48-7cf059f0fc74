'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Download, Upload, Loader2, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function ImportarPage() {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const router = useRouter();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Verificar se o arquivo é uma planilha Excel ou CSV
      if (
        selectedFile.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        selectedFile.type === 'application/vnd.ms-excel' ||
        selectedFile.type === 'text/csv'
      ) {
        setFile(selectedFile);
      } else {
        toast.error('Por favor, selecione um arquivo Excel ou CSV válido');
        e.target.value = '';
      }
    }
  };

  const handleUpload = async () => {
    if (!file) {
      toast.error('Por favor, selecione um arquivo para importar');
      return;
    }

    setIsUploading(true);

    try {
      // O FormData será usado para enviar o arquivo ao servidor
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/import-lancamentos', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao importar lançamentos');
      }

      const data = await response.json();

      toast.success(`${data.imported} lançamentos importados com sucesso!`);
      router.refresh();
    } catch (error: any) {
      toast.error(error instanceof Error ? error.message : 'Erro ao importar lançamentos');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDownloadModelo = async () => {
    setIsDownloading(true);

    try {
      const response = await fetch('/api/modelo-lancamentos');

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao baixar modelo');
      }

      // Criar um Blob a partir da resposta
      const blob = await response.blob();

      // Criar uma URL para o Blob
      const url = window.URL.createObjectURL(blob);

      // Criar um elemento âncora para download
      const a = document.createElement('a');
      a.href = url;
      a.download = 'modelo_lancamentos.xlsx';
      document.body.appendChild(a);

      // Clicar no elemento para iniciar o download
      a.click();

      // Limpar recursos
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('Modelo de planilha baixado com sucesso!');
    } catch (error: any) {
      
      toast.error(error instanceof Error ? error.message : 'Erro ao baixar modelo de planilha');
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col items-start gap-4 md:flex-row md:justify-between md:items-center mb-8">
        <div>
          <div className="flex items-center gap-2">
            <Link href="/lancamentos">
              <Button variant="ghost" size="sm" className="gap-1">
                <ArrowLeft className="h-4 w-4" />
                <span>Voltar</span>
              </Button>
            </Link>
            <h1 className="text-3xl font-bold tracking-tight">Importar Lançamentos</h1>
          </div>
          <p className="text-muted-foreground mt-1">
            Importe lançamentos a partir de uma planilha Excel ou CSV
          </p>
        </div>
      </div>

      <div className="flex flex-col gap-8">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Importar Lançamentos</CardTitle>
            <CardDescription>
              Importe seus lançamentos a partir de uma planilha Excel ou CSV.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid w-full items-center gap-4">
              <div className="flex flex-col space-y-1.5">
                <Label htmlFor="file">Arquivo</Label>
                <div className="flex items-center gap-2">
                  <input
                    id="file"
                    type="file"
                    accept=".xlsx,.xls,.csv"
                    onChange={handleFileChange}
                    className="flex-1 px-3 py-2 border border-input rounded-md text-sm shadow-sm"
                    aria-label="Selecionar arquivo de lançamentos"
                    title="Selecionar arquivo de lançamentos"
                  />
                </div>
              </div>
              {file && (
                <p className="text-sm text-gray-500">
                  Arquivo selecionado: {file.name}
                </p>
              )}
              <div className="flex justify-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownloadModelo}
                  disabled={isDownloading}
                  className="mt-2"
                >
                  {isDownloading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Baixando...
                    </>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      Baixar modelo de planilha
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setFile(null)}>
              Limpar
            </Button>
            <Button
              onClick={handleUpload}
              disabled={!file || isUploading}
            >
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Importando...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Importar
                </>
              )}
            </Button>
          </CardFooter>
        </Card>

        <div className="mt-6 space-y-4">
          <h2 className="text-xl font-semibold tracking-tight">Instruções de Importação</h2>
          <div className="space-y-2">
            <p>Para importar lançamentos, siga os passos abaixo:</p>
            <ol className="list-decimal list-inside space-y-2 ml-4">
              <li>Baixe o modelo de planilha clicando no botão &quot;Baixar modelo de planilha&quot;</li>
              <li>Preencha os dados na planilha conforme as instruções</li>
              <li>Salve a planilha em formato Excel ou CSV</li>
              <li>Clique em &quot;Escolher arquivo&quot; e selecione a planilha salva</li>
              <li>Clique em &quot;Importar&quot; para iniciar a importação</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}