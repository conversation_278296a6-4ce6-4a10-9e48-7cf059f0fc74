'use client'

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { PageHeader } from '@/components/Layout/PageHeader';
import { ListaContatos } from './components/lista-contatos';
import { DrawerContato } from './components/drawer-contato';

export default function ContatosPage() {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedContatoId, setSelectedContatoId] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleNewContato = () => {
    setSelectedContatoId(null);
    setDrawerOpen(true);
  };

  const handleEditContato = (id: string) => {
    setSelectedContatoId(id);
    setDrawerOpen(true);
  };

  const handleContatoSaved = () => {
    setRefreshKey(prev => prev + 1);
    setDrawerOpen(false);
  };

  return (
    <div className="space-y-6">
      <PageHeader 
        title="Contatos"
        description="Gerencie fornecedores, prestadores e clientes"
        actions={
          <Button onClick={handleNewContato}>
            <Plus className="h-4 w-4 mr-2" />
            Novo Contato
          </Button>
        }
      />

      <ListaContatos 
        key={refreshKey}
        onEdit={handleEditContato}
      />

      <DrawerContato 
        open={drawerOpen}
        onOpenChange={setDrawerOpen}
        contatoId={selectedContatoId}
        onContatoSaved={handleContatoSaved}
      />
    </div>
  );
}