---
description: 
globs: 
alwaysApply: true
---
# Componentes de UI

## Biblioteca de Componentes

O projeto utiliza os componentes do [shadcn/ui](mdc:https:/ui.shadcn.com) como base para a interface. Estes componentes estão localizados em [src/components/ui](mdc:src/components/ui).

## Componentes de Overlay

Temos três principais componentes de overlay:

1. **Dialog**: Para diálogos modais centralizados na tela
   ```tsx
   <Dialog open={open} onOpenChange={setOpen}>
     <DialogContent className="sm:max-w-[500px]">
       <DialogHeader>
         <DialogTitle>Título</DialogTitle>
         <DialogDescription>Descrição</DialogDescription>
       </DialogHeader>
       {/* Conteúdo */}
     </DialogContent>
   </Dialog>
   ```

2. **Sheet**: Para painéis laterais (drawers)
   ```tsx
   <Sheet open={open} onOpenChange={setOpen}>
     <SheetContent className="sm:max-w-[600px]" side="right">
       <SheetHeader>
         <SheetTitle>Título</SheetTitle>
         <SheetDescription>Descrição</SheetDescription>
       </SheetHeader>
       {/* Conteúdo */}
     </SheetContent>
   </Sheet>
   ```

3. **Popover**: Para dropdowns e tooltips
   ```tsx
   <Popover>
     <PopoverTrigger>Clique aqui</PopoverTrigger>
     <PopoverContent className="bg-background text-foreground z-50">
       {/* Conteúdo */}
     </PopoverContent>
   </Popover>
   ```

## Problema Conhecido: Background em Overlays

Dentro de componentes de overlay (Sheet, Dialog), os componentes de dropdown (Select, Popover) podem ter problemas de fundo transparente. Para resolver:

1. Sempre adicione as classes `bg-background text-foreground z-50` ao componente de conteúdo:
   ```tsx
   <SelectContent className="bg-background text-foreground z-50">
   ```

2. Para Popovers aninhados, use um z-index ainda maior:
   ```tsx
   <PopoverContent className="bg-background text-foreground z-[100]">
   ```

## Tabelas

As tabelas seguem esta estrutura:
```tsx
<div className="border rounded-md">
  <Table>
    <TableHeader className="bg-muted">
      <TableRow>
        <TableHead>Coluna 1</TableHead>
        <TableHead>Coluna 2</TableHead>
        <TableHead className="w-[100px]"></TableHead> {/* Largura fixa para ações */}
      </TableRow>
    </TableHeader>
    <TableBody>
      {items.map((item) => (
        <TableRow key={item.id}>
          <TableCell className="font-medium">{item.value1}</TableCell>
          <TableCell>{item.value2}</TableCell>
          <TableCell>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" className="h-7 w-7">
                <Edit2 className="h-4 w-4" />
              </Button>
            </div>
          </TableCell>
        </TableRow>
      ))}
    </TableBody>
  </Table>
</div>
```

Para responsividade, use `className="hidden md:table-cell"` nas colunas menos importantes.

