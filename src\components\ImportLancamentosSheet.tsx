'use client'

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Download, Loader2, Upload } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

// Este componente será implementado após instalar a biblioteca xlsx
export function ImportLancamentosSheet() {
  const [isUploading, setIsUploading] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const router = useRouter();
  const supabase = createClientComponentClient();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Verificar se o arquivo é uma planilha Excel ou CSV
      if (
        selectedFile.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        selectedFile.type === 'application/vnd.ms-excel' ||
        selectedFile.type === 'text/csv'
      ) {
        setFile(selectedFile);
      } else {
        toast.error('Por favor, selecione um arquivo Excel ou CSV válido');
        e.target.value = '';
      }
    }
  };

  const handleAuthError = async (error: any) => {
    // Se for um erro de autenticação, tente recarregar a sessão
    if (error.message?.includes('cookie') || error.message?.includes('auth') || error.message?.includes('token')) {
      toast.error('Erro de autenticação. Redirecionando para o login...');
      try {
        // Limpar a sessão atual e redirecionar para login
        await supabase.auth.signOut({ scope: 'local' });
        setTimeout(() => router.push('/login'), 2000);
      } catch (err) {
        // Último recurso: recarregar a página
        setTimeout(() => window.location.href = '/login', 2000);
      }
    } else {
      // Se for outro tipo de erro, apenas informe ao usuário
      toast.error(error.message || 'Erro ao processar requisição');
    }
  };

  const handleUpload = async () => {
    if (!file) {
      toast.error('Por favor, selecione um arquivo para importar');
      return;
    }

    setIsUploading(true);

    try {
      // Verificar a sessão primeiro
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) throw sessionError;
      if (!session) {
        toast.error('Sessão expirada. Redirecionando para o login...');
        setTimeout(() => router.push('/login'), 2000);
        return;
      }

      // O FormData será usado para enviar o arquivo ao servidor
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/import-lancamentos', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao importar lançamentos');
      }

      const data = await response.json();

      toast.success(`${data.imported} lançamentos importados com sucesso!`);
      router.refresh();
    } catch (error: any) {
      if (error.message?.includes('cookie') || error.message?.includes('auth') || error.message?.includes('token')) {
        handleAuthError(error);
      } else {
        toast.error(error instanceof Error ? error.message : 'Erro ao importar lançamentos');
      }
    } finally {
      setIsUploading(false);
    }
  };

  const handleDownloadModelo = async () => {
    setIsDownloading(true);

    try {
      // Verificar a sessão primeiro
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) throw sessionError;
      if (!session) {
        toast.error('Sessão expirada. Redirecionando para o login...');
        setTimeout(() => router.push('/login'), 2000);
        return;
      }

      const response = await fetch('/api/modelo-lancamentos');

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao baixar modelo');
      }

      // Criar um Blob a partir da resposta
      const blob = await response.blob();

      // Criar uma URL para o Blob
      const url = window.URL.createObjectURL(blob);

      // Criar um elemento âncora para download
      const a = document.createElement('a');
      a.href = url;
      a.download = 'modelo_lancamentos.xlsx';
      document.body.appendChild(a);

      // Clicar no elemento para iniciar o download
      a.click();

      // Limpar recursos
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('Modelo de planilha baixado com sucesso!');
    } catch (error: any) {
      if (error.message?.includes('cookie') || error.message?.includes('auth') || error.message?.includes('token')) {
        handleAuthError(error);
      } else {
        toast.error(error instanceof Error ? error.message : 'Erro ao baixar modelo de planilha');
      }
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Importar Lançamentos</CardTitle>
        <CardDescription>
          Importe seus lançamentos a partir de uma planilha Excel ou CSV.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid w-full items-center gap-4">
          <div className="flex flex-col space-y-1.5">
            <Label htmlFor="file">Arquivo</Label>
            <div className="flex items-center gap-2">
              <input
                id="file"
                type="file"
                accept=".xlsx,.xls,.csv"
                onChange={handleFileChange}
                className="flex-1 px-3 py-2 border border-input rounded-md text-sm shadow-sm"
                aria-label="Selecionar arquivo de lançamentos"
                title="Selecionar arquivo de lançamentos"
              />
            </div>
          </div>
          {file && (
            <p className="text-sm text-gray-500">
              Arquivo selecionado: {file.name}
            </p>
          )}
          <div className="flex justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadModelo}
              disabled={isDownloading}
              className="mt-2"
            >
              {isDownloading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Baixando...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Baixar modelo de planilha
                </>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => setFile(null)}>
          Limpar
        </Button>
        <Button
          onClick={handleUpload}
          disabled={!file || isUploading}
        >
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Importando...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Importar
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}