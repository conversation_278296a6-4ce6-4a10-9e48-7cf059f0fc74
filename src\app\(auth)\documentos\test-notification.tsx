'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { DocumentProcessingNotification, DOCUMENT_PROCESSING_STEPS } from '@/components/ui/document-processing-notification';
import { FileText, Play, RefreshCw } from 'lucide-react';

export default function TestNotification() {
  const [showNotification, setShowNotification] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [fileName, setFileName] = useState("Fábio 24.04At.pdf");
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [isRunning, setIsRunning] = useState(false);

  // Limpar timers ao desmontar o componente
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  const startDemo = () => {
    // Reset state
    setCurrentStep(0);
    setIsComplete(false);
    setShowNotification(true);
    setIsRunning(true);

    // Start advancing steps
    advanceStep();
  };

  const advanceStep = () => {
    // Clear any existing timer
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // Advance to next step
    setCurrentStep(prev => {
      const nextStep = prev + 1;
      if (nextStep >= DOCUMENT_PROCESSING_STEPS.length - 1) {
        setTimeout(() => {
          setIsComplete(true);
          setIsRunning(false);
        }, 1000);
        return DOCUMENT_PROCESSING_STEPS.length - 1;
      }
      return nextStep;
    });

    // Schedule next advance (between 1.5-3 seconds for a better demo)
    const nextStepDelay = Math.floor(Math.random() * 1500) + 1500;
    timerRef.current = setTimeout(() => {
      if (currentStep < DOCUMENT_PROCESSING_STEPS.length - 2) {
        advanceStep();
      } else {
        // Complete the process
        setIsComplete(true);
        setIsRunning(false);
      }
    }, nextStepDelay);
  };

  const resetDemo = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    setShowNotification(false);
    setCurrentStep(0);
    setIsComplete(false);
    setIsRunning(false);
  };

  return (
    <div className="p-8 max-w-3xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h1 className="text-2xl font-bold mb-2">Demonstração do Componente de Processamento</h1>
        <p className="text-gray-600 mb-6">
          Este é um exemplo do novo componente de notificação de processamento de documentos.
          Clique no botão abaixo para iniciar a demonstração.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 items-center">
          <div className="flex-1 w-full">
            <div className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50">
              <FileText className="h-5 w-5 text-blue-500" />
              <span className="text-sm font-medium">{fileName}</span>
            </div>
          </div>

          <div className="flex gap-3">
            <Button
              onClick={startDemo}
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              <span>Iniciar Demo</span>
            </Button>

            <Button
              onClick={resetDemo}
              variant="outline"
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Resetar</span>
            </Button>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-lg font-semibold mb-4">Sobre o Componente</h2>
        <ul className="list-disc pl-5 space-y-2 text-gray-700">
          <li>Design moderno com animações suaves e interativas</li>
          <li>Mensagens comerciais mais atraentes durante o processamento</li>
          <li>Efeitos visuais aprimorados (confetti, brilho, digitação)</li>
          <li>Barra de progresso com animações sofisticadas</li>
          <li>Ícones animados para cada etapa do processo</li>
          <li>Efeito de hover com transformação 3D sutil</li>
          <li>Celebração visual quando o processamento é concluído</li>
        </ul>
      </div>

      {showNotification && (
        <DocumentProcessingNotification
          fileName={fileName}
          currentStep={currentStep}
          isComplete={isComplete}
          onClose={() => setShowNotification(false)}
        />
      )}
    </div>
  );
}