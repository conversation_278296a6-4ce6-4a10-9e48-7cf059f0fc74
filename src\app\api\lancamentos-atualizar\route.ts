import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/types/supabase';
import { createClient } from '@supabase/supabase-js';

// Criar cliente Supabase com a chave de serviço para contornar o RLS
const createServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    
    return null;
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

// POST /api/lancamentos-atualizar - Atualizar um lançamento específico
export async function POST(request: NextRequest) {
  try {
    // Obter os dados da requisição para atualização
    const { id, ...dadosAtualizacao } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'ID do lançamento não fornecido' },
        { status: 400 }
      );
    }

    // Criar cliente Supabase com service role para contornar o RLS
    const supabase = createServiceClient();

    if (!supabase) {
      return NextResponse.json(
        { error: 'Não foi possível criar o cliente de serviço' },
        { status: 500 }
      );
    }

    // Verificar primeiro se o lançamento existe
    const { data: lancamentoExistente, error: erroConsulta } = await supabase
      .from('lancamentos')
      .select('id')
      .eq('id', id)
      .maybeSingle();

    if (erroConsulta) {
      
      return NextResponse.json(
        { error: `Erro ao consultar lançamento: ${erroConsulta.message}` },
        { status: 500 }
      );
    }

    if (!lancamentoExistente) {
      return NextResponse.json(
        { error: `Lançamento com ID ${id} não encontrado` },
        { status: 404 }
      );
    }

    // Processar os dados antes de atualizar
    const dadosProcessados = { ...dadosAtualizacao };

    // Garantir que o valor_total seja um número válido
    if (dadosProcessados.valor_total !== undefined) {
      if (typeof dadosProcessados.valor_total === 'string') {
        const valorLimpo = dadosProcessados.valor_total.replace(/[^\d,.]/g, '').replace(',', '.');
        dadosProcessados.valor_total = parseFloat(valorLimpo);

        if (isNaN(dadosProcessados.valor_total)) {
          return NextResponse.json(
            { error: `Valor total inválido: ${dadosAtualizacao.valor_total}` },
            { status: 400 }
          );
        }
      }
    }

    // Garantir que a data_competencia esteja no formato correto
    if (dadosProcessados.data_competencia !== undefined && dadosProcessados.data_competencia !== null) {
      try {
        const dataObj = new Date(dadosProcessados.data_competencia);
        if (isNaN(dataObj.getTime())) {
          throw new Error('Data inválida');
        }

        // Formatar a data para o formato ISO (YYYY-MM-DD)
        const ano = dataObj.getFullYear();
        const mes = String(dataObj.getMonth() + 1).padStart(2, '0');
        const dia = String(dataObj.getDate()).padStart(2, '0');
        dadosProcessados.data_competencia = `${ano}-${mes}-${dia}`;
      } catch (error) {
        return NextResponse.json(
          { error: `Data de competência inválida: ${dadosProcessados.data_competencia}` },
          { status: 400 }
        );
      }
    }

    // Adicionar timestamp de atualização
    dadosProcessados.updated_at = new Date().toISOString();

    // Atualizar o lançamento usando o cliente de serviço
    const { data, error } = await supabase
      .from('lancamentos')
      .update(dadosProcessados)
      .eq('id', id)
      .select();

    if (error) {
      return NextResponse.json(
        { error: `Erro ao atualizar lançamento: ${error.message}` },
        { status: 500 }
      );
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: 'Lançamento atualizado, mas não foi possível recuperar os dados' },
        { status: 500 }
      );
    }

    return NextResponse.json(data[0]);
  } catch (error: any) {
    return NextResponse.json(
      { error: `Erro interno do servidor: ${error?.message || 'Desconhecido'}` },
      { status: 500 }
    );
  }
}