## **Structure (Tecnologias e Estrutura da Aplicação)**

## Nesta seção definimos a **arquitetura tecnológica** da aplicação e como o código será organizado. Como especificado, usaremos **React** no front-end, **Supabase** como back-end (banco de dados Postgres, autenticação e possivelmente funções serverless), **Shadcn/UI** para componentes de interface, e a integração com WhatsApp via **Evolution API**. Também detalharemos a estrutura de pastas/projeto e quaisquer padrões de desenvolvimento relevantes.

### **Tecnologias Principais**

## - **Front-end:** Desenvolvido em **React** (JavaScript/TypeScript). Iremos usar **Next.js** como framework React para facilitar roteamento e possivelmente API routes. 

- **Linguagem:** Todo o código será em **TypeScript** para maior segurança de tipos e melhor manutenção.

- **Banco de Dados:** **Supabase (PostgreSQL)** – centraliza o armazenamento dos dados via tabelas descritas no schema. O Supabase também fornece:

  - **Autenticação** de usuários (registro, login, gestão de sessões JWT) facilitada.
  - **Row Level Security (RLS)** para proteger os dados (configurável para multi-usuário).
  - **Reações em Tempo Real:** Supabase pode transmitir mudanças do banco para o cliente via websockets, utilizado para atualizar lançamentos em tempo real nas telas.
  - **Storage de arquivos:** se futuramente quisermos armazenar documentos (e.g. notas fiscais, imagens enviadas pelo WhatsApp), podemos usar o storage Supabase. Não detalhado aqui mas disponível.
  - **Edge Functions:** funções serverless em Node/TS que rodam no backend Supabase – usaremos para a integração do webhook do WhatsApp.

- **Framework/UI:** **Shadcn/UI** (shadcn/ui) – uma coleção de componentes React pré-construídos e estilizados com **Tailwind CSS**. Esses componentes fornecem elementos de interface modernos e acessíveis (por ex: formulários, botões, diálogos, tabelas, navegação) que podemos utilizar para agilizar o desenvolvimento UI, mantendo consistência visual. 
  - _Obs:_ Precisamos configurar Tailwind CSS no projeto e talvez usar o CLI do shadcn para adicionar os componentes desejados. A estilização será baseada em classes utilitárias do Tailwind, com possibilidade de customizar temas (cores, etc.) conforme identidade da empresa.

- **Estilização:** Principalmente via **Tailwind CSS** classes, possibly with custom design tokens. Shadcn components come with default Tailwind styling which we can override if needed. This avoids writing a lot of custom CSS and ensures responsiveness out-of-the-box.

- **Ícones e Gráficos:** Podemos usar a biblioteca **Lucide Icons** (que Shadcn UI costuma suportar) para ícones no menu, botões, etc. Para gráficos (no dashboard), iremos usar a lib **Recharts**.

- **WhatsApp Integration:** **Evolution API** – Esta será configurada provavelmente fora do React app (no servidor). Duas possibilidades:

  - **Hosted Evolution API:** Podemos rodar uma instância do Evolution API (que é open-source Node.js) em um servidor ou as Supabase Edge Functions não suportariam persistent Websocket to WhatsApp, então provavelmente um separate server or use a cloud service from Evolution if available. The Evolution API will handle WhatsApp and then call our Supabase function/webhook.

  - **Webhook Setup:** Uma função no Supabase (ou um small Express server if we host externally) vai expor um endpoint /webhook/whatsapp. Configuraremos a Evolution API para enviar eventos de mensagem recebida para esse endpoint. A função receberá e processará conforme descrito.

  - **Library for HTTP:** Dentro da Edge Function (if using), use Node fetch or axios to communicate responses if needed. The function then uses Supabase SDK or SQL to insert the lancamento.

  - **Security:** The webhook URL will have a secret or token check (Evolution API can append a token in header or query which we validate).

- **Autenticação no front-end:** Utilizando o Supabase Auth, provavelmente via Magic Link ou email/senha. O React app usará o Supabase client to sign in. Uma vez logado, manterá o usuário e seu JWT em um contexto.

  - Podemos usar a abordagem de **Context API** or **React hooks** provided by Supabase (`useUser()` etc if using their toolkit) to get the current user and protect routes.

  - Next.js: implement middleware to redirect to login if not authenticated, or use client-side checks in routes.

### **Estrutura de Pastas e Organização do Código (Front-end)**

## Organizaremos o projeto de forma modular, facilitando manutenção e escalabilidade. Uma possível estrutura (assumindo Next.js 13+ with App Router for example):

/app
   / (raiz do app, layout principal, etc.)
   /dashboard/page.tsx        -> Dashboard geral da empresa
   /obras/page.tsx            -> Listagem de obras
   /obras/[obraId]/page.tsx   -> Detalhe/dashboard de uma obra específica
   /obras/[obraId]/edit.tsx   -> (opcional) Tela de edição de obra
   /obras/new/page.tsx        -> Formulário de nova obra
   /lancamentos/pendentes/page.tsx -> Lista global de lançamentos pendentes
   /lancamentos/new/page.tsx  -> Formulário genérico novo lançamento (se for via menu global)
   /lancamentos/[id]/edit.tsx -> Editar um lançamento específico
   /contratos/page.tsx        -> Lista de contratos (todos)
   /contratos/[id]/page.tsx   -> Detalhe de contrato (com suas medições)
   /contratos/new/page.tsx    -> Criar novo contrato
   /contatos/page.tsx         -> Lista de contatos
   /contatos/new/page.tsx     -> Novo contato (pode ser modal via /contatos)
   /contatos/[id]/edit.tsx    -> Editar contato
   /configuracoes/page.tsx    -> Tela de configurações gerais
   /login/page.tsx            -> Tela de login
   ... etc.

* We might not need heavy state management (Redux) because Supabase + React state should suffice. Possibly use **React Query** to fetch and cache lists of data (like list of obras, contatos, etc.) and auto-refetch on changes. But Supabase's real-time might supplant some manual refetch.

* Use **React Context** for things like current user, and maybe current company (if multi-tenant), or a global loading state.

### **Componentes de UI e UX Considerations**

## - **Navigation:** Use a Sidebar with links to main sections (Dashboard, Obras, Lançamentos Pendentes, Contratos, Contatos, Configurações, Logout). 

- **Tables/Listings:** Shadcn UI provides stylized table components which we can use for lists (e.g., lista de obras, contatos). We will ensure they are responsive (on mobile, maybe stack or scroll).

- **Forms:** Use Shadcn’s **Form** components which wrap Radix UI inputs with nice styling. This ensures consistent look for input, select, date-picker (perhaps using Radix **Popover** + custom date picker or integrating something like **react-datepicker** styled accordingly).

- **Modals/Dialogs:** For actions like “Edit” or confirming deletion, Shadcn’s **Dialog** or **Alert Dialog** can be used. E.g., to confirm removing an item or approving a launch, a confirmation dialog can pop with yes/no.

- **Feedback:** Use Shadcn’s **Toast** or **Notification** system (they might not have out-of-box, but one can create a context for toasts) to show success messages like “Lançamento aprovado!” or error messages uniformly.

- **Icons:** incorporate icons from Lucide (Shadcn uses Lucide icons by default). For example, use a building icon for Obras, file-text or credit-card icon for Lançamentos, briefcase for Contratos, users for Contatos, gear for Configurações, etc., making the menu more intuitive.

- **Theme:** Possibly allow a light/dark mode (Shadcn components support dark mode if configured). Initially can stick to a light theme that matches corporate identity (maybe using Tailwind config to set primary color if needed).

- **Responsividade:** The UI must be responsive to allow usage on desktop, tablet, or phone (perhaps managers in the field might open on phone). Tailwind makes it easier with responsive utility classes. The layout should collapse menu on small screens, etc.

- **Configuration & Secrets:** We'll have a `.env` file (not committed) holding:

  - Supabase keys (anon key for client, service key for functions if needed).

  - Evolution API info: e.g. `EVOLUTION_API_TOKEN` (if calls need auth) or `WHATSAPP_VERIFY_TOKEN` if we need to respond to a verification challenge (for official API webhook).

  - Possibly phone numbers or IDs.

  - These will be loaded in appropriate places (Next.js can expose needed keys to client via env variables prefixed with NEXT\_PUBLIC for example).

- **Error Handling:** Implement try/catch around supabase calls and show user-friendly errors. E.g., if a launch insert fails (maybe missing field), show message. Log details to console or Supabase logs for debugging.

### **Testing and Quality**

## - We will test the interface manually and possibly write unit tests for critical parsing logic (like the WhatsApp message parser function, to ensure various message formats are handled).

- Since cursor.io might assist in code generation from this spec, clarity in each part ensures that all required components are built.

- The codebase will be documented where non-trivial, and the spec will serve as a blueprint for the implementation.

- Using a consistent code style (formatted via Prettier, linted via ESLint) to maintain quality.

### **Deployment**

## - The app (if Next.js) can be deployed on Vercel or similar, connecting to Supabase for data. The Evolution API likely runs on a server or container either self-managed or a service.

- Supabase handles the database hosting and functions; ensure the Edge Function deployment and Evolution webhook are in place before going live.

- The WhatsApp integration should be tested with the fixed number in a sandbox environment before enabling with real data.

#
