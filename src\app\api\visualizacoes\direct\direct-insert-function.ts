// Função SQL para inserir visualizações diretamente

export const directInsertVisualizacao = `
CREATE OR REPLACE FUNCTION public.direct_insert_visualizacao(
  p_user_id UUID,
  p_contexto TEXT,
  p_nome TEXT,
  p_descricao TEXT,
  p_filtros_json JSONB
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_id UUID;
BEGIN
  -- Generate a new UUID for the visualization
  v_id := gen_random_uuid();
  
  -- Insert the visualization with the generated UUID
  INSERT INTO public.visualizacoes (
    id,
    user_id,
    contexto,
    nome,
    descricao,
    filtros_json,
    created_at,
    updated_at
  ) VALUES (
    v_id,
    p_user_id,
    p_contexto,
    p_nome,
    p_descricao,
    p_filtros_json,
    NOW(),
    NOW()
  );
  
  -- Return the ID of the created visualization
  RETURN v_id;
END;
$$;
`;

// Função para verificar se a função de inserção direta existe
export const checkDirectInsertFunctionExists = `
CREATE OR REPLACE FUNCTION public.check_direct_insert_function_exists()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_count int;
  v_result json;
BEGIN
  SELECT COUNT(*) INTO v_count
  FROM pg_proc
  WHERE proname = 'direct_insert_visualizacao';
  
  v_result := json_build_object('count', v_count);
  RETURN v_result;
END;
$$;
`;