import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { getSupabaseRouteClient } from '@/lib/supabase/route-client';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import { verificarAutenticacao } from '@/lib/auth-helpers';
import { createServiceClient } from '@/lib/supabase/service-client';

// GET /api/documentos/[id] - Obter um documento específico
export const dynamic = 'force-dynamic';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Acessar params.id de forma assíncrona
    const { id: documentoId } = await params;

    if (!documentoId) {
      return NextResponse.json(
        { error: 'ID do documento não fornecido' },
        { status: 400 }
      );
    }

    // Verificar se devemos forçar o uso do cliente de serviço
    const url = new URL(request.url);
    const forceServiceRole = url.searchParams.get('forceServiceRole') === 'true';

    // Inicializar o cliente do Supabase
    const supabase = await getSupabaseRouteClient();

    // Determinar qual cliente usar
    let clientToUse = supabase;

    // Verificar se devemos usar o cliente de serviço diretamente
    if (forceServiceRole) {
      const serviceClient = createServiceClient();

      if (!serviceClient) {
        return NextResponse.json({ error: 'Erro ao criar cliente de serviço' }, { status: 500 });
      }

      clientToUse = serviceClient;
    } else {
      // Tentar com autenticação normal primeiro
      const auth = await verificarAutenticacao(supabase);

      // Se não estiver autenticado, tentar com cliente de serviço como fallback
      if (!auth.autenticado) {
        const serviceClient = createServiceClient();

        if (!serviceClient) {
          return NextResponse.json({ error: 'Erro ao criar cliente de serviço após falha de autenticação' }, { status: 500 });
        }

        clientToUse = serviceClient;
      }
    }

    // Consultar documento
    const { data, error } = await clientToUse
      .from('documentos')
      .select('*')
      .eq('id', documentoId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Documento não encontrado' }, { status: 404 });
      }
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Erro ao obter documento: ' + error.message },
      { status: 500 }
    );
  }
}

// PATCH /api/documentos/[id] - Atualizar um documento
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Acessar params.id de forma assíncrona
    const { id: documentoId } = await params;

    if (!documentoId) {
      return NextResponse.json(
        { error: 'ID do documento não fornecido' },
        { status: 400 }
      );
    }

    // Criar cliente Supabase diretamente com as variáveis de ambiente
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      return NextResponse.json(
        { error: 'Configuração do servidor incompleta' },
        { status: 500 }
      );
    }

    // Criar cliente Supabase
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // Obter o body da requisição com tratamento de erro
    let body;
    try {
      const clonedRequest = request.clone();
      body = await clonedRequest.json();
    } catch (jsonError: any) {
      console.error('Erro ao processar JSON:', jsonError);
      return NextResponse.json(
        { error: `Erro ao processar o corpo da requisição: ${jsonError.message}` },
        { status: 400 }
      );
    }

    // Verificar se são dados extraídos
    if (body.dados_extraidos) {
      // Usar o cliente de serviço para garantir que a atualização funcione
      const serviceClient = createServiceClient();

      if (!serviceClient) {
        return NextResponse.json({ error: 'Erro ao criar cliente de serviço' }, { status: 500 });
      }

      // Primeiro, buscar os dados atuais do documento
      const { data: documentoAtual, error: getError } = await serviceClient
        .from('documentos')
        .select('*')
        .eq('id', documentoId)
        .single();

      if (getError) {
        return NextResponse.json({ error: getError.message }, { status: 500 });
      }

      // Mesclar dados existentes com novos dados, preservando campos importantes
      const dadosExistentes = documentoAtual.dados_extraidos || {};
      const dadosMesclados = {
        ...dadosExistentes,
        ...body.dados_extraidos
      };

      // Preparar campos para atualização, preservando valores existentes quando novos são vazios
      const camposAtualizacao: any = {
        dados_extraidos: dadosMesclados,
        updated_at: new Date().toISOString()
      };

      // Só atualizar campos se os novos valores não forem vazios/null
      if (body.dados_extraidos.fornecedor && body.dados_extraidos.fornecedor.trim() !== '') {
        camposAtualizacao.fornecedor = body.dados_extraidos.fornecedor;
      }

      if (body.dados_extraidos.valor_total && body.dados_extraidos.valor_total > 0) {
        camposAtualizacao.valor_total = body.dados_extraidos.valor_total;
      }

      if (body.dados_extraidos.data_vencimento) {
        camposAtualizacao.data_vencimento = body.dados_extraidos.data_vencimento;
      }

      // Atualizar dados extraídos usando o cliente de serviço
      const { data, error } = await serviceClient
        .from('documentos')
        .update(camposAtualizacao)
        .eq('id', documentoId)
        .select()
        .single();


      if (error) console.error('❌ Erro ao atualizar:', error);

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data);
    }

    // Se for aprovação
    if (body.status === 'aprovado' && body.lancamento) {
      // Usar o cliente de serviço para contornar RLS
      const serviceClient = createServiceClient();
      if (!serviceClient) {
        return NextResponse.json({ error: 'Erro ao criar cliente de serviço para aprovação' }, { status: 500 });
      }

      // Primeiro, obter o documento para ter acesso ao user_id e dados extraídos
      const { data: docData, error: docError } = await serviceClient
        .from('documentos')
        .select('user_id, dados_extraidos')
        .eq('id', documentoId)
        .single();

      if (docError) {
        return NextResponse.json({ error: docError.message }, { status: 500 });
      }

      // Verificar se há contato detectado pela IA e se não há contato_id já definido
      let contatoId = body.lancamento.contato_id;
      
      if (!contatoId && docData.dados_extraidos?.contato_detectado) {
        try {
          // Criar o contato automaticamente baseado nos dados detectados pela IA
          const contatoDetectado = docData.dados_extraidos.contato_detectado;
          
          const novoContato = {
            nome_empresa: contatoDetectado.nome_empresa || contatoDetectado.nome_razao || docData.dados_extraidos.fornecedor || 'Fornecedor Detectado',
            nome_razao: contatoDetectado.nome_razao,
            nome_contato: contatoDetectado.nome_contato,
            email: contatoDetectado.email,
            telefone: contatoDetectado.telefone,
            cpf_cnpj: contatoDetectado.cpf_cnpj,
            tipo_pessoa: contatoDetectado.tipo_pessoa,
            chave_pix: contatoDetectado.chave_pix,
            tipo: 'fornecedor', // Assumir fornecedor por padrão
            user_id: docData.user_id
          };

          // Remover campos undefined/null para evitar problemas
          Object.keys(novoContato).forEach(key => {
            if ((novoContato as any)[key] === undefined || (novoContato as any)[key] === null || (novoContato as any)[key] === '') {
              delete (novoContato as any)[key];
            }
          });

          const { data: contatoCriado, error: contatoError } = await serviceClient
            .from('contatos')
            .insert(novoContato)
            .select()
            .single();

          if (contatoError) {
            console.error('Erro ao criar contato detectado:', contatoError);
            // Continuar sem criar o contato se houver erro
          } else {
            contatoId = contatoCriado.id;
            // Atualizar o body.lancamento com o novo contato_id
            body.lancamento.contato_id = contatoId;
            console.log('Contato criado automaticamente com ID:', contatoId);
          }
        } catch (error) {
          console.error('Erro ao processar contato detectado:', error);
          // Continuar sem criar o contato se houver erro
        }
      }

      // Extrair as parcelas do objeto de lançamento antes de inserir
      let parcelas = body.lancamento.parcelas || [];

      // Remover o campo parcelas do objeto de lançamento para evitar o erro
      const { parcelas: _, ...lancamentoSemParcelas } = body.lancamento;

      // Adicionar o user_id do documento ao lançamento
      const lancamentoComUserId = {
        ...lancamentoSemParcelas,
        user_id: docData.user_id
      };

      // Determinar o número de parcelas
      const numeroParcelas = parcelas.length > 0 ? parcelas.length : 1;

      // Preparar arrays de datas de vencimento e valores para a função RPC
      let datasVencimento = null;
      let valoresParcelas = null;
      let lancamentoId = null;
      let lancamentoError = null;

      if (parcelas.length > 0) {
        // Mapear diretamente os valores e datas das parcelas
        datasVencimento = parcelas.map((p: any) => p.vencimento);
        valoresParcelas = parcelas.map((p: any) => p.valor);

        // Verificar se todas as parcelas têm número
        const parcelasComNumeros = parcelas.map((p: any, index: number) => ({
          ...p,
          numero: p.numero || index + 1
        }));

        parcelas = parcelasComNumeros;

        const rpcParams = {
          p_descricao: lancamentoComUserId.descricao,
          p_valor_total: lancamentoComUserId.valor_total,
          p_data_competencia: lancamentoComUserId.data_competencia,
          p_forma_pagamento: lancamentoComUserId.forma_pagamento,
          p_tipo_lancamento: lancamentoComUserId.tipo_lancamento,
          p_status: lancamentoComUserId.status,
          p_observacoes: lancamentoComUserId.observacoes || '',
          p_obra_id: lancamentoComUserId.obra_id || null,
          p_contato_id: lancamentoComUserId.contato_id || null,
          p_categoria_id: lancamentoComUserId.categoria_id || null,
          p_parcelas: parcelas
        };

        const rpcResponse = await serviceClient.rpc(
          'criar_lancamento_com_parcelas_array',
          rpcParams
        );

        lancamentoId = rpcResponse.data;
        lancamentoError = rpcResponse.error;
      } else {
        // Usar a função RPC simplificada para parcelas automáticas
        const rpcResponse = await serviceClient.rpc(
          'criar_lancamento_com_parcelas_simples',
          {
            p_descricao: lancamentoComUserId.descricao,
            p_valor_total: lancamentoComUserId.valor_total,
            p_data_competencia: lancamentoComUserId.data_competencia,
            p_forma_pagamento: lancamentoComUserId.forma_pagamento,
            p_tipo_lancamento: lancamentoComUserId.tipo_lancamento,
            p_status: lancamentoComUserId.status,
            p_observacoes: lancamentoComUserId.observacoes || '',
            p_obra_id: lancamentoComUserId.obra_id || null,
            p_contato_id: lancamentoComUserId.contato_id || null,
            p_categoria_id: lancamentoComUserId.categoria_id || null,
            p_numero_parcelas: numeroParcelas,
            p_user_id: docData.user_id
          }
        );
        lancamentoId = rpcResponse.data;
        lancamentoError = rpcResponse.error;
      }

      if (lancamentoError) {
        // Tentar método alternativo com insert direto

        // Primeiro, criar o lançamento sem as parcelas usando o cliente de serviço
        const { data: lancamento, error: insertError } = await serviceClient
          .from('lancamentos')
          .insert(lancamentoComUserId)
          .select()
          .single();

        if (insertError) {
          return NextResponse.json({ error: insertError.message }, { status: 500 });
        }

        lancamentoId = lancamento.id;

        // Depois, criar as parcelas associadas ao lançamento usando o cliente de serviço
        if (parcelas.length > 0) {
          try {
            // Preparar as parcelas com o ID do lançamento
            const parcelasComLancamentoId = parcelas.map((parcela: any) => ({
              lancamento_id: lancamento.id,
              numero: parcela.numero,
              valor: parcela.valor,
              vencimento: parcela.vencimento,
              status: 'pendente'
            }));

            // Inserir todas as parcelas de uma vez
            const { data: parcelasInseridas, error: parcelasError } = await serviceClient
              .from('parcelas')
              .insert(parcelasComLancamentoId)
              .select();

            if (parcelasError) {
              // Fallback: inserir uma a uma se a inserção em lote falhar
              const parcelasInseridasIndividualmente = [];

              for (const parcela of parcelasComLancamentoId) {
                const { data: parcelaInserida, error: parcelaError } = await serviceClient
                  .from('parcelas')
                  .insert(parcela)
                  .select();

                if (parcelaError) {
                  throw parcelaError;
                } else {
                  parcelasInseridasIndividualmente.push(parcelaInserida);
                }
              }
            }
          } catch (parcelasError) {
            // Mesmo com erro nas parcelas, continuamos para atualizar o documento
          }
        } else {
          // Criar uma única parcela com o valor total
          const parcelaUnica = {
            lancamento_id: lancamento.id,
            numero: 1,
            valor: lancamentoComUserId.valor_total,
            vencimento: lancamentoComUserId.data_competencia,
            status: 'pendente'
          };

          const { error: parcelaError } = await serviceClient
            .from('parcelas')
            .insert(parcelaUnica);
        }
      }

      // Depois, atualizar o documento usando o cliente de serviço
      const { data, error } = await serviceClient
        .from('documentos')
        .update({
          status: 'aprovado',
          lancamento_id: lancamentoId
        })
        .eq('id', documentoId)
        .select()
        .single();

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data);
    }

    // Se for rejeição
    if (body.status === 'rejeitado' && body.motivo_rejeicao) {

      // Usar o cliente de serviço para contornar RLS
      const serviceClient = createServiceClient();
      if (!serviceClient) {
        return NextResponse.json({ error: 'Erro ao criar cliente de serviço para rejeição' }, { status: 500 });
      }

      const { data, error } = await serviceClient
        .from('documentos')
        .update({
          status: 'rejeitado',
          motivo_rejeicao: body.motivo_rejeicao
        })
        .eq('id', documentoId)
        .select()
        .single();

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data);
    }

    // Caso contrário, atualizar o documento com os dados fornecidos

    // Usar o cliente de serviço para contornar RLS
    const serviceClient = createServiceClient();
    if (!serviceClient) {
      return NextResponse.json({ error: 'Erro ao criar cliente de serviço para atualização' }, { status: 500 });
    }

    // Garantir que obra_id seja tratado corretamente
    // Se for undefined ou string vazia, definir explicitamente como null
    if (body.obra_id === undefined || body.obra_id === '') {
      body.obra_id = null;
    }

    console.log('Atualizando documento com dados:', JSON.stringify(body));
    console.log('Valor de obra_id:', body.obra_id);
    console.log('Tipo de obra_id:', typeof body.obra_id);

    // Verificar se o serviceClient está funcionando corretamente
    try {
      const { data: testData, error: testError } = await serviceClient
        .from('documentos')
        .select('id, obra_id')
        .eq('id', documentoId)
        .single();

      console.log('Teste de leitura antes da atualização:', testData);
      if (testError) {
        console.error('Erro no teste de leitura:', testError);
      }
    } catch (testErr) {
      console.error('Exceção no teste de leitura:', testErr);
    }

    // Tentar atualizar apenas o campo obra_id primeiro
    try {
      const { data: obraUpdateData, error: obraUpdateError } = await serviceClient
        .from('documentos')
        .update({ obra_id: body.obra_id })
        .eq('id', documentoId)
        .select()
        .single();

      console.log('Resultado da atualização apenas de obra_id:', obraUpdateData);
      if (obraUpdateError) {
        console.error('Erro na atualização de obra_id:', obraUpdateError);
      }
    } catch (obraErr) {
      console.error('Exceção na atualização de obra_id:', obraErr);
    }

    // Agora tentar a atualização completa
    const { data, error } = await serviceClient
      .from('documentos')
      .update(body)
      .eq('id', documentoId)
      .select()
      .single();

    if (error) {
      console.error('Erro ao atualizar documento:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log('Documento atualizado com sucesso:', data);

    // Verificar se a atualização foi realmente aplicada
    try {
      const { data: verifyData, error: verifyError } = await serviceClient
        .from('documentos')
        .select('id, obra_id, fornecedor')
        .eq('id', documentoId)
        .single();

      console.log('Verificação após atualização:', verifyData);
      if (verifyError) {
        console.error('Erro na verificação após atualização:', verifyError);
      }
    } catch (verifyErr) {
      console.error('Exceção na verificação após atualização:', verifyErr);
    }

    return NextResponse.json(data);
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Erro ao atualizar documento: ' + error.message },
      { status: 500 }
    );
  }
}

// DELETE /api/documentos/[id] - Excluir um documento
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Acessar params.id de forma assíncrona
    const { id: documentoId } = await params;

    if (!documentoId) {
      return NextResponse.json(
        { error: 'ID do documento não fornecido' },
        { status: 400 }
      );
    }

    // Verificar se devemos forçar o uso do cliente de serviço
    const url = new URL(request.url);
    const forceServiceRole = url.searchParams.get('forceServiceRole') === 'true';

    // Criar cliente de serviço diretamente para garantir permissões
    const serviceClient = createServiceClient();
    if (!serviceClient) {
      return NextResponse.json({ error: 'Erro ao criar cliente de serviço' }, { status: 500 });
    }

    // Verificar se o documento existe
    const { data: documento, error: getError } = await serviceClient
      .from('documentos')
      .select('id, arquivo_path')
      .eq('id', documentoId)
      .single();

    if (getError) {
      if (getError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Documento não encontrado' }, { status: 404 });
      }
      return NextResponse.json({ error: getError.message }, { status: 500 });
    }

    // Tentar excluir o arquivo do storage se existir
    if (documento.arquivo_path) {
      try {
        const { error: storageError } = await serviceClient.storage
          .from('documentos')
          .remove([documento.arquivo_path]);
        // Continuar mesmo com erro no storage
      } catch (storageError) {
        // Continuar mesmo com erro no storage
      }
    }

    // Excluir documento do banco de dados
    const { error } = await serviceClient
      .from('documentos')
      .delete()
      .eq('id', documentoId);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Documento excluído com sucesso' });
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Erro ao excluir documento: ' + error.message },
      { status: 500 }
    );
  }
}
